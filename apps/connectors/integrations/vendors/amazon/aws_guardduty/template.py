from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import AwsGuarddutyV1TemplateVersion


class AwsGuarddutyTemplate(Template):
    id = "aws_guardduty"
    name = "AWS GuardDuty"
    category = Template.Category.CLOUD_SECURITY
    versions = {
        AwsGuarddutyV1TemplateVersion.id: AwsGuarddutyV1TemplateVersion(),
    }
    vendor = Vendors.AMAZON
