import boto3


def paginate(bound_method, response_key, **kwargs):
    response = bound_method(**kwargs)
    yield response[response_key]
    next_token = response["NextToken"] if "NextToken" in response else None
    while next_token:
        response = bound_method(next_token=next_token, **kwargs)
        yield response[response_key]
        next_token = response["NextToken"] if "NextToken" in response else None


class AwsGuarddutyV1Api:
    def __init__(
        self,
        aws_access_key_id=None,
        aws_secret_access_key=None,
        region_name=None,
    ):
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.region_name = region_name
        self._session = None

    @property
    def session(self):
        if not self._session:
            self._session = boto3.Session(
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                region_name=self.region_name,
            )
        return self._session

    def list_detectors(self, next_token=None, **kwargs):
        """
        List all detectors in the account.
        """
        client = self.session.client("guardduty")
        if next_token:
            kwargs["NextToken"] = next_token
        response = client.list_detectors(**kwargs)
        return response

    def list_findings(self, detector_id, updated_at, next_token=None, **kwargs):
        """
        List findings for a given detector ID.
        """
        client = self.session.client("guardduty")
        if next_token:
            kwargs["NextToken"] = next_token
        epoch_millis = int(updated_at.timestamp() * 1000)
        response = client.list_findings(
            DetectorId=detector_id,
            FindingCriteria={"Criterion": {"updatedAt": {"GreaterThan": epoch_millis}}},
            **kwargs,
        )
        return response

    def get_findings(self, detector_id, finding_ids, **kwargs):
        """
        Get a specific finding by its ID.
        """
        client = self.session.client("guardduty")
        response = client.get_findings(
            DetectorId=detector_id, FindingIds=finding_ids, **kwargs
        )
        return response

    def archive_findings(self, detector_id, finding_id):
        client = self.session.client("guardduty")
        return client.archive_findings(DetectorId=detector_id, FindingIds=[finding_id])

    def unarchive_findings(self, detector_id, finding_id):
        client = self.session.client("guardduty")
        return client.unarchive_findings(
            DetectorId=detector_id, FindingIds=[finding_id]
        )

    def get_caller_identity(self):
        sts = self.session.client("sts")
        response = sts.get_caller_identity()
        return response
