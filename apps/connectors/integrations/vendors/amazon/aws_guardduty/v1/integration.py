from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.actions.update_lifecycle_status import (
    AwsGuarddutyV1UpdateLifecycleStatus,
)

from .actions.event_sync import AwsGuardDutyV1EventSync
from .api import AwsGuarddutyV1Api
from .health_check import ConnectionHealthCheck


class AwsGuarddutyV1Integration(Integration):
    api_class = AwsGuarddutyV1Api
    actions = (AwsGuardDutyV1EventSync, AwsGuarddutyV1UpdateLifecycleStatus)
    critical_health_checks = (ConnectionHealthCheck,)
