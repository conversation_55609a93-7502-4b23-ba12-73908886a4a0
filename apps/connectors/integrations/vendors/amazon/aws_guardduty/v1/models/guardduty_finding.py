# generated by datamodel-codegen:
#   filename:  guardduty_finding_uber.json
#   timestamp: 2025-05-08T01:54:36+00:00

from __future__ import annotations

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class IamInstanceProfile(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    arn: Optional[str] = Field(None, alias="Arn")
    id: Optional[str] = Field(None, alias="Id")


class PrivateIpAddress(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    private_dns_name: Optional[str] = Field(None, alias="PrivateDnsName")
    private_ip_address: Optional[str] = Field(None, alias="PrivateIpAddress")


class SecurityGroup(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    group_id: Optional[str] = Field(None, alias="GroupId")
    group_name: Optional[str] = Field(None, alias="GroupName")


class NetworkInterface(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    ipv6_addresses: Optional[List] = Field(None, alias="Ipv6Addresses")
    network_interface_id: Optional[str] = Field(None, alias="NetworkInterfaceId")
    private_dns_name: Optional[str] = Field(None, alias="PrivateDnsName")
    private_ip_address: Optional[str] = Field(None, alias="PrivateIpAddress")
    private_ip_addresses: Optional[List[PrivateIpAddress]] = Field(
        None, alias="PrivateIpAddresses"
    )
    public_dns_name: Optional[str] = Field(None, alias="PublicDnsName")
    public_ip: Optional[str] = Field(None, alias="PublicIp")
    security_groups: Optional[List[SecurityGroup]] = Field(None, alias="SecurityGroups")
    subnet_id: Optional[str] = Field(None, alias="SubnetId")
    vpc_id: Optional[str] = Field(None, alias="VpcId")


class ProductCode(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    code: Optional[str] = Field(None, alias="Code")
    product_type: Optional[str] = Field(None, alias="ProductType")


class Tag(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    key: Optional[str] = Field(None, alias="Key")
    value: Optional[str] = Field(None, alias="Value")


class InstanceDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    availability_zone: Optional[str] = Field(None, alias="AvailabilityZone")
    iam_instance_profile: Optional[IamInstanceProfile] = Field(
        None, alias="IamInstanceProfile"
    )
    image_description: Optional[str] = Field(None, alias="ImageDescription")
    image_id: Optional[str] = Field(None, alias="ImageId")
    instance_id: Optional[str] = Field(None, alias="InstanceId")
    instance_state: Optional[str] = Field(None, alias="InstanceState")
    instance_type: Optional[str] = Field(None, alias="InstanceType")
    outpost_arn: Optional[str] = Field(None, alias="OutpostArn")
    launch_time: Optional[str] = Field(None, alias="LaunchTime")
    network_interfaces: Optional[List[NetworkInterface]] = Field(
        None, alias="NetworkInterfaces"
    )
    product_codes: Optional[List[ProductCode]] = Field(None, alias="ProductCodes")
    tags: Optional[List[Tag]] = Field(None, alias="Tags")


class HostPath(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    path: Optional[str] = Field(None, alias="Path")


class Volume(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    name: Optional[str] = Field(None, alias="Name")
    host_path: Optional[HostPath] = Field(None, alias="HostPath")


class VolumeMount(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    name: Optional[str] = Field(None, alias="Name")
    mount_path: Optional[str] = Field(None, alias="MountPath")


class SecurityContext(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    privileged: Optional[bool] = Field(None, alias="Privileged")


class Container(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    container_runtime: Optional[str] = Field(None, alias="ContainerRuntime")
    id: Optional[str] = Field(None, alias="Id")
    name: Optional[str] = Field(None, alias="Name")
    image: Optional[str] = Field(None, alias="Image")
    image_prefix: Optional[str] = Field(None, alias="ImagePrefix")
    volume_mounts: Optional[List[VolumeMount]] = Field(None, alias="VolumeMounts")
    security_context: Optional[SecurityContext] = Field(None, alias="SecurityContext")


class TaskDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    arn: Optional[str] = Field(None, alias="Arn")
    definition_arn: Optional[str] = Field(None, alias="DefinitionArn")
    version: Optional[str] = Field(None, alias="Version")
    task_created_at: Optional[str] = Field(None, alias="TaskCreatedAt")
    started_at: Optional[str] = Field(None, alias="StartedAt")
    started_by: Optional[str] = Field(None, alias="StartedBy")
    volumes: Optional[List[Volume]] = Field(None, alias="Volumes")
    containers: Optional[List[Container]] = Field(None, alias="Containers")
    group: Optional[str] = Field(None, alias="Group")
    launch_type: Optional[str] = Field(None, alias="LaunchType")


class EcsClusterDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    name: Optional[str] = Field(None, alias="Name")
    arn: Optional[str] = Field(None, alias="Arn")
    status: Optional[str] = Field(None, alias="Status")
    active_services_count: Optional[int] = Field(None, alias="ActiveServicesCount")
    registered_container_instances_count: Optional[int] = Field(
        None, alias="RegisteredContainerInstancesCount"
    )
    running_tasks_count: Optional[int] = Field(None, alias="RunningTasksCount")
    tags: Optional[List[Tag]] = Field(None, alias="Tags")
    task_details: Optional[TaskDetails] = Field(None, alias="TaskDetails")


class AccessKeyDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    access_key_id: Optional[str] = Field(None, alias="AccessKeyId")
    principal_id: Optional[str] = Field(None, alias="PrincipalId")
    user_name: Optional[str] = Field(None, alias="UserName")
    user_type: Optional[str] = Field(None, alias="UserType")


class Resource(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    instance_details: Optional[InstanceDetails] = Field(None, alias="InstanceDetails")
    resource_type: Optional[str] = Field(None, alias="ResourceType")
    ecs_cluster_details: Optional[EcsClusterDetails] = Field(
        None, alias="EcsClusterDetails"
    )
    access_key_details: Optional[AccessKeyDetails] = Field(
        None, alias="AccessKeyDetails"
    )


class LocalPortDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    port: Optional[int] = Field(None, alias="Port")
    port_name: Optional[str] = Field(None, alias="PortName")


class LocalIpDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    ip_address_v4: Optional[str] = Field(None, alias="IpAddressV4")
    ip_address_v6: Optional[str] = Field(None, alias="IpAddressV6")


class City(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    city_name: Optional[str] = Field(None, alias="CityName")


class Country(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    country_name: Optional[str] = Field(None, alias="CountryName")


class GeoLocation(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    lat: Optional[float] = Field(None, alias="Lat")
    lon: Optional[float] = Field(None, alias="Lon")


class Organization(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    asn: Optional[str] = Field(None, alias="Asn")
    asn_org: Optional[str] = Field(None, alias="AsnOrg")
    isp: Optional[str] = Field(None, alias="Isp")
    org: Optional[str] = Field(None, alias="Org")


class RemoteIpDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    city: Optional[City] = Field(None, alias="City")
    country: Optional[Country] = Field(None, alias="Country")
    geo_location: Optional[GeoLocation] = Field(None, alias="GeoLocation")
    ip_address_v4: Optional[str] = Field(None, alias="IpAddressV4")
    ip_address_v6: Optional[str] = Field(None, alias="IpAddressV6")
    organization: Optional[Organization] = Field(None, alias="Organization")


RemotePortDetails = LocalPortDetails


class NetworkConnectionAction(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    blocked: Optional[bool] = Field(None, alias="Blocked")
    connection_direction: Optional[str] = Field(None, alias="ConnectionDirection")
    local_port_details: Optional[LocalPortDetails] = Field(
        None, alias="LocalPortDetails"
    )
    protocol: Optional[str] = Field(None, alias="Protocol")
    local_ip_details: Optional[LocalIpDetails] = Field(None, alias="LocalIpDetails")
    remote_ip_details: Optional[RemoteIpDetails] = Field(None, alias="RemoteIpDetails")
    remote_port_details: Optional[RemotePortDetails] = Field(
        None, alias="RemotePortDetails"
    )


class AwsApiCallAction(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    api: Optional[str] = Field(None, alias="Api")
    caller_type: Optional[str] = Field(None, alias="CallerType")
    error_code: Optional[str] = Field(None, alias="ErrorCode")
    remote_ip_details: Optional[RemoteIpDetails] = Field(None, alias="RemoteIpDetails")
    service_name: Optional[str] = Field(None, alias="ServiceName")
    affected_resources: Optional[Dict[str, Any]] = Field(
        None, alias="AffectedResources"
    )


class Action(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    action_type: Optional[str] = Field(None, alias="ActionType")
    network_connection_action: Optional[NetworkConnectionAction] = Field(
        None, alias="NetworkConnectionAction"
    )
    aws_api_call_action: Optional[AwsApiCallAction] = Field(
        None, alias="AwsApiCallAction"
    )


class ThreatIntelligenceDetail(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    threat_list_name: Optional[str] = Field(None, alias="ThreatListName")
    threat_names: Optional[List[str]] = Field(None, alias="ThreatNames")


class Evidence(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    threat_intelligence_details: Optional[List[ThreatIntelligenceDetail]] = Field(
        None, alias="ThreatIntelligenceDetails"
    )


class AdditionalInfo(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    value: Optional[str] = Field(None, alias="Value")
    type: Optional[str] = Field(None, alias="Type")


class LineageItem(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    pid: Optional[int] = Field(None, alias="Pid")
    uuid: Optional[str] = Field(None, alias="Uuid")
    executable_path: Optional[str] = Field(None, alias="ExecutablePath")
    euid: Optional[int] = Field(None, alias="Euid")
    parent_uuid: Optional[str] = Field(None, alias="ParentUuid")


class Process(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    name: Optional[str] = Field(None, alias="Name")
    executable_path: Optional[str] = Field(None, alias="ExecutablePath")
    executable_sha256: Optional[str] = Field(None, alias="ExecutableSha256")
    pwd: Optional[str] = Field(None, alias="Pwd")
    pid: Optional[int] = Field(None, alias="Pid")
    start_time: Optional[str] = Field(None, alias="StartTime")
    uuid: Optional[str] = Field(None, alias="Uuid")
    parent_uuid: Optional[str] = Field(None, alias="ParentUuid")
    user: Optional[str] = Field(None, alias="User")
    user_id: Optional[int] = Field(None, alias="UserId")
    euid: Optional[int] = Field(None, alias="Euid")
    lineage: Optional[List[LineageItem]] = Field(None, alias="Lineage")


class RuntimeDetails(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    process: Optional[Process] = Field(None, alias="Process")


class Service(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    action: Optional[Action] = Field(None, alias="Action")
    evidence: Optional[Evidence] = Field(None, alias="Evidence")
    archived: Optional[bool] = Field(None, alias="Archived")
    count: Optional[int] = Field(None, alias="Count")
    detector_id: Optional[str] = Field(None, alias="DetectorId")
    event_first_seen: Optional[str] = Field(None, alias="EventFirstSeen")
    event_last_seen: Optional[str] = Field(None, alias="EventLastSeen")
    resource_role: Optional[str] = Field(None, alias="ResourceRole")
    service_name: Optional[str] = Field(None, alias="ServiceName")
    additional_info: Optional[AdditionalInfo] = Field(None, alias="AdditionalInfo")
    feature_name: Optional[str] = Field(None, alias="FeatureName")
    runtime_details: Optional[RuntimeDetails] = Field(None, alias="RuntimeDetails")


class GuarddutyFinding(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    account_id: Optional[str] = Field(None, alias="AccountId")
    arn: Optional[str] = Field(None, alias="Arn")
    created_at: Optional[str] = Field(None, alias="CreatedAt")
    description: Optional[str] = Field(None, alias="Description")
    id: Optional[str] = Field(None, alias="Id")
    partition: Optional[str] = Field(None, alias="Partition")
    region: Optional[str] = Field(None, alias="Region")
    resource: Optional[Resource] = Field(None, alias="Resource")
    schema_version: Optional[str] = Field(None, alias="SchemaVersion")
    service: Optional[Service] = Field(None, alias="Service")
    severity: Optional[float] = Field(None, alias="Severity")
    title: Optional[str] = Field(None, alias="Title")
    type: Optional[str] = Field(None, alias="Type")
    updated_at: Optional[str] = Field(None, alias="UpdatedAt")
