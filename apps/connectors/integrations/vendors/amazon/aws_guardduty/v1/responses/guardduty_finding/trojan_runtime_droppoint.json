{"AccountId": "************", "Arn": "arn:aws:guardduty:us-west-2:************:detector/12b3658826b8840b8913b6e6d3db920d/finding/1a73249898f54e7a87518cdcda27e1cd", "CreatedAt": "2025-04-08T17:31:31.136Z", "Description": "The process GeneratedFindingProcessName from EC2 instance i-******** is communicating with a remote host ************ that is known to hold credentials and other stolen data captured by malware.", "Id": "1a73249898f54e7a87518cdcda27e1cd", "Partition": "aws", "Region": "us-west-2", "Resource": {"InstanceDetails": {"AvailabilityZone": "generated-az-1a", "IamInstanceProfile": {"Arn": "arn:aws:iam::************:instance-profile/generated", "Id": "GeneratedFindingInstanceProfileId"}, "ImageDescription": "GeneratedFindingInstanceImageDescription", "ImageId": "ami-********", "InstanceId": "i-********", "InstanceState": "running", "InstanceType": "m3.xlarge", "OutpostArn": "arn:aws:outposts:us-east-1:************:outpost/op-1234567890abcdef0", "LaunchTime": "2016-08-02T02:05:06.000Z", "NetworkInterfaces": [{"Ipv6Addresses": [], "NetworkInterfaceId": "eni-abcdef12", "PrivateDnsName": "GeneratedFindingPrivateDnsName", "PrivateIpAddress": "********", "PrivateIpAddresses": [{"PrivateDnsName": "GeneratedFindingPrivateName", "PrivateIpAddress": "********"}], "PublicDnsName": "GeneratedFindingPublicDNSName", "PublicIp": "************", "SecurityGroups": [{"GroupId": "GeneratedFindingSecurityId", "GroupName": "GeneratedFindingSecurityGroupName"}], "SubnetId": "GeneratedFindingSubnetId", "VpcId": "vpc-generatedvpcid"}], "ProductCodes": [{"Code": "GeneratedFindingProductCodeId", "ProductType": "marketplace"}], "Tags": [{"Key": "GeneratedFindingInstanceTag1", "Value": "GeneratedFindingInstanceValue1"}, {"Key": "GeneratedFindingInstanceTag2", "Value": "GeneratedFindingInstanceTagValue2"}, {"Key": "GeneratedFindingInstanceTag3", "Value": "GeneratedFindingInstanceTagValue3"}, {"Key": "GeneratedFindingInstanceTag4", "Value": "GeneratedFindingInstanceTagValue4"}, {"Key": "GeneratedFindingInstanceTag5", "Value": "GeneratedFindingInstanceTagValue5"}, {"Key": "GeneratedFindingInstanceTag6", "Value": "GeneratedFindingInstanceTagValue6"}, {"Key": "GeneratedFindingInstanceTag7", "Value": "GeneratedFindingInstanceTagValue7"}, {"Key": "GeneratedFindingInstanceTag8", "Value": "GeneratedFindingInstanceTagValue8"}, {"Key": "GeneratedFindingInstanceTag9", "Value": "GeneratedFindingInstanceTagValue9"}]}, "ResourceType": "ECSCluster", "EcsClusterDetails": {"Name": "GeneratedFindingECSClusterName", "Arn": "arn:aws:ecs:us-east-1:************:cluster/sample-cluster", "Status": "ACTIVE", "ActiveServicesCount": 1, "RegisteredContainerInstancesCount": 1, "RunningTasksCount": 1, "Tags": [{"Key": "GeneratedFindingECSClusterTag1", "Value": "GeneratedFindingECSClusterTagValue1"}, {"Key": "GeneratedFindingECSClusterTag2", "Value": "GeneratedFindingECSClusterTagValue2"}, {"Key": "GeneratedFindingECSClusterTag3", "Value": "GeneratedFindingECSClusterTagValue3"}], "TaskDetails": {"Arn": "arn:aws:ecs:us-east-1:************:task/sample-cluster/12345678-1234-1234-1234-************", "DefinitionArn": "arn:aws:ecs:us-east-1:************:task-definition/sample-cluster/76f1f1asdf", "Version": "1", "TaskCreatedAt": "2021-11-11T10:15:55.218Z", "StartedAt": "2021-11-11T10:15:55.218Z", "StartedBy": "GeneratedFindingECSServiceId", "Volumes": [{"Name": "GeneratedFindingECSTaskVolumeName", "HostPath": {"Path": "GeneratedFindingECSTaskHostPath"}}], "Containers": [{"ContainerRuntime": "GeneratedFindingECSRuntime", "Id": "GeneratedFindingECSContainerId", "Name": "GeneratedFindingECSContainerName", "Image": "GeneratedFindingECSContainerImage", "ImagePrefix": "GeneratedFindingECSContainerImagePrefix", "VolumeMounts": [{"Name": "GeneratedFindingVolumeMountName", "MountPath": "GeneratedFindingVolumeMountMountPath"}], "SecurityContext": {"Privileged": false}}], "Group": "GeneratedFindingECSContainerGroup", "LaunchType": "GeneratedFindingECSLaunchType"}}}, "SchemaVersion": "2.0", "Service": {"Action": {"ActionType": "NETWORK_CONNECTION", "NetworkConnectionAction": {"Blocked": false, "ConnectionDirection": "OUTBOUND", "LocalPortDetails": {"Port": 2000, "PortName": "Unknown"}, "Protocol": "TCP", "LocalIpDetails": {"IpAddressV4": "*********", "IpAddressV6": "1234:5678:90ab:cdef:1234:5678:90ab:cde1"}, "RemoteIpDetails": {"City": {"CityName": "GeneratedFindingCityName"}, "Country": {"CountryName": "GeneratedFindingCountryName"}, "GeoLocation": {"Lat": 0.0, "Lon": 0.0}, "IpAddressV4": "************", "IpAddressV6": "1234:5678:90ab:cdef:1234:5678:90ab:cde0", "Organization": {"Asn": "-1", "AsnOrg": "GeneratedFindingASNOrg", "Isp": "GeneratedFindingISP", "Org": "GeneratedFindingORG"}}, "RemotePortDetails": {"Port": 25, "PortName": "SMTP"}}}, "Evidence": {"ThreatIntelligenceDetails": [{"ThreatListName": "GeneratedFindingThreatListName1", "ThreatNames": ["GeneratedFindingThreatName1", "GeneratedFindingThreatName2", "GeneratedFindingThreatName3", "GeneratedFindingThreatName4"]}, {"ThreatListName": "GeneratedFindingThreatListName2", "ThreatNames": ["GeneratedFindingThreatName1", "GeneratedFindingThreatName2", "GeneratedFindingThreatName3", "GeneratedFindingThreatName4"]}, {"ThreatListName": "GeneratedFindingThreatListName3", "ThreatNames": ["GeneratedFindingThreatName1", "GeneratedFindingThreatName2", "GeneratedFindingThreatName3", "GeneratedFindingThreatName4"]}, {"ThreatListName": "GeneratedFindingThreatListName4", "ThreatNames": ["GeneratedFindingThreatName1", "GeneratedFindingThreatName2", "GeneratedFindingThreatName3", "GeneratedFindingThreatName4"]}]}, "Archived": false, "Count": 2, "DetectorId": "12b3658826b8840b8913b6e6d3db920d", "EventFirstSeen": "2025-04-08T17:31:31.000Z", "EventLastSeen": "2025-04-09T12:57:01.000Z", "ResourceRole": "TARGET", "ServiceName": "<PERSON><PERSON><PERSON>", "AdditionalInfo": {"Value": "{\"threatListName\":\"GeneratedFindingThreatListName\",\"sample\":true,\"agentDetails\":{\"agentVersion\":\"1\",\"agentId\":\"GeneratedFindingAgentId\"}}", "Type": "default"}, "FeatureName": "RuntimeMonitoring", "RuntimeDetails": {"Process": {"Name": "GeneratedFindingProcessName", "ExecutablePath": "GeneratedFindingPath", "ExecutableSha256": "GeneratedFindingHash", "Pwd": "GeneratedFindingPath", "Pid": 1234, "StartTime": "2021-11-15T21:38:14Z", "Uuid": "GeneratedFindingUUId", "ParentUuid": "GeneratedFindingUUId", "User": "ec2-user", "UserId": 1000, "Euid": 1000, "Lineage": [{"Pid": 1233, "Uuid": "GeneratedFindingUUId1", "ExecutablePath": "GeneratedFindingPath1", "Euid": 1000, "ParentUuid": "GeneratedFindingParentUUId1"}, {"Pid": 1234, "Uuid": "GeneratedFindingUUId2", "ExecutablePath": "GeneratedFindingPath2", "Euid": 1001, "ParentUuid": "GeneratedFindingParentUUId2"}, {"Pid": 1235, "Uuid": "GeneratedFindingUUId3", "ExecutablePath": "GeneratedFindingPath3", "Euid": 1002, "ParentUuid": "GeneratedFindingParentUUId3"}, {"Pid": 1236, "Uuid": "GeneratedFindingUUId4", "ExecutablePath": "GeneratedFindingPath4", "Euid": 1003, "ParentUuid": "GeneratedFindingParentUUId4"}]}}}, "Severity": 5.0, "Title": "The EC2 instance i-******** is communicating with a Drop Point.", "Type": "Trojan:Runtime/DropPoint", "UpdatedAt": "2025-04-09T12:57:01.825Z"}