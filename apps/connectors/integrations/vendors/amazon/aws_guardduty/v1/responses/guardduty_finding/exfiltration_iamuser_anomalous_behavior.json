{"AccountId": "************", "Arn": "arn:aws:guardduty:us-west-2:************:detector/12b3658826b8840b8913b6e6d3db920d/finding/1c371eca2caf45e888f9be52f89bc4f0", "CreatedAt": "2025-04-08T17:31:31.050Z", "Description": "APIs commonly used in Exfiltration tactics were invoked by user GeneratedFindingUserType : GeneratedFindingUserName under unusual circumstances. Such activity is not typically seen from this user.", "Id": "1c371eca2caf45e888f9be52f89bc4f0", "Partition": "aws", "Region": "us-west-2", "Resource": {"AccessKeyDetails": {"AccessKeyId": "GeneratedFindingAccessKeyId", "PrincipalId": "GeneratedFindingPrincipalId", "UserName": "GeneratedFindingUserName", "UserType": "GeneratedFindingUserType"}, "InstanceDetails": {"AvailabilityZone": "generated-az-1a", "IamInstanceProfile": {"Arn": "arn:aws:iam::************:instance-profile/generated", "Id": "GeneratedFindingInstanceProfileId"}, "ImageDescription": "GeneratedFindingInstaceImageDescription", "ImageId": "ami-********", "InstanceId": "i-********", "InstanceState": "running", "InstanceType": "m3.xlarge", "OutpostArn": "arn:aws:outposts:us-west-2:123456789000:outpost/op-0fbc006e9abbc73c3", "LaunchTime": "2016-08-02T02:05:06.000Z", "NetworkInterfaces": [{"Ipv6Addresses": [], "NetworkInterfaceId": "eni-abcdef00", "PrivateDnsName": "GeneratedFindingPrivateDnsName1", "PrivateIpAddress": "********", "PrivateIpAddresses": [{"PrivateDnsName": "GeneratedFindingPrivateName1", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName2", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName3", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName4", "PrivateIpAddress": "********"}], "PublicDnsName": "GeneratedFindingPublicDNSName1", "PublicIp": "************", "SecurityGroups": [{"GroupId": "GeneratedFindingSecurityId1", "GroupName": "GeneratedFindingSecurityGroupName1"}, {"GroupId": "GeneratedFindingSecurityId2", "GroupName": "GeneratedFindingSecurityGroupName2"}, {"GroupId": "GeneratedFindingSecurityId3", "GroupName": "GeneratedFindingSecurityGroupName3"}, {"GroupId": "GeneratedFindingSecurityId4", "GroupName": "GeneratedFindingSecurityGroupName4"}], "SubnetId": "GeneratedFindingSubnetId1", "VpcId": "vpc-generatedvpcid1"}, {"Ipv6Addresses": [], "NetworkInterfaceId": "eni-abcdef01", "PrivateDnsName": "GeneratedFindingPrivateDnsName2", "PrivateIpAddress": "********", "PrivateIpAddresses": [{"PrivateDnsName": "GeneratedFindingPrivateName1", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName2", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName3", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName4", "PrivateIpAddress": "********"}], "PublicDnsName": "GeneratedFindingPublicDNSName2", "PublicIp": "************", "SecurityGroups": [{"GroupId": "GeneratedFindingSecurityId1", "GroupName": "GeneratedFindingSecurityGroupName1"}, {"GroupId": "GeneratedFindingSecurityId2", "GroupName": "GeneratedFindingSecurityGroupName2"}, {"GroupId": "GeneratedFindingSecurityId3", "GroupName": "GeneratedFindingSecurityGroupName3"}, {"GroupId": "GeneratedFindingSecurityId4", "GroupName": "GeneratedFindingSecurityGroupName4"}], "SubnetId": "GeneratedFindingSubnetId2", "VpcId": "vpc-generatedvpcid2"}, {"Ipv6Addresses": [], "NetworkInterfaceId": "eni-abcdef02", "PrivateDnsName": "GeneratedFindingPrivateDnsName3", "PrivateIpAddress": "********", "PrivateIpAddresses": [{"PrivateDnsName": "GeneratedFindingPrivateName1", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName2", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName3", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName4", "PrivateIpAddress": "********"}], "PublicDnsName": "GeneratedFindingPublicDNSName3", "PublicIp": "************", "SecurityGroups": [{"GroupId": "GeneratedFindingSecurityId1", "GroupName": "GeneratedFindingSecurityGroupName1"}, {"GroupId": "GeneratedFindingSecurityId2", "GroupName": "GeneratedFindingSecurityGroupName2"}, {"GroupId": "GeneratedFindingSecurityId3", "GroupName": "GeneratedFindingSecurityGroupName3"}, {"GroupId": "GeneratedFindingSecurityId4", "GroupName": "GeneratedFindingSecurityGroupName4"}], "SubnetId": "GeneratedFindingSubnetId3", "VpcId": "vpc-generatedvpcid3"}, {"Ipv6Addresses": [], "NetworkInterfaceId": "eni-abcdef03", "PrivateDnsName": "GeneratedFindingPrivateDnsName4", "PrivateIpAddress": "********", "PrivateIpAddresses": [{"PrivateDnsName": "GeneratedFindingPrivateName1", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName2", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName3", "PrivateIpAddress": "********"}, {"PrivateDnsName": "GeneratedFindingPrivateName4", "PrivateIpAddress": "********"}], "PublicDnsName": "GeneratedFindingPublicDNSName4", "PublicIp": "************", "SecurityGroups": [{"GroupId": "GeneratedFindingSecurityId1", "GroupName": "GeneratedFindingSecurityGroupName1"}, {"GroupId": "GeneratedFindingSecurityId2", "GroupName": "GeneratedFindingSecurityGroupName2"}, {"GroupId": "GeneratedFindingSecurityId3", "GroupName": "GeneratedFindingSecurityGroupName3"}, {"GroupId": "GeneratedFindingSecurityId4", "GroupName": "GeneratedFindingSecurityGroupName4"}], "SubnetId": "GeneratedFindingSubnetId4", "VpcId": "vpc-generatedvpcid4"}], "ProductCodes": [{"Code": "GeneratedFindingProductCodeId1", "ProductType": "marketplace"}, {"Code": "GeneratedFindingProductCodeId2", "ProductType": "marketplace"}, {"Code": "GeneratedFindingProductCodeId3", "ProductType": "marketplace"}, {"Code": "GeneratedFindingProductCodeId4", "ProductType": "marketplace"}, {"Code": "GeneratedFindingProductCodeId5", "ProductType": "marketplace"}], "Tags": [{"Key": "GeneratedFindingInstaceTag1", "Value": "GeneratedFindingInstaceValue1"}, {"Key": "GeneratedFindingInstaceTag2", "Value": "GeneratedFindingInstaceTagValue2"}, {"Key": "GeneratedFindingInstaceTag3", "Value": "GeneratedFindingInstaceTagValue3"}, {"Key": "GeneratedFindingInstaceTag4", "Value": "GeneratedFindingInstaceTagValue4"}, {"Key": "GeneratedFindingInstaceTag5", "Value": "GeneratedFindingInstaceTagValue5"}, {"Key": "GeneratedFindingInstaceTag6", "Value": "GeneratedFindingInstaceTagValue6"}, {"Key": "GeneratedFindingInstaceTag7", "Value": "GeneratedFindingInstaceTagValue7"}, {"Key": "GeneratedFindingInstaceTag8", "Value": "GeneratedFindingInstaceTagValue8"}, {"Key": "GeneratedFindingInstaceTag9", "Value": "GeneratedFindingInstaceTagValue9"}]}, "ResourceType": "AccessKey"}, "SchemaVersion": "2.0", "Service": {"Action": {"ActionType": "AWS_API_CALL", "AwsApiCallAction": {"Api": "GeneratedFindingAPIName", "CallerType": "Remote IP", "ErrorCode": "AccessDenied", "RemoteIpDetails": {"City": {"CityName": "GeneratedFindingCityName"}, "Country": {"CountryName": "GeneratedFindingCountryName"}, "GeoLocation": {"Lat": 0.0, "Lon": 0.0}, "IpAddressV4": "************", "IpAddressV6": "1234:5678:90ab:cdef:1234:5678:90ab:cde0", "Organization": {"Asn": "-1", "AsnOrg": "GeneratedFindingASNOrg", "Isp": "GeneratedFindingISP", "Org": "GeneratedFindingOrg"}}, "ServiceName": "GeneratedFindingAPIServiceName", "AffectedResources": {}}}, "Archived": true, "Count": 2, "DetectorId": "12b3658826b8840b8913b6e6d3db920d", "EventFirstSeen": "2025-04-08T17:31:31.000Z", "EventLastSeen": "2025-04-09T12:57:01.000Z", "ResourceRole": "TARGET", "ServiceName": "<PERSON><PERSON><PERSON>", "AdditionalInfo": {"Value": "{\"userAgent\":{\"fullUserAgent\":\"GeneratedFindingFullUserAgent\",\"userAgentCategory\":\"GeneratedFindingUserAgentCategory\"},\"anomalies\":{\"anomalousAPIs\":\"GeneratedFindingAPIServiceName:[GeneratedFindingAPIName:AccessDenied , GeneratedFindingAPINameTwo:AccessDenied] , GeneratedFindingAPIServiceNameThree:[GeneratedFindingAPINameThree:success] , GeneratedFindingAPIServiceNameFour:[GeneratedFindingAPINameFour:success]\"},\"profiledBehavior\":{\"rareProfiledAPIsAccountProfiling\":\"GeneratedFindingAPINameTwo , GeneratedFindingAPINameThree\",\"infrequentProfiledAPIsAccountProfiling\":\"GeneratedFindingAPINameFour\",\"frequentProfiledAPIsAccountProfiling\":\"GeneratedFindingAPINameFive , GeneratedFindingAPINameSix\",\"rareProfiledAPIsUserIdentityProfiling\":\"GeneratedFindingAPINameTwo\",\"infrequentProfiledAPIsUserIdentityProfiling\":\"GeneratedFindingAPINameSix\",\"frequentProfiledAPIsUserIdentityProfiling\":\"GeneratedFindingAPINameFive\",\"rareProfiledUserTypesAccountProfiling\":\"GeneratedFindingUserType\",\"infrequentProfiledUserTypesAccountProfiling\":\"\",\"frequentProfiledUserTypesAccountProfiling\":\"ASSUMED_ROLE\",\"rareProfiledUserNamesAccountProfiling\":\"GeneratedFindingUserName , GeneratedFindingUserNameTwo\",\"infrequentProfiledUserNamesAccountProfiling\":\"\",\"frequentProfiledUserNamesAccountProfiling\":\"GeneratedFindingUserNameTwoThree\",\"rareProfiledASNsAccountProfiling\":\"\",\"infrequentProfiledASNsAccountProfiling\":\"\",\"frequentProfiledASNsAccountProfiling\":\"asnNumber: GeneratedFindingASNOne asnOrg: GeneratedFindingASNOrgOne\",\"rareProfiledASNsUserIdentityProfiling\":\"asnNumber: GeneratedFindingASNOne asnOrg: GeneratedFindingASNOrgOne\",\"infrequentProfiledASNsUserIdentityProfiling\":\"\",\"frequentProfiledASNsUserIdentityProfiling\":\"\",\"rareProfiledUserAgentsAccountProfiling\":\"GeneratedFindingUserAgentOne , GeneratedFindingUserAgentTwo , GeneratedFindingUserAgentThree\",\"infrequentProfiledUserAgentsAccountProfiling\":\"\",\"frequentProfiledUserAgentsAccountProfiling\":\"AWS Service , AWS Internal\",\"rareProfiledUserAgentsUserIdentityProfiling\":\"GeneratedFindingUserAgentOne\",\"infrequentProfiledUserAgentsUserIdentityProfiling\":\"\",\"frequentProfiledUserAgentsUserIdentityProfiling\":\"\"},\"unusualBehavior\":{\"unusualAPIsAccountProfiling\":\"GeneratedFindingAPIName\",\"unusualAPIsUserIdentityProfiling\":\"GeneratedFindingAPIName\",\"unusualUserTypesAccountProfiling\":\"\",\"unusualUserNamesAccountProfiling\":\"\",\"unusualASNsAccountProfiling\":\"asnNumber: -1 asnOrg: GeneratedFindingASNOrg\",\"unusualASNsUserIdentityProfiling\":\"asnNumber: -1 asnOrg: GeneratedFindingASNOrg\",\"unusualUserAgentsAccountProfiling\":\"GeneratedFindingUserAgentCategory\",\"unusualUserAgentsUserIdentityProfiling\":\"GeneratedFindingUserAgentCategory\",\"isUnusualUserIdentity\":\"false\"},\"sample\":true}", "Type": "default"}}, "Severity": 8.0, "Title": "The user GeneratedFindingUserType : GeneratedFindingUserName is anomalously invoking APIs commonly used in Exfiltration tactics.", "Type": "Exfiltration:IAMUser/AnomalousBehavior", "UpdatedAt": "2025-04-09T12:57:01.789Z"}