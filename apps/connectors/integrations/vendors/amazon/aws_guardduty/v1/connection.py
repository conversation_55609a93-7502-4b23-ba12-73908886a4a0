from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class AwsGuarddutyV1Config(TemplateVersionConfig):
    aws_access_key_id: str = Field(
        title="AWS Access Key ID",
        description="Your AWS access key ID.",
    )
    aws_secret_access_key: EncryptedStr = Field(
        title="AWS Secret Access Key",
        description="Your AWS secret access key.",
    )
    region_name: str = Field(
        title="AWS Region",
        description="The AWS region where your GuardDuty is set up.",
    )


class AwsGuarddutyV1Connection(ConnectionTemplate):
    id = "aws_guardduty"
    name = "AWS GuardDuty"
    config_model = AwsGuarddutyV1Config
