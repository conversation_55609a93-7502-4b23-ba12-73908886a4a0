from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.api import (
    AwsGuarddutyV1Api,
)
from apps.connectors.integrations.vendors.amazon.aws_guardduty.v1.health_check import (
    ReadAllFindings,
)


def map_corr_incident_status(status: CorrIncidentStatus) -> str:
    return {
        CorrIncidentStatus.NEW: "Archived False",
        CorrIncidentStatus.ASSIGNED: "Archived False",
        CorrIncidentStatus.REVIEWING: "Archived False",
        CorrIncidentStatus.MITIGATED: "Archived False",
        CorrIncidentStatus.CLOSED: "Archived True",
    }[status]


class AwsGuarddutyV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: AwsGuarddutyV1Api = self.integration.get_api()

        update_data = {
            "status": map_corr_incident_status(args.status),
        }
        [detector_id, findings_id] = args.vendor_sync_id.split(";")
        if detector_id and findings_id:
            if update_data["status"] == "Archived True":
                api.archive_findings(detector_id, findings_id)
            else:
                api.unarchive_findings(detector_id, findings_id)

        return UpdateLifecycleStatusResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllFindings]
