from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import InfobloxDdiV1TemplateVersion


class InfobloxDdiTemplate(Template):
    id = "infoblox_ddi"
    name = "Infoblox DDI"
    category = Template.Category.ASSET_SOURCE
    versions = {
        InfobloxDdiV1TemplateVersion.id: InfobloxDdiV1TemplateVersion(),
    }
    vendor = Vendors.INFOBLOX
