from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["results"]
    offset = response.get("page", {}).get("offset", None)
    while offset:
        response = bound_method(**kwargs, offset=offset)
        if ("results" in response) and len(response["results"]) > 0:
            yield response["results"]
            offset = response.get("page", {}).get("offset", None)
        else:
            break


class InfobloxDdiV1Api(ApiBase):

    """
    Infoblox DDI API
    https://csp.infoblox.com/apidoc
    """

    def __init__(self, api_key=None, **kwargs):
        self.api_key = api_key
        url = "https://csp.infoblox.com/api/infra/v1/"
        static_headers = {"Authorization": f"Token {api_key}"}
        super().__init__(base_url=url, static_headers=static_headers)

    # https://csp.infoblox.com/apidoc
    def get_hosts(self, offset=None, limit=100):
        params = {"_limit": limit}
        if offset:
            params["_offset"] = offset
        url_path = self.url("hosts")
        response = self.session.get(url_path, params=params)
        return response.json()
