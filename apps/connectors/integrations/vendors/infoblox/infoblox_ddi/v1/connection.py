from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class InfobloxDdiV1Config(TemplateVersionConfig):
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key",
        max_length=1024,
    )


class InfobloxDdiV1Connection(ConnectionTemplate):
    id = "infoblox_ddi"
    name = "Infoblox DDI"
    config_model = InfobloxDdiV1Config
