from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.infoblox.infoblox_ddi.v1.actions.host_sync import (
    InfobloxDdiV1HostSync,
)

from .api import InfobloxDdiV1Api
from .health_check import ConnectionHealthCheck


class InfobloxDdiV1Integration(Integration):
    api_class = InfobloxDdiV1Api
    actions = (InfobloxDdiV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
