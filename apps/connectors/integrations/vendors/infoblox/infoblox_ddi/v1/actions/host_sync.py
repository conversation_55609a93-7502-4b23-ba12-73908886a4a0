from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.infoblox.infoblox_ddi.v1.api import paginate
from apps.connectors.integrations.vendors.infoblox.infoblox_ddi.v1.health_check import (
    ReadAllHosts,
)

HOST_TYPE_MAP = {
    "NIOS": HostType.SERVER,
    "NIOS HA": HostType.SERVER,
    "BloxOne VM": HostType.SERVER,
    "BloxOne Appliance": HostType.SERVER,
    "BloxOne Container": HostType.CONTAINER,
    "CNIOS": HostType.CONTAINER,
}


def normalize_host(host_data: dict):
    hostname = host_data.get("display_name")
    ips = host_data.get("ip_address") if host_data.get("ip_address") else []
    mac_adressess = host_data.get("mac_address") if host_data.get("mac_address") else []

    os_name = host_data.get("host_type")
    host_type = next(
        (HOST_TYPE_MAP[k] for k in HOST_TYPE_MAP if k in os_name),
        HostType.UNKNOWN,
    )
    os_family, __ = OsFamily.from_string(os_name)

    return Host(
        source_id=host_data["id"],
        hostname=hostname,
        ip_addresses=ips,
        mac_addresses=mac_adressess,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("updated_at")),
        source_data=host_data,
    )


class InfobloxDdiV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_hosts, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
