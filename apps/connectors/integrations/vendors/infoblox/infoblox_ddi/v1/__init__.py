from apps.connectors.integrations import TemplateVersion

from .connection import InfobloxDdiV1Config, InfobloxDdiV1Connection
from .integration import InfobloxDdiV1Integration
from .settings import InfobloxDdiV1Settings


class InfobloxDdiV1TemplateVersion(TemplateVersion):
    integration = InfobloxDdiV1Integration
    id = "v1"
    name = "v1"
    config_model = InfobloxDdiV1Config
    connection_model = InfobloxDdiV1Connection
    settings_model = InfobloxDdiV1Settings
