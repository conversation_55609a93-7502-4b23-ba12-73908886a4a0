from typing import Generator

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas.operating_system import (
    HostType,
    OsAttributes,
    OsFamily,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.health_check import (
    ReadHosts,
)


def normalize_host(host_data: dict):
    ip_address = host_data.get("ip-address")
    mac_address = host_data.get("mac-addr")
    return Host(
        source_id=host_data.get("serial"),
        group_names=[],
        hostname=host_data.get("hostname"),
        fqdns=[],
        ip_addresses=[ip_address],
        mac_addresses=[mac_address],
        os=OsAttributes(
            family=OsFamily.UNKNOWN, name=None, host_type=HostType.UNKNOWN
        ),  # OsAttributes
        owners=[],  # List[OwnerAttributes]
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=None,  # only works for iso date string or list of iso date strings
        source_data=host_data,
    )


class PaloAltoPanoramaV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in api.get_devices(**kwargs):
            yield page

    def get_permission_checks(self):
        return [ReadHosts]
