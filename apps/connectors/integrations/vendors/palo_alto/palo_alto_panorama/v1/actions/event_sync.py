import re
from datetime import datetime, timezone
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.schemas.ocsf import (
    OSINT,
    ControlAction,
    Device,
    DeviceHwInfo,
    Disposition,
    File,
    GeoLocation,
    Metadata,
    NetworkActivity,
    NetworkActivityType,
    NetworkConnectionInfo,
    NetworkEndpoint,
    OperatingSystem,
    OSINTIndicatorType,
    Profile,
    Session,
    User,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.api import (
    PaloAltoPanoramaV1Api,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.bookmarks import (
    PaloAltoPanoramaSyncBookmark,
)


def parse_datetime(dt_str: str) -> datetime:
    return parser.parse(dt_str)


def get_iso_date(dt_str: str) -> datetime:
    return parser.parse(dt_str).isoformat()


def translate_country(cc: str) -> str:  # pragma: no cover
    if not cc:
        return None
    numbers = re.findall(r"\d+", cc)
    if numbers:
        return None
    return cc


def convert_ngfw_action(action: str) -> tuple[ControlAction, Disposition]:
    action = action.lower()
    control_action = ControlAction.UNKNOWN
    disposition = Disposition.UNKNOWN

    match action:
        case "alert":
            control_action = ControlAction.OBSERVED
            disposition = Disposition.ALERT

        case "allow":
            control_action = ControlAction.ALLOWED
            disposition = Disposition.ALLOWED

        case "deny":
            control_action = ControlAction.DENIED
            disposition = Disposition.OTHER

        case "drop" | "block-url" | "block-ip" | "random-drop" | "block":
            control_action = ControlAction.DENIED
            disposition = Disposition.DROPPED

        case "reset-client" | "reset-server" | "reset-both":
            control_action = ControlAction.DENIED
            disposition = Disposition.RESET

        case "sinkhole":
            control_action = ControlAction.ALLOWED
            disposition = Disposition.OTHER

        case "syncookie":
            control_action = ControlAction.ALLOWED
            disposition = Disposition.OTHER

        case "block-continue" | "block-override":
            control_action = ControlAction.DENIED
            disposition = Disposition.CHALLENGE

        case "continue":
            control_action = ControlAction.ALLOWED
            disposition = Disposition.ALLOWED

        case "override":
            control_action = ControlAction.ALLOWED
            disposition = Disposition.APPROVED

        case "override-lockout":
            control_action = ControlAction.DENIED
            disposition = Disposition.REJECTED

    return control_action, disposition


def get_osint_indicators(event: dict) -> list[OSINT]:
    """
    Generate OSINT indicators based on the threat log subtype and available data.

    Different threat subtypes map to different OSINT indicator types:
    - data: Data pattern indicators (OSINT.type = OTHER for data patterns)
    - file: File indicators (OSINT.type = FILE)
    - flood: Network flood indicators (OSINT.type = IP_ADDRESS for source of flood)
    - packet: Packet-based attack indicators (OSINT.type = IP_ADDRESS for attack source)
    - scan: Network scan indicators (OSINT.type = IP_ADDRESS for scan source)
    - spyware: URL and potentially file indicators (OSINT.type = URL)
    - url: URL indicators (OSINT.type = URL)
    - virus, ml-virus: File and hash indicators (OSINT.type = FILE, HASH)
    - vulnerability: URL and vulnerability indicators (OSINT.type = URL, VULNERABILITY)
    - wildfire: File hash and name indicators (OSINT.type = FILE, HASH)
    - wildfire-virus: File hash and virus indicators (OSINT.type = FILE, HASH)

    Args:
        event: The threat log event

    Returns:
        List of OSINT indicators
    """
    indicators = []
    subtype = event.get("subtype")

    if event.get("filedigest"):
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.HASH,
                value=event.get("filedigest"),
            )
        )

    if event.get("misc") and subtype in ["url", "spyware", "vulnerability"]:
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.URL,
                value=event.get("misc"),
            )
        )

    if event.get("misc") and subtype in [
        "file",
        "virus",
        "ml-virus",
        "wildfire",
        "wildfire-virus",
    ]:
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.FILE,
                value=event.get("misc"),
                file=File(
                    name=event.get("misc"),
                ),
            )
        )

    if subtype == "vulnerability" and event.get("threatid"):
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.VULNERABILITY,
                value=event.get("threatid"),
            )
        )

    if subtype == "data" and event.get("misc"):
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.OTHER,
                value=event.get("misc"),
            )
        )

    if subtype in ["flood", "packet", "scan"] and event.get("src"):
        indicators.append(
            OSINT(
                type=OSINTIndicatorType.OTHER,
                value=event.get("src"),
            )
        )

    return indicators


def convert_to_ocsf(event: dict) -> NetworkActivity:
    control_action, disposition = convert_ngfw_action(event.get("action"))
    time_dt = (
        event.get("time_generated")
        if event.get("time_generated")
        else event.get("cef-formatted-time_generated")
    )

    return NetworkActivity(
        action=control_action,
        disposition=disposition,
        activity=NetworkActivityType.OTHER,
        device=Device(
            name=event.get("device_name"),
            hw_info=DeviceHwInfo(
                serial_number=event.get("serial"),
            ),
        ),
        metadata=Metadata(
            correlation_uid=event.get("seqno"),
            event_code=event.get("subtype"),
            profiles=[
                Profile.DATETIME,
                Profile.OSINT,
                Profile.HOST,
                Profile.SECURITY_CONTROL,
            ],
            uid=event.get("@logid"),
        ),
        time_dt=get_iso_date(time_dt),
        src_endpoint=NetworkEndpoint(
            ip=event.get("src"),
            user=User(
                name=event.get("srcuser") or "",
                uid=event.get("srcuser") or "",
            ),
            zone=event.get("from"),
            interface_name=event.get("inbound_if"),
            port=event.get("sport"),
            location=GeoLocation(
                country=translate_country(event.get("srcloc", {}).get("@cc")),
            ),
            hostname=event.get("src_host"),
            os=OperatingSystem(
                type=event.get("src_osfamily"),
                version=event.get("src_osversion"),
                mac=event.get("src_mac"),
            ),
        ),
        dst_endpoint=NetworkEndpoint(
            ip=event.get("dst"),
            user=User(
                name=event.get("dstuser") or "",
                uid=event.get("dstuser") or "",
            ),
            zone=event.get("to"),
            interface_name=event.get("outbound_if"),
            port=event.get("dport"),
            location=GeoLocation(
                country=translate_country(event.get("dstloc", {}).get("@cc")),
            ),
            os=OperatingSystem(
                type=event.get("dst_osfamily"),
                version=event.get("dst_osversion"),
                hostname=event.get("dst_host"),
                mac=event.get("dst_mac"),
            ),
        ),
        app_name=event.get("app"),
        osint=get_osint_indicators(event),
        message=event.get("threatid") or "Threat detected",
        severity=event.get("severity"),
        connection_info=NetworkConnectionInfo(
            session=Session(
                uid=event.get("sessionid"),
                count=event.get("repeatcnt"),
            ),
            protocol_name=event.get("proto"),
        ),
    )


def normalize_event(event: dict) -> Event:
    return Event(
        event_timestamp=get_iso_date(event.get("receive_time")),
        raw_event=event,
        ocsf=convert_to_ocsf(event),
        vendor_item_ref=None,
        vendor_group_ref=None,
        ioc=EventIOCInfo(
            external_id=event.get("threatid"),
            external_name=event.get("threatid"),
            has_ioc_definition=False,
            mitre_techniques=None,
        ),
    )


class PaloAltoPanoramaV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: PaloAltoPanoramaSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: PaloAltoPanoramaV1Api = self.integration.get_api()
        query_receive_time = parse_datetime(bookmark.query_receive_time)
        latest_receive_time = query_receive_time

        threat_logs = api.get_threat_logs(start_time=query_receive_time)

        for log in threat_logs:
            log_time_str = log.get("receive_time")
            if log_time_str:
                log_time = parse_datetime(log_time_str)
                log_time = log_time.replace(tzinfo=timezone.utc)

                if log_time > latest_receive_time:
                    latest_receive_time = log_time
            yield log

        # Update bookmark with latest seen receive_time
        bookmark.query_receive_time = latest_receive_time

    def get_permission_checks(self):
        return []  # pragma: no cover
