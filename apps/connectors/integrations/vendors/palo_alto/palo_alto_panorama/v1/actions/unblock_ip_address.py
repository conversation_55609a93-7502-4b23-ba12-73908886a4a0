from apps.connectors.integrations.actions.ip_address import (
    Message,
    UnblockIpAddress,
    UnblockIpAddressArgs,
    UnblockIpAddressResult,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.api import (
    PaloAltoPanoramaV1Api,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.settings import (
    PaloAltoPanoramaV1DAGSettings,
)


class PaloAltoPanaramaUnBlockIpAddress(UnblockIpAddress):
    settings: PaloAltoPanoramaV1DAGSettings

    def execute(self, args: UnblockIpAddressArgs) -> UnblockIpAddressResult:
        api: PaloAltoPanoramaV1Api = self.integration.get_api()
        api.untag_ip_address(args.ip_address.value, self.settings.dynamic_address_group)
        return UnblockIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was unblocked.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
