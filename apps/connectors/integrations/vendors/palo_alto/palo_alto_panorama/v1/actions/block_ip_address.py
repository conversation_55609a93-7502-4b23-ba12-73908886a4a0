from apps.connectors.integrations.actions.ip_address import (
    BlockIpAddress,
    BlockIpAddressArgs,
    BlockIpAddressResult,
    Message,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.api import (
    PaloAltoPanoramaV1Api,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.settings import (
    PaloAltoPanoramaV1DAGSettings,
)


class PaloAltoPanaramaBlockIpAddress(BlockIpAddress):
    settings: PaloAltoPanoramaV1DAGSettings

    def execute(self, args: BlockIpAddressArgs) -> BlockIpAddressResult:
        api: PaloAltoPanoramaV1Api = self.integration.get_api()
        api.tag_ip_address(args.ip_address.value, self.settings.dynamic_address_group)
        return BlockIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was blocked.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
