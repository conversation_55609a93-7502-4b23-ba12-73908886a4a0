from urllib.parse import urljoin

import requests
import xmltodict
from pydantic import Field, HttpUrl, field_validator
from pydantic_core.core_schema import ValidationInfo

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class PaloAltoPanoramaV1Config(TemplateVersionConfig):
    # https://docs.paloaltonetworks.com/pan-os/11-1/pan-os-panorama-api/pan-os-api-authentication/get-your-api-key
    base_url: HttpUrl = Field(
        title="Panorama URL",
        description="The URL of the Panorama server.",
    )
    username: str = Field(
        title="Username",
        description="The username to use for authentication.",
    )
    password: EncryptedStr = Field(
        title="Password",
        description="The password to use for authentication.",
    )
    api_key: EncryptedStr = Field(
        default=None,
        title="Api Key",
        # "Optional" - if not set, we try to set it in the field validator
        description="Optional. Use if you prefer to provide a static API key instead of a login.",
    )
    verify_tls: bool = Field(
        default=True,
        title="Verify TLS Certificates",
        description="Defaults to True. Setting this to False could pose a security risk if used improperly.",
    )

    @field_validator(
        "api_key", mode="before"
    )  # mode='before' to run before default value assignment
    def validate_key(cls, value, values: ValidationInfo):  # noqa: N805
        if not value:
            try:
                headers = {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json",
                }
                data = {
                    "username": values.data.get(
                        "username"
                    ),  # Access other fields via `values`
                    "password": values.data.get("password").get_secret_value(),
                }
                response = requests.post(
                    urljoin(str(values.data.get("base_url")), "api/?type=keygen"),
                    headers=headers,
                    data=data,
                )

                # Check if the response is valid and parse the key from XML response
                response_data = xmltodict.parse(response.text)
                api_key = (
                    response_data.get("response", {}).get("result", {}).get("key", None)
                )
                return api_key
            except:
                raise ValueError("API key could not be generated.")

        # Return the provided value if it's not empty
        return value


class PaloAltoPanoramaV1Connection(ConnectionTemplate):
    id = "palo_alto_panorama"
    name = "Palo Alto Panorama"
    config_model = PaloAltoPanoramaV1Config
