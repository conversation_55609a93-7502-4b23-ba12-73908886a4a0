from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.bookmarks import (
    PaloAltoPanoramaSyncBookmarks,
)

from .connection import PaloAltoPanoramaV1Config, PaloAltoPanoramaV1Connection
from .integration import PaloAltoPanoramaV1Integration
from .settings import PaloAltoPanoramaV1Settings


class PaloAltoPanoramaV1TemplateVersion(TemplateVersion):
    integration = PaloAltoPanoramaV1Integration
    id = "v1"
    name = "v1"
    config_model = PaloAltoPanoramaV1Config
    connection_model = PaloAltoPanoramaV1Connection
    settings_model = PaloAltoPanoramaV1Settings
    bookmarks_model = PaloAltoPanoramaSyncBookmarks
