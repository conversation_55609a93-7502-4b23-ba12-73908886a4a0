from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.actions.block_ip_address import (
    PaloAltoPanaramaBlockIpAddress,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.actions.event_sync import (
    PaloAltoPanoramaV1EventSync,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.actions.host_sync import (
    PaloAltoPanoramaV1HostSync,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_panorama.v1.actions.unblock_ip_address import (
    PaloAltoPanaramaUnBlockIpAddress,
)

from .api import PaloAltoPanoramaV1Api
from .health_check import ConnectionHealthCheck


class PaloAltoPanoramaV1Integration(Integration):
    api_class = PaloAltoPanoramaV1Api
    actions = (
        PaloAltoPanoramaV1HostSync,
        PaloAltoPanoramaV1EventSync,
        PaloAltoPanaramaBlockIpAddress,
        PaloAltoPanaramaUnBlockIpAddress,
    )
    critical_health_checks = (ConnectionHealthCheck,)
