from datetime import datetime
from typing import Generator

from ata_common.chunking import chunks
from django.utils.timezone import make_aware

from apps.connectors.integrations.actions import HostSync, normalize, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.palo_alto.cortex_xdr.v1.health_check import (
    ReadEndpoints,
)

HOST_TYPE_MAP = {
    "workstation": HostType.WORKSTATION,
    "server": HostType.SERVER,
    "mobile": HostType.MOBILE,
    "container": HostType.CONTAINER,
}

OS_FAMILY_MAP = {
    "AGENT_OS_WINDOWS": OsFamily.WINDOWS,
    "AGENT_OS_LINUX": OsFamily.LINUX,
    "AGENT_OS_MAC": OsFamily.MAC,
    "AGENT_OS_IOS": OsFamily.IOS,
    "AGENT_OS_ANDROID": OsFamily.ANDROID,
}


def normalize_last_seen(host_data: dict) -> datetime:
    unix_timestamp = host_data.get("last_seen") / 1000
    return (
        make_aware(datetime.utcfromtimestamp(unix_timestamp))
        if unix_timestamp
        else None
    )


def normalize_host(host_data: dict):
    endpoint_type = (host_data.get("endpoint_type") or "").lower()
    host_type = next(
        (HOST_TYPE_MAP[k] for k in HOST_TYPE_MAP if k in endpoint_type),
        HostType.UNKNOWN,
    )
    os_family, __ = OsFamily.from_string(
        host_data.get("os_type"), os_family_map=OS_FAMILY_MAP
    )

    ip_addresses = []
    ip_addresses.extend(to_list(host_data.get("public_ip")))
    ip_addresses.extend(host_data.get("ip") + host_data.get("ipv6"))
    mac_addresses = host_data.get("mac_address")

    # Except MacOS, the operating_system contains the full name.
    os_name = (
        " ".join([host_data.get("operating_system"), str(host_data.get("os_version"))])
        if os_family == OsFamily.MAC
        else host_data.get("operating_system")
    )

    hostname = host_data.get("endpoint_name")

    return Host(
        source_id=host_data.get("endpoint_id", ""),
        group_names=to_list(host_data.get("group_name")),
        hostname=hostname,
        _domain=host_data.get("domain"),
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data),
        source_data=host_data,
    )


class CortexXdrV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()

        endpoints = api.get_endpoints()
        # Retrieve all the endpoints first, so we can filter them by id.
        # Doing this avoids the complexity of potentially retrieving the same
        # endpoint multiple times when paginating the get_endpoint method.
        # We are avoiding paginating the endpoints by chunking all endpoints
        # in groups of 100, the max result size for the get_endpoint method.
        # This method is also slightly faster than paginating.
        for chunk in chunks(endpoints, 100):
            ids = [endpoint["agent_id"] for endpoint in chunk]
            filters = [{"field": "endpoint_id_list", "operator": "in", "value": ids}]
            yield from api.get_endpoint(filters=filters, **kwargs)["endpoints"]

    def get_permission_checks(self):
        return [ReadEndpoints]
