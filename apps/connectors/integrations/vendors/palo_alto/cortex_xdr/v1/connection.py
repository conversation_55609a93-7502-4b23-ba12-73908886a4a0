from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CortexXdrV1Config(TemplateVersionConfig):
    api_url: HttpUrl = Field(title="Tenant URL")
    advanced_api_key: EncryptedStr = Field(
        title="Advanced API Key",
        max_length=1024,
    )
    advanced_api_key_id: str = Field(
        title="Advanced API Key ID",
        max_length=1024,
    )


class CortexXdrV1Connection(ConnectionTemplate):
    id = "cortex_xdr"
    name = "Cortex XDR"
    config_model = CortexXdrV1Config
