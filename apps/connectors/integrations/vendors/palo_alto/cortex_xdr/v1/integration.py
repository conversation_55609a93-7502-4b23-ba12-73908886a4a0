from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.palo_alto.cortex_xdr.v1.health_check import (
    ConnectionHealthCheck,
)

from .actions.host_sync import CortexXdrV1HostSync
from .api import CortexXdrV1Api


class CortexXdrV1Integration(Integration):
    api_class = CortexXdrV1Api
    actions = (CortexXdrV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
