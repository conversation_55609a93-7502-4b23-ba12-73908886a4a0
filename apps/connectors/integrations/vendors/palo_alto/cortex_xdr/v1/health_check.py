from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadEndpoints(IntegrationPermissionsHealthCheck):
    name = "Read endpoints"
    description = "Read endpoints from Cortex XDR"
    value = "endpoints:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke(
                "get_endpoint", search_from=0, search_to=1
            )
            return (
                IntegrationHealthCheckResult.PASSED
                if response
                else IntegrationHealthCheckResult.FAILED
            )
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_health_check")
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
