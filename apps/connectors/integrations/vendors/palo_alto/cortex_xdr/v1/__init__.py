from apps.connectors.integrations import TemplateVersion

from .connection import CortexXdrV1Config, CortexXdrV1Connection
from .integration import CortexXdrV1Integration
from .settings import CortexXdrV1Settings


class CortexXdrV1TemplateVersion(TemplateVersion):
    integration = CortexXdrV1Integration
    id = "v1"
    name = "v1"
    config_model = CortexXdrV1Config
    connection_model = CortexXdrV1Connection
    settings_model = CortexXdrV1Settings
