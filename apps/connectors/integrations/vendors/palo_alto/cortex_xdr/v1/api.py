import hashlib
import logging
from urllib.parse import urljoin

from django.utils.crypto import get_random_string
from django.utils.timezone import now

from apps.connectors.integrations import ApiBase

logger = logging.getLogger(__name__)


# def paginate(func, page_size=100, **kwargs):
#     """
#     Generator to paginate through results
#     """
#     # Search parameters are zero-based.
#     search_from = 0
#     search_to = page_size
#     while True:
#         result = func(search_from=search_from, search_to=search_to, **kwargs)
#
#         result.pop("total_count")
#         result_count = result.pop("result_count")
#
#         # The API returns a list of results under a different key for each endpoint.
#         # Use the only key left in the result as the key for the list of items.
#         assert len(result.keys()) == 1
#         key = list(result.keys())[0]
#
#         items = result[key]
#         yield items
#
#         if result_count < page_size:
#             break
#
#         search_from += page_size
#         search_to += page_size


class CortexXdrV1Api(ApiBase):
    """
    Cortex XDR API wrapper
    https://cortex-panw.stoplight.io/docs/cortex-xdr
    """

    def __init__(
        self,
        api_url=None,
        advanced_api_key_id=None,
        advanced_api_key=None,
        **kwargs,
    ):
        self.api_key = advanced_api_key
        self.api_key_id = str(advanced_api_key_id)

        base_url = urljoin(api_url, "/public_api/v1/")
        super().__init__(base_url=base_url)

    def get_endpoints(self):
        """
        https://cortex-panw.stoplight.io/docs/cortex-xdr/89535019b740f-get-all-endpoints
        """

        endpoint = "endpoints/get_endpoints"
        return self.post(endpoint)

    def get_endpoint(
        self,
        filters: list = None,
        sort: list = None,
        search_from: int = None,
        search_to: int = None,
    ):
        """
        https://cortex-panw.stoplight.io/docs/cortex-xdr/b149d40bd4c51-get-endpoint

        :param filters:  list of {"field": "field_name", "operator": "operator", "value": "value"}
            field -> Allowed values:
                endpoint_id_list
                endpoint_status
                dist_name
                first_seen
                last_seen
                ip_list
                group_name
                platform
                alias
                isolate
                hostname
            operator -> Allowed values:
                in
                gte
                lte
        :param sort: list of {"field": "field_name", "keyword": "keyword"}
            field -> Allowed values:
                endpoint_id
                first_seen
                last_seen
            keyword -> Allowed values: ASC, DESC
        :param search_from: Represents the start offset within the query result set from which
            you want endpoints returned.
            Endpoints are returned as a zero-based list. Any endpoint indexed less than
            this value is not returned in the final result set and defaults to zero.
        :param search_to: Represents the end offset within the result set after which you do
            not want endpoints returned.
            Endpoint in the endpoint list that is indexed higher than this value is
            not returned in the final results set. Defaults to 100, which returns all
            endpoints to the end of the list.

        """
        endpoint = "endpoints/get_endpoint"

        return self.post(
            endpoint,
            filters=filters,
            sort=sort,
            search_from=search_from,
            search_to=search_to,
        )

    def post(self, endpoint, **kwargs):
        body = {
            "request_data": {
                key: value for key, value in kwargs.items() if value is not None
            }
        }
        url = self.url(endpoint)
        self.session.headers.update(self.get_headers())
        return self.session.post(url, json=body).json()["reply"]

    def get_headers(self):
        """
        Copying over from their example here
        https://docs-cortex.paloaltonetworks.com/r/Cortex-XDR/Cortex-XDR-API-Reference/Get-Started-with-APIs
        """
        nonce = get_random_string(length=64)
        timestamp = int(now().timestamp()) * 1000
        auth_key = "%s%s%s" % (self.api_key, nonce, timestamp)
        api_key_hash = hashlib.sha256(auth_key.encode("utf-8")).hexdigest()
        return {
            "x-xdr-timestamp": str(timestamp),
            "x-xdr-nonce": nonce,
            "x-xdr-auth-id": self.api_key_id,
            "Authorization": api_key_hash,
        }

    def get_health_check(self):
        # https://cortex-panw.stoplight.io/docs/cortex-xdr/2d6b304423a4f-system-health-check
        endpoint = "healthcheck"
        url = self.url(endpoint)
        self.session.headers.update(self.get_headers())
        return self.session.get(url).json()
