import logging
from typing import Generator

from falconpy import APIError, Result

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    InternetExposure,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.crowdstrike.falcon_em.v1.api import EntityType
from apps.connectors.integrations.vendors.crowdstrike.falcon_em.v1.health_check import (
    ReadAssets,
)
from apps.connectors.integrations.vendors.crowdstrike.falcon_em.v1.settings import (
    FalconEmV1HostSyncSettings,
)
from apps.connectors.utils import split_cs

logger = logging.getLogger(__name__)

HOST_TYPE_MAP = {
    "Workstation": HostType.WORKSTATION,
    "Server": HostType.SERVER,
    "Domain Controller": HostType.SERVER,
    "Mobile": HostType.MOBILE,
    "Pod": HostType.CONTAINER,
}

CRITICALITY_MAP = {
    "Unassigned": AssetCriticality.UNKNOWN,
    "Critical": AssetCriticality.TIER_0,
    "High": AssetCriticality.TIER_1,
    "Noncritical": AssetCriticality.TIER_4,
}

INTERNET_EXPOSURE_MAP = {
    "Yes": InternetExposure.INTERNET_FACING,
    "No": InternetExposure.NOT_INTERNET_FACING,
    "Pending": InternetExposure.UNKNOWN,
}


def normalize_host(host_data: dict):
    # Copy the mac addresses and ip addresses to avoid modifying the original list.
    mac_addresses = list(host_data.get("mac_addresses", []))
    ip_addresses = list(host_data.get("local_ip_addresses", []))

    for interface in host_data.get("network_interfaces", []):
        mac = interface.get("mac_address")
        if mac and mac not in mac_addresses:
            mac_addresses.append(mac)
        if "local_ip" in interface and interface["local_ip"] not in ip_addresses:
            ip_addresses.append(interface["local_ip"])

    if external_ip := host_data.get("external_ip"):
        ip_addresses.append(external_ip)

    if current_local_ip := host_data.get("current_local_ip"):
        if current_local_ip in ip_addresses:
            ip_addresses.remove(current_local_ip)
        ip_addresses.insert(0, current_local_ip)

    internet_exposure = INTERNET_EXPOSURE_MAP.get(
        host_data.get("internet_exposure", ""), InternetExposure.UNKNOWN
    )

    host_type = HOST_TYPE_MAP.get(
        host_data.get("product_type_desc", ""), HostType.UNKNOWN
    )
    platform_name = host_data.get("platform_name") or ""
    os_family, __ = OsFamily.from_string(platform_name)
    os_name = host_data.get("os_version", "")
    os = OsAttributes(host_type=host_type, family=os_family, name=os_name)

    hostname = host_data.get("hostname", "")
    domain = host_data.get("machine_domain")
    groups = host_data.get("groups", [])
    criticality = CRITICALITY_MAP.get(
        host_data.get("criticality", ""), AssetCriticality.UNKNOWN
    )

    return Host(
        source_id=host_data["id"],
        group_names=groups,
        hostname=hostname,
        _domain=domain,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        internet_exposure=internet_exposure,
        os=os,
        criticality=criticality,
        last_seen=normalize_last_seen(host_data.get("last_seen_timestamp")),
        source_data=host_data,
    )


class FalconEmV1HostSync(HostSync):
    settings: FalconEmV1HostSyncSettings

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()

        cid_list = split_cs(self.settings.fetch_by_customer_ids)

        # Due to the limitation of 10K hosts per query, we need to
        # sort by last_seen_timestamp to get the most recent data first.
        # Additionally, we are filtering the query by entity_type to maximize
        # the number of hosts we can get even with the 10K limit.
        for entity_type in EntityType:
            if entity_type == EntityType.UNSUPPORTED:
                # We define a host as a workstation or server. We are not
                # interested in what CrowdStrike calls unsupported because
                # they are devices like printers, routers, etc.
                continue

            fql = f"entity_type:'{entity_type}'"
            if cid_list:
                cid_fql = ",".join(f"cid:'{cid}'" for cid in cid_list)
                fql = f"{fql}+({cid_fql})"

            try:
                result: Result
                offset = 0
                total = 1  # Just to get the loop started.
                while offset < total:
                    result = api.query_hosts(
                        offset=offset,
                        filter=fql,
                        sort="last_seen_timestamp.desc",
                        **kwargs,
                    )
                    total = result.total
                    # The documented pattern is to use result.offset for the next call.
                    # However, the api returns the offset passed in, not the offset of the next page.
                    # I am unsure if this is a bug in the discover api or if the documentation is wrong.
                    offset += result.limit
                    ids = list(result)
                    yield from api.get_hosts(ids=ids)
            except APIError as e:
                limit_message = "ERROR: offset 10000 and limit 100 are invalid; offset + limit must be less than or equal to 10000"
                if e.message == limit_message:
                    logger.warning(
                        "CrowdStrike API limit reached. Continuing with next query."
                    )
                else:
                    raise

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAssets]
