from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.crowdstrike.connection import (
    CrowdstrikeConnection,
)

from .integration import FalconEmV1Integration
from .settings import FalconEmV1Settings


class FalconEmV1TemplateVersion(TemplateVersion):
    integration = FalconEmV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = CrowdstrikeConnection
    settings_model = FalconEmV1Settings
