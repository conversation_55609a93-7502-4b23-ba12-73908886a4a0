from enum import StrEnum

from ata_common.chunking import chunks
from falconpy import Discover


class EntityType(StrEnum):
    MANAGED = "managed"
    UNMANAGED = "unmanaged"
    UNSUPPORTED = "unsupported"


class FalconEmV1Api:
    """
    Crowdstrike Falcon API
    https://falcon.crowdstrike.com/documentation/category/a3a706a8/crowdstrike-apis
    """

    def __init__(self, client_id=None, client_secret=None):
        # Asset Management APIs: https://falcon.crowdstrike.com/documentation/page/a9df69ec/asset-management-apis
        self.discover = Discover(
            client_id=client_id,
            client_secret=client_secret,
            pythonic=True,
        )

    def query_hosts(self, **kwargs):
        result = self.discover.query_hosts(**kwargs)
        return result

    def get_hosts(self, ids=None):
        for chunk in chunks(ids, 100):
            result = self.discover.get_hosts(ids=chunk)
            yield from result
