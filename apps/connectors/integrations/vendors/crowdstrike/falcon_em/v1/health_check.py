from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadAssets(IntegrationPermissionsHealthCheck):
    name = "Read assets"
    description = "Read assets from Falcon"
    value = "assets:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke("query_hosts", limit=1)
            return (
                IntegrationHealthCheckResult.PASSED
                if response
                else IntegrationHealthCheckResult.FAILED
            )
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        if api.discover.token_valid:
            return IntegrationHealthCheckResult.PASSED
        else:
            return IntegrationHealthCheckResult.FAILED
