from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class FalconEmV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(
        title="CrowdStrike Discover Fetch Hosts Settings",
    )

    fetch_by_customer_ids: str = Field(
        title="Fetch by Customer IDs",
        description="Specify a comma-separated list of Customer IDs (CIDs). "
        "If the API client is created in the parent customer, leaving this field empty will fetch hosts "
        "for the parent and all child CIDs, while entering specific CIDs will fetch hosts only for those CIDs. "
        "This field should not be populated if the API client is created in a child customer, "
        "as it will automatically fetch hosts only for that child customer.",
        default_factory=str,
    )


FalconEmV1Settings = create_settings_model(
    "FalconEmV1Settings",
    {
        IntegrationActionType.HOST_SYNC: FalconEmV1HostSyncSettings,
    },
)
