import logging

from falconpy import APIError

from apps.connectors.integrations import Integration

from .actions.host_sync import FalconEmV1HostSync
from .api import FalconEmV1Api
from .health_check import ConnectionHealthCheck

logger = logging.getLogger(__name__)


class FalconEmV1Integration(Integration):
    api_class = FalconEmV1Api
    exception_types = (APIError,)
    actions = (FalconEmV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
