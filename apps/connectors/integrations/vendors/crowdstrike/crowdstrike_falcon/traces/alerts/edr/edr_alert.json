{"agent_id": "4bcf0f889edd40428b9b74bed8bd18dd", "aggregate_id": "aggind:4bcf0f889edd40428b9b74bed8bd18dd:48702446249", "alleged_filetype": "exe", "child_process_ids": ["pid:4bcf0f889edd40428b9b74bed8bd18dd:720581558317", "pid:4bcf0f889edd40428b9b74bed8bd18dd:720580396778"], "cid": "cf1f74a3638945d59f17070a253e8d5a", "cloud_indicator": "false", "cmdline": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy AllSigned -NoProfile -NonInteractive -Command \"& {$OutputEncoding = [Console]::OutputEncoding =[System.Text.Encoding]::UTF8;$scriptFileStream = [System.IO.File]::Open('C:\\ProgramData\\Microsoft\\Windows Defender Advanced Threat Protection\\DataCollection\\8499.12492757.0.12492757-5c32cfb008a88a61dd313f1f303dfae04a73a3c5\\0e3d6d2d-06cc-486d-9465-9ef3bee75444.ps1', [System.IO.FileMode]::Open, [System.IO.FileAccess]::Read, [System.IO.FileShare]::Read);$calculatedHash = Get-FileHash 'C:\\ProgramData\\Microsoft\\Windows Defender Advanced Threat Protection\\DataCollection\\8499.12492757.0.12492757-5c32cfb008a88a61dd313f1f303dfae04a73a3c5\\0e3d6d2d-06cc-486d-9465-9ef3bee75444.ps1' -Algorithm SHA256;if (!($calculatedHash.Hash -eq 'a759a3af0cde8863e7594aa52e86f32b489b8c47ea7ad6d1277755dcec6e8184')) { exit 323;}; . 'C:\\ProgramData\\Microsoft\\Windows Defender Advanced Threat Protection\\DataCollection\\8499.12492757.0.12492757-5c32cfb008a88a61dd313f1f303dfae04a73a3c5\\0e3d6d2d-06cc-486d-9465-9ef3bee75444.ps1' }\"", "comment": "11 Experimental IOC\nPriority: Critical INFO - 0 https://portal.criticalstart.io/#/incidents/36250704\nCategory: security Closed by CAT alert: to Start.\nLink", "comments": [{"falcon_user_id": "faccab939c444116a95c8f8b3451d562", "timestamp": "2025-03-25T14:20:20.573015318Z", "value": "Closed by Critical Start.\nLink to security alert: https://portal.criticalstart.io/#/incidents/36250704\nCategory: CAT 11 - Experimental IOC\nPriority: 0 - INFO"}], "composite_id": "cf1f74a3638945d59f17070a253e8d5a:ind:4bcf0f889edd40428b9b74bed8bd18dd:************-41004-**********", "confidence": 100, "context_timestamp": "2025-03-25T14:18:14.222Z", "control_graph_id": "ctg:4bcf0f889edd40428b9b74bed8bd18dd:48702446249", "crawled_timestamp": "2025-03-25T15:18:18.080343895Z", "created_timestamp": "2025-03-25T14:19:18.016156706Z", "data_domains": ["Endpoint"], "description": "A process triggered an informational severity custom rule.", "device": {"agent_load_flags": "0", "agent_local_time": "2024-08-20T02:53:18.347Z", "agent_version": "7.15.18514.0", "bios_manufacturer": "<PERSON>en", "bios_version": "4.11.amazon", "cid": "cf1f74a3638945d59f17070a253e8d5a", "config_id_base": "65994753", "config_id_build": "18514", "config_id_platform": "3", "device_id": "4bcf0f889edd40428b9b74bed8bd18dd", "external_ip": "*************", "first_seen": "2024-08-16T14:21:54Z", "hostinfo": {"domain": ""}, "hostname": "I-070112468EB25", "instance_id": "i-070112468eb251de3", "last_seen": "2025-03-25T03:56:02Z", "local_ip": "**********", "mac_address": "02-ce-69-54-ae-67", "machine_domain": "", "major_version": "6", "minor_version": "3", "modified_timestamp": "2025-03-25T03:56:08Z", "os_version": "Windows Server 2012 R2", "ou": null, "platform_id": "0", "platform_name": "Windows", "pod_labels": null, "product_type": "3", "product_type_desc": "Server", "service_provider": "AWS_EC2_V2", "service_provider_account_id": "************", "status": "normal", "system_manufacturer": "<PERSON>en", "system_product_name": "HVM domU"}, "display_name": "CustomIOAWinLowest", "email_sent": true, "falcon_host_link": "https://falcon.crowdstrike.com/activity-v2/detections/cf1f74a3638945d59f17070a253e8d5a:ind:4bcf0f889edd40428b9b74bed8bd18dd:************-41004-**********?_cid=g03000yclplcl665amvn66b6hdd4swfq", "filename": "powershell.exe", "filepath": "\\Device\\HarddiskVolume2\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "global_prevalence": "common", "grandparent_details": {"cmdline": "\"C:\\Program Files\\Windows Defender Advanced Threat Protection\\MsSense.exe\"", "filename": "MsSense.exe", "filepath": "\\Device\\HarddiskVolume2\\Program Files\\Windows Defender Advanced Threat Protection\\MsSense.exe", "local_process_id": "7560", "md5": "e33ca30d381049107ed0a45e94d6f09c", "process_graph_id": "pid:4bcf0f889edd40428b9b74bed8bd18dd:************", "process_id": "************", "sha256": "83fa920eb14dae24091444502becc2510bf99ce0ecad5b2a1694daf865210c87", "timestamp": "1601-01-01T00:00:00.000Z", "user_graph_id": "uid:4bcf0f889edd40428b9b74bed8bd18dd:S-1-5-18", "user_id": "S-1-5-18", "user_name": "I-070112468EB25$"}, "id": "ind:4bcf0f889edd40428b9b74bed8bd18dd:************-41004-**********", "indicator_id": "ind:4bcf0f889edd40428b9b74bed8bd18dd:************-41004-**********", "ioc_context": [], "local_prevalence": "low", "local_process_id": "3608", "logon_domain": "WORKGROUP", "md5": "c031e215b8b08c752bf362f6d4c5d3ad", "name": "WinCustomTemplateInformationalPattern", "objective": "Falcon Detection Method", "parent_details": {"cmdline": "\"C:\\Program Files\\Windows Defender Advanced Threat Protection\\SenseIR.exe\" \"OfflineSenseIR\" \"5372\" \"eyJDb21tYW5kSWQiOiIiLCJEb3dubG9hZEZpbGVBY3Rpb25Db25maWciOm51bGwsIkRvd25sb2FkVHJ1c3RlZENlcnRpZmljYXRlc0NoYWlucyI6bnVsbCwiRW5hYmxlU2xlZXBTdXNwZW5zaW9uIjowLCJNYXhXYWl0Rm9yTmV3QWN0aW9uc0luTXMiOjEyNjAwMDAsIk9yZ0lkIjoiIiwiUnVuUHNTY3JpcHRBY3Rpb25Db25maWciOnsiZW5hYmxlIjp0cnVlfSwiYWNjZXB0U2ltdWxhdG9yU2lnbmluZyI6MCwib2ZmbGluZUlyUGlwZUhhbmRsZSI6NTIxMn0=\"", "filename": "SenseIR.exe", "filepath": "\\Device\\HarddiskVolume2\\Program Files\\Windows Defender Advanced Threat Protection\\SenseIR.exe", "local_process_id": "6236", "md5": "7a2c88b0b11927b17c8aeec2404eec69", "process_graph_id": "pid:4bcf0f889edd40428b9b74bed8bd18dd:************", "process_id": "************", "sha256": "1344d68bb61000fd58bda5db1708abd61b6b6a59a5b518d80164c1f34c1bfc1d", "timestamp": "2025-03-25T14:35:29Z", "user_graph_id": "uid:4bcf0f889edd40428b9b74bed8bd18dd:S-1-5-18", "user_id": "S-1-5-18", "user_name": "I-070112468EB25$"}, "parent_process_id": "************", "pattern_disposition": 0, "pattern_disposition_description": "Detection, standard detection.", "pattern_disposition_details": {"blocking_unsupported_or_disabled": false, "bootup_safeguard_enabled": false, "containment_file_system": false, "critical_process_disabled": false, "detect": false, "fs_operation_blocked": false, "handle_operation_downgraded": false, "inddet_mask": false, "indicator": false, "kill_action_failed": false, "kill_parent": false, "kill_process": false, "kill_subprocess": false, "mfa_required": false, "operation_blocked": false, "policy_disabled": false, "prevention_provisioning_enabled": false, "process_blocked": false, "quarantine_file": false, "quarantine_machine": false, "registry_operation_blocked": false, "response_action_already_applied": false, "response_action_failed": false, "response_action_triggered": false, "rooting": false, "sensor_only": false, "suspend_parent": false, "suspend_process": false}, "pattern_id": 41004, "platform": "Windows", "poly_id": "AADPH3SjY4lF1Z8XBwolPo1avpP-s9B6rwEyr0OfJFyGRwAATiHHRBX135MXOFUax7PaCtXRsO6hw0YvLl7ujWKsaRTJVg==", "process_end_time": "1742912407", "process_id": "************", "process_start_time": "1742912294", "product": "epp", "rule_group_id": "aba21ad49ff64ff69b3f63d0a4508e80", "rule_group_name": "Critical Start MDR Detections for Windows", "rule_instance_created_by": "<EMAIL>", "rule_instance_id": "1146", "rule_instance_name": "Exp - CSDE0013 - Disabling Microsoft Defender with dir deletion", "rule_instance_version": "2", "scenario": "suspicious_activity", "seconds_to_resolved": 62, "seconds_to_triaged": 0, "severity": 10, "severity_name": "Informational", "sha1": "0000000000000000000000000000000000000000", "sha256": "840e1f9dc5a29bebf01626822d7390251e9cf05bb3560ba7b68bdb8a41cf08e3", "show_in_ui": true, "source_products": ["Falcon Insight"], "source_vendors": ["CrowdStrike"], "status": "closed", "tactic": "Custom Intelligence", "tactic_id": "CSTA0005", "technique": "Indicator of Attack", "technique_id": "CST0004", "template_instance_id": "1146", "template_instance_version": "2", "timestamp": "2025-03-25T14:18:16.987Z", "tree_id": "48702446249", "tree_root": "************", "triggering_process_graph_id": "pid:4bcf0f889edd40428b9b74bed8bd18dd:************", "type": "ldt", "updated_timestamp": "2025-03-25T15:18:18.080323861Z", "user_id": "S-1-5-18", "user_name": "I-070112468EB25$"}