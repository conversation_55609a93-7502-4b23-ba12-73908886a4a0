from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.crowdstrike.connection import (
    CrowdstrikeConnection,
)

from .integration import CrowdstrikeFalconV1Integration
from .settings import CrowdstrikeFalconV1Settings


class CrowdstrikeFalconV1TemplateVersion(TemplateVersion):
    integration = CrowdstrikeFalconV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = CrowdstrikeConnection
    settings_model = CrowdstrikeFalconV1Settings
