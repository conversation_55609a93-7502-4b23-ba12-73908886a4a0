import logging

from ata_common.chunking import chunks
from falconpy import Al<PERSON><PERSON>, Hosts, OAuth2, Result

logger = logging.getLogger(__name__)


def paginate(bound_method, *args, body=None):
    """Only used for the bulk fetch alerts API"""
    response = bound_method(*args, body=body)
    for r in response:
        yield r
    cursor = response.meta.pagination.get("after")
    while cursor:
        body["after"] = cursor
        response = bound_method(*args, body=body)
        for r in response:
            yield r
        cursor = response.meta.pagination.get("after")


class CrowdstrikeFalconV1Api:
    """
    Crowdstrike Falcon API
    https://falcon.crowdstrike.com/documentation/category/a3a706a8/crowdstrike-apis
    """

    BASE_URL = "https://api.crowdstrike.com"

    def __init__(self, client_id=None, client_secret=None):
        # Initialize FalconPY auth
        self.auth = OAuth2(client_id=client_id, client_secret=client_secret)

        # Alerts are shared across all Products. Formerly known as Detections
        self.alerts = Alerts(auth_object=self.auth, pythonic=True)
        # Hosts API: https://falcon.crowdstrike.com/documentation/page/c0b16f1b/host-and-host-group-management-apis
        self.hosts = Hosts(auth_object=self.auth, pythonic=True)

    def fetch_alerts(self, body: dict):
        """Fetch alerts from Crowdstrike API"""
        return self.alerts.get_alerts_combined(body=body)

    def get_device_ids_by(self, **kwargs):
        result: Result
        # noinspection PyTypeChecker
        result = self.hosts.query_devices_by_filter(**kwargs)
        return list(result)

    def get_all_device_ids(self, **kwargs):
        result: Result
        offset = None
        total = 1  # Just to get the loop started.
        ids = []
        while len(ids) < total:
            # noinspection PyTypeChecker
            result = self.hosts.query_devices_by_filter_scroll(**kwargs, offset=offset)
            total = result.total
            offset = result.offset
            ids.extend(result)
        return ids

    def get_devices(self, ids, **kwargs):
        result: Result
        # noinspection PyTypeChecker
        result = self.hosts.get_device_details(ids=ids)
        return list(result)

    def get_devices_by(self, **kwargs):
        ids = self.get_device_ids_by(**kwargs)
        return self.get_devices(ids)

    def get_all_devices(self, **kwargs):
        result: Result
        ids = self.get_all_device_ids(**kwargs)
        for ids_chunk in chunks(ids, 5000):
            # noinspection PyTypeChecker
            result = self.hosts.get_device_details(ids=ids_chunk)
            yield from result

    def get_nic_history(self, device_ids):
        result: Result
        # noinspection PyTypeChecker
        result = self.hosts.query_network_address_history(ids=device_ids)
        return list(result)
