from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.integration import IntegrationError


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            # The API tries to initialize and get a token. As long as we can authenticate
            # this check will pass
            self.integration.get_api()
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
