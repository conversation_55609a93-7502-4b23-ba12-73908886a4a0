from typing import Generator

from ata_common.chunking import chunks

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
    to_list,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.v1.api import (
    CrowdstrikeFalconV1Api,
)

HOST_TYPE_MAP = {
    "Workstation": HostType.WORKSTATION,
    "Server": HostType.SERVER,
    "Domain Controller": HostType.SERVER,
    "Mobile": HostType.MOBILE,
    "Pod": HostType.CONTAINER,
}


def normalize_host(host_data: dict):
    ip_addresses = []
    ip_addresses.extend(to_list(host_data.get("local_ip")))
    ip_addresses.extend(to_list(host_data.get("external_ip")))
    mac_addresses = host_data.get("mac_address")
    os_family, fallback_name = OsFamily.from_string(host_data.get("platform_name"))
    os_name = (
        host_data.get("os_product_name") or host_data.get("os_version") or fallback_name
    )
    host_type = HOST_TYPE_MAP.get(host_data.get("product_type_desc"), HostType.UNKNOWN)
    hostname = host_data.get("hostname") or ""

    # Network history is provided in reverse chronological order (latest first),
    # Append only the latest IP address for each unique MAC address.
    nic_history = host_data.get("nic_history")
    if nic_history:
        mac_addresses = to_list(mac_addresses)
        for nic_entry in nic_history:
            mac = nic_entry.get("mac_address")
            if mac and mac not in mac_addresses:
                mac_addresses.append(mac)
                ip = nic_entry.get("ip_address")
                if ip and ip not in ip_addresses:
                    ip_addresses.append(ip)

    return Host(
        source_id=host_data["device_id"],
        group_names=to_list(host_data.get("groups")),
        hostname=hostname,
        _domain=host_data.get("machine_domain"),
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("last_seen")),
        source_data=host_data,
    )


class CrowdstrikeFalconV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api: CrowdstrikeFalconV1Api = self.integration.get_api()

        # Get all devices
        all_ids = api.get_all_device_ids()
        for ids_chunk in chunks(all_ids, 5000):
            devices = api.get_devices(ids_chunk)
            for devices_chunk in chunks(devices, 500):
                # Add NIC history to the devices.
                # Build a lookup table by device ID for mapping the NIC history results.
                devices_by_id = {d["device_id"]: d for d in devices_chunk}
                lookup_ids = list(devices_by_id.keys())
                nic_history_result = api.get_nic_history(device_ids=lookup_ids)
                for nic_history in nic_history_result:
                    device_id = nic_history["device_id"]
                    device = devices_by_id[device_id]
                    device["nic_history"] = nic_history["history"]

            yield from devices

    def get_permission_checks(self):
        return []
