from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CrowdstrikeFalconV1TemplateVersion


class CrowdstrikeFalconTemplate(Template):
    id = "crowdstrike_falcon"
    name = "CrowdStrike Falcon"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CrowdstrikeFalconV1TemplateVersion.id: CrowdstrikeFalconV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.CROWDSTRIKE
