from apps.connectors.integrations import Integration

from .actions import (
    CrowdstrikeFalconIpAddHostToWatchlist,
    CrowdstrikeFalconIpAddUserToWatchlist,
    CrowdstrikeFalconIpEventSync,
    CrowdstrikeFalconIpGetHostInfo,
    CrowdstrikeFalconIpGetUserInfo,
    CrowdstrikeFalconIpRemoveHostFromWatchlist,
    CrowdstrikeFalconIpRemoveUserFromWatchlist,
    CrowdstrikeIpAddAlertComment,
    CrowdstrikeIpUpdateLifecycleStatus,
)
from .api import CrowdstrikeFalconIpV1Api
from .health_check import (
    ConnectionHealthCheck,
    ReadIdentityProtectionEntities,
)


class CrowdstrikeFalconIpV1Integration(Integration):
    api_class = CrowdstrikeFalconIpV1Api
    actions = (
        CrowdstrikeFalconIpAddHostToWatchlist,
        CrowdstrikeFalconIpAddUserToWatchlist,
        CrowdstrikeFalconIpEventSync,
        CrowdstrikeFalconIpGetUserInfo,
        CrowdstrikeIpAddAlertComment,
        CrowdstrikeIpUpdateLifecycleStatus,
        CrowdstrikeFalconIpGetHostInfo,
        CrowdstrikeFalconIpRemoveHostFromWatchlist,
        CrowdstrikeFalconIpRemoveUserFromWatchlist,
    )
    critical_health_checks = (
        ConnectionHealthCheck,
        ReadIdentityProtectionEntities,
    )
