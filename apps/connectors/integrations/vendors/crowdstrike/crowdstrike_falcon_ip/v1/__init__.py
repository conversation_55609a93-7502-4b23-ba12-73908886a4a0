from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.crowdstrike.connection import (
    CrowdstrikeConnection,
)

from .bookmarks import CrowdstrikeFalconIpV1Bookmarks
from .integration import CrowdstrikeFalconIpV1Integration
from .settings import CrowdstrikeFalconIpV1Settings


class CrowdstrikeFalconIpV1TemplateVersion(TemplateVersion):
    integration = CrowdstrikeFalconIpV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = CrowdstrikeConnection
    settings_model = CrowdstrikeFalconIpV1Settings
    bookmarks_model = CrowdstrikeFalconIpV1Bookmarks
