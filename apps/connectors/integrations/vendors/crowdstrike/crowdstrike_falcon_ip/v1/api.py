from urllib.parse import urljoin

import requests

from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.v1.api import (
    CrowdstrikeFalconV1Api,
)


class CrowdstrikeFalconIpV1Api(CrowdstrikeFalconV1Api):
    """
    Crowdstrike Falcon IP API
    Inherits from CrowdstrikeFalconV1Api to use the fetch_alerts method
    """

    GRAPHQL_PATH = "/identity-protection/api/graphql"

    def __init__(self, client_id=None, client_secret=None, **kwargs):
        super().__init__(client_id=client_id, client_secret=client_secret, **kwargs)
        self.GRAPHQL_ENDPOINT = urljoin(self.BASE_URL, self.GRAPHQL_PATH)

    def execute_graphql_query(self, query, variables=None):
        """
        Execute a GraphQL query against the Crowdstrike Falcon Identity Protection API

        Args:
            query (str): The GraphQL query to execute
            variables (dict, optional): Variables to include in the query

        Returns:
            dict: The response from the GraphQL API
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth.token()}",
        }

        payload = {"query": query}
        if variables:
            payload["variables"] = variables

        response = requests.post(self.GRAPHQL_ENDPOINT, json=payload, headers=headers)

        result = response.json()

        return result

    def get_user_info(self, user_id):
        """
        Get information about a user from Crowdstrike Falcon Identity Protection.

        Args:
            user_id (str): The user identifier to fetch information for

        Returns:
            dict: The user information or None if not found
        """
        query = """
        {
            "query": "query { entities(entityIds: \\"<user_id>\\" first: 1) { nodes { entityId type primaryDisplayName riskScore riskScoreSeverity ...on UserEntity { emailAddresses } } } }"
        }
        """

        # Replace the user_id placeholder in the query
        query = query.replace("<user_id>", user_id)
        response = self.execute_graphql_query(query)

        # Extract the user information
        user_data = response.get("data", {}).get("entities", {}).get("nodes", [])
        return user_data[0] if user_data else None

    def get_host_info(self, host_id):
        """
        Get information about a host from Crowdstrike Falcon Identity Protection.

        Args:
            host_id (str): The host identifier to fetch information for

        Returns:
            dict: The host information or None if not found
        """
        query = """
        {
            "query": "query { entities(entityIds: \\"<host_id>\\" first: 1) { nodes { entityId type primaryDisplayName ...on EndpointEntity { hostName staticIpAddresses agentId agentVersion targetType operatingSystemInfo { displayName family name servicePack version } } } } }"
        }
        """

        # Replace the host_id placeholder in the query
        query = query.replace("<host_id>", host_id)
        response = self.execute_graphql_query(query)

        # Extract the host information
        host_data = response.get("data", {}).get("entities", {}).get("nodes", [])
        return host_data[0] if host_data else None
