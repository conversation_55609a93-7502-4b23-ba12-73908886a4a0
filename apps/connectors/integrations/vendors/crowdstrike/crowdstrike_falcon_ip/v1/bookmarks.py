from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_alert_creation_time():
    return datetime.now(timezone.utc) - timedelta(days=1)


class CrowdstrikeFalconIpV1EventSyncBookmark(TemplateVersionActionBookmark):
    last_event_ingested: datetime = Field(
        title="Latest Event Update Datetime",
        description="The latest Event Timestamp received during a fetch.",
        default_factory=default_alert_creation_time,
    )


CrowdstrikeFalconIpV1Bookmarks = create_bookmarks_model(
    "CrowdstrikeFalconIpV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: CrowdstrikeFalconIpV1EventSyncBookmark,
    },
)
