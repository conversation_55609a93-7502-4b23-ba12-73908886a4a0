from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.template import TemplateVersionActionSettings


class CrowdstrikeFalconIpV1WatchlistSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(
        title="Crowdstrike Falcon Identity Protection Watchlist Settings",
    )

    watchlist_name: str = Field(
        title="Watchlist Name",
        description="Name of the watchlist to add hosts to.",
    )


CrowdstrikeFalconIpV1Settings = create_settings_model(
    "CrowdstrikeFalconIpV1Settings",
    {},
)
