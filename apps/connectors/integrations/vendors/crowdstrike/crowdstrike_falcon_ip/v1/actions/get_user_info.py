import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions.user import GetUserInfo, UserInfoResult
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadIdentityProtectionEntities,
)


def map_risk_level(severity: str) -> int:
    """Map Crowdstrike risk severity to OCSF risk level ID."""
    match severity:
        case "NORMAL":
            return ocsf.RiskLevel.LOW
        case "MEDIUM":
            return ocsf.RiskLevel.MEDIUM
        case "HIGH":
            return ocsf.RiskLevel.HIGH
        case _:
            return ocsf.RiskLevel.OTHER


def normalize_user(user: dict) -> ocsf.User:
    """
    Normalize a user entity from Crowdstrike Falcon Identity Protection.
    Maps the raw user data to OCSF format according to the mapping table.
    """
    return ocsf.User(
        uid=user.get("entityId", ""),
        full_name=user.get("primaryDisplayName"),
        email_addr=user.get("emailAddresses", [None])[0],
        risk_score=user.get("riskScore"),
        risk_level_id=map_risk_level(user.get("riskScoreSeverity")),
    )


class CrowdstrikeFalconIpGetUserInfo(GetUserInfo):
    """
    Get information about a user from Crowdstrike Falcon Identity Protection.
    This action uses the GraphQL API to fetch user entity information.
    """

    def execute(self, args: UserIdentifierArgs) -> UserInfoResult:
        """
        Execute the get user info action.
        This method fetches user data from the GraphQL API and returns it as a normalized user.
        """
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()
        user = api.get_user_info(args.user_id.value)
        if not user:
            return UserInfoResult(
                error=ErrorDetail(message=f"User {args.user_id.value} not found")
            )
        return UserInfoResult(result=normalize_user(user))

    def get_permission_checks(self):  # pragma: no cover
        return [ReadIdentityProtectionEntities]
