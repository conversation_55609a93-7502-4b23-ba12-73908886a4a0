import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions import to_list
from apps.connectors.integrations.actions.host import GetHostInfoAction, HostInfoResult
from apps.connectors.integrations.schemas.identifiers.host_identifier import (
    HostIdentifierArgs,
)
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadIdentityProtectionEntities,
)


def map_operating_system(os_family: str) -> ocsf.enums.OSType:
    """Map the operating system information to the OCSF OperatingSystem model."""

    match os_family:
        case "WINDOWS":
            return ocsf.enums.OSType.WINDOWS
        case "OSX":
            return ocsf.enums.OSType.MACOS
        case "UNIX":
            return ocsf.enums.OSType.LINUX
        case "LINUX":
            return ocsf.enums.OSType.LINUX
        case "IOS":
            return ocsf.enums.OSType.IOS
        case "ANDROID":
            return ocsf.enums.OSType.ANDROID
        case "OTHER":
            return ocsf.enums.OSType.OTHER


def map_target_type(target_type: str) -> ocsf.EndpointType:
    """Map the target type to the OCSF EndpointType model."""
    match target_type:
        case "WORKSTATION":
            return ocsf.EndpointType.LAPTOP
        case "SERVER":
            return ocsf.EndpointType.SERVER
        case "INTEGRATED_SOLUTION_APPLIANCE":
            return target_type
        case "MOBILE":
            return ocsf.EndpointType.MOBILE
        case "TABLET":
            return ocsf.EndpointType.TABLET
        case "GAME_CONSOLE":
            return target_type
        case "WEARABLE":
            return target_type
        case "SMART_TV":
            return target_type
        case "PDA":
            return target_type
        case "UNDETERMINED":
            return ocsf.EndpointType.UNKNOWN
        case _:
            return target_type


def normalize_host(host_data: dict) -> ocsf.NetworkEndpoint:
    """
    Normalize host data from Crowdstrike Falcon Identity Protection to NetworkEndpoint model.

    Args:
        host_data: The host data from the GraphQL API response

    Returns:
        NetworkEndpoint: A normalized NetworkEndpoint object
    """
    ip_addresses = to_list(host_data.get("staticIpAddresses", []))
    ip_address = ip_addresses[0] if ip_addresses else None

    # Extract OS information
    os_info = host_data.get("operatingSystemInfo", {})
    os_family = os_info.get("family", "")
    os_name = os_info.get("name", "")
    os_sp_name = os_info.get("servicePack", "")

    # Create and return the NetworkEndpoint object
    return ocsf.NetworkEndpoint(
        uid=host_data.get("entityId", ""),
        agent_list=(
            [
                ocsf.Agent(
                    uid=host_data.get("agentId", ""),
                    type_id=ocsf.AgentType.ENDPOINT_DETECTION_AND_RESPONSE.value[0],
                )
            ]
            if host_data.get("agentId")
            else [
                ocsf.Agent(
                    version=host_data.get("agentVersion", ""),
                    type_id=ocsf.AgentType.ENDPOINT_DETECTION_AND_RESPONSE.value[0],
                )
            ]
        ),
        os=ocsf.OperatingSystem(
            name=os_name,
            type=map_operating_system(os_family),
            sp_name=os_sp_name,
            version=os_info.get("version", ""),
        ),
        type=map_target_type(host_data.get("targetType")),
        name=(
            host_data.get("hostName", "")
            if host_data.get("hostName")
            else host_data.get("primaryDisplayName", "")
        ),
        ip=ip_address,
    )


class CrowdstrikeFalconIpGetHostInfo(GetHostInfoAction):
    """
    Get information about a host from Crowdstrike Falcon Identity Protection.

    This action uses the GraphQL API to fetch host entity information.
    """

    def execute(self, args: HostIdentifierArgs) -> HostInfoResult:
        """
        Execute the get host info action.
        This method fetches host data from the GraphQL API and returns it as a HostInfoResult.
        """
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()
        host = api.get_host_info(args.host.value)

        if not host:
            return HostInfoResult(
                error=ErrorDetail(message=f"Host {args.host.value} not found")
            )

        return HostInfoResult(result=normalize_host(host))

    def get_permission_checks(self):  # pragma: no cover
        return [ReadIdentityProtectionEntities]
