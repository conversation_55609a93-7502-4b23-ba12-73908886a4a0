from apps.connectors.integrations.actions.user.user_watchlist import (
    AddUserToWatchlist,
    AddUserToWatchlistResult,
)
from apps.connectors.integrations.schemas import Message, UserIdentifierArgs
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadIdentityProtectionEntities,
)


class CrowdstrikeFalconIpAddUserToWatchlist(AddUserToWatchlist):
    """
    Add a user to the watchlist in Crowdstrike Falcon Identity Protection.

    This action uses the GraphQL API to add a user entity to the watchlist.
    """

    def execute(self, args: UserIdentifierArgs, **kwargs) -> AddUserToWatchlistResult:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()

        # Extract the user ID from the args
        user_id = args.user_id.value

        # Construct the GraphQL mutation with variables
        mutation = """
        mutation AddUserToWatchlist($entityId: String!) {
            addEntitiesToWatchList(input: {
                entityQuery: {
                    entityIds: $entityId
                }
            }) {
                updatedEntities {
                    primaryDisplayName
                    secondaryDisplayName
                    watched
                }
                failures {
                    entityIds
                    errorDetails {
                        message
                    }
                }
            }
        }
        """

        # Execute the mutation with variables
        variables = {"entityId": user_id}
        api.execute_graphql_query(mutation, variables)

        # Return a Message in the result as required
        return AddUserToWatchlistResult(
            result=Message(message=f"User {user_id} added to watchlist.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return [ReadIdentityProtectionEntities]
