from apps.connectors.integrations.actions import UpdateLifecycleStatus
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadAlerts,
)


class CrowdstrikeIpUpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()
        api.alerts.update_alerts_v3(
            composite_ids=[args.vendor_sync_id],
            update_status=self.map_corr_incident_status(args.status),
        )

        return UpdateLifecycleStatusResult()

    @staticmethod
    def map_corr_incident_status(status: CorrIncidentStatus) -> str:
        return {
            # CODE REVIEW QUESTION: In this case, if a customer has already
            # started working on this, and then we ingest it into CORR, wouldn't
            # emitting a "New" status result in us clobbering the status of the remote
            # object?
            CorrIncidentStatus.NEW: "new",
            CorrIncidentStatus.ASSIGNED: "in_progress",
            CorrIncidentStatus.REVIEWING: "in_progress",
            CorrIncidentStatus.MITIGATED: "closed",
            CorrIncidentStatus.CLOSED: "closed",
        }[status]

    def get_permission_checks(self):
        return [ReadAlerts]  # pragma: no cover
