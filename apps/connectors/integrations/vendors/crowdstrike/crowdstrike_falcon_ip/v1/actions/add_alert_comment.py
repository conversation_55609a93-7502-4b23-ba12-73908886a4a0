from apps.connectors.integrations.actions.add_alert_comment import (
    AddAlertComment,
    AddAlertCommentArgs,
    AddAlertCommentResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.api import (
    CrowdstrikeFalconIpV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.v1.health_check import (
    ReadAlerts,
)


class CrowdstrikeIpAddAlertComment(AddAlertComment):
    def execute(self, args: AddAlertCommentArgs, **kwargs) -> AddAlertCommentResult:
        api: CrowdstrikeFalconIpV1Api = self.integration.get_api()
        api.alerts.update_alerts_v3(
            composite_ids=[args.vendor_sync_id],
            append_comment=args.comment,
        )
        return AddAlertCommentResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAlerts]  # pragma: no cover
