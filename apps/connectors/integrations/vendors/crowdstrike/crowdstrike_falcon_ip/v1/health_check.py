from datetime import timedelta

from django.utils.timezone import now
from falconpy._error import SDKError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.constants import (
    CrowdstrikeProducts,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon_ip.utils import (
    generate_fql_filter,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        # The FalconPy client API tries to initialize and get an OAuth token during
        # __init__(). Internally when it's authenticating it's not in "pythonic" mode,
        # thus, no exception is raised on HTTP error
        api = self.integration.get_api()
        if api.auth.bearer_token.status == 201:
            return IntegrationHealthCheckResult.PASSED
        else:
            return IntegrationHealthCheckResult.FAILED


class ReadAlerts(IntegrationHealthCheck):
    name = "Read Alerts"
    description = "Read alerts from Crowdstrike"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        since = now() - timedelta(hours=1)
        try:
            api = self.integration.get_api()

            body = {
                "filter": generate_fql_filter(
                    products=[CrowdstrikeProducts.IDP],
                    created_after=since,
                ),
                "sort": "created_timestamp.asc",
                "limit": 1,
            }
            # Will only be one item
            list(api.fetch_alerts(body=body))
            return IntegrationHealthCheckResult.PASSED
        except SDKError:
            return IntegrationHealthCheckResult.FAILED


class ReadIdentityProtectionEntities(IntegrationHealthCheck):
    name = "Read Identity Protection Entities"
    description = "Read entities from Crowdstrike Identity Protection"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        # Simple query to test if we can access the GraphQL API
        query = """
        query GetEntities($first: Int!) {
            entities(first: $first) {
                nodes {
                    entityId
                    type
                }
            }
        }
        """
        variables = {"first": 1}
        api.execute_graphql_query(query, variables)

        return IntegrationHealthCheckResult.PASSED
