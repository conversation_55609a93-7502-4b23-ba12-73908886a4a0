from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CrowdstrikeFalconIpV1TemplateVersion


class CrowdstrikeFalconIpTemplate(Template):
    id = "crowdstrike_falcon_ip"
    name = "CrowdStrike Falcon Identity Protection"
    category = Template.Category.IDENTITY_SECURITY
    versions = {
        CrowdstrikeFalconIpV1TemplateVersion.id: CrowdstrikeFalconIpV1TemplateVersion(),
    }
    vendor = Vendors.CROWDSTRIKE
