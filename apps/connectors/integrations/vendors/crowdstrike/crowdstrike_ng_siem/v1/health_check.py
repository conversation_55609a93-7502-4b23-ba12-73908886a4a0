from datetime import timedelta

from django.utils.timezone import now
from falconpy._error import SDKError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon import (
    constants,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.constants import (
    SUPPORTED_INGEST_PRODUCTS,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.utils import (
    generate_fql_filter,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        # The FalconPy client API tries to initialize and get an OAuth token during
        # __init__(). Internally when it's authenticating it's not in "pythonic" mode,
        # thus, no exception is raised on HTTP error
        api = self.integration.get_api()
        if api.auth.bearer_token.status == 201:
            return IntegrationHealthCheckResult.PASSED
        else:
            return IntegrationHealthCheckResult.FAILED


class ReadAlerts(IntegrationHealthCheck):
    name = "Read Alerts"
    description = "Read alerts from Crowdstrike"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        since = now() - timedelta(hours=1)
        try:
            api = self.integration.get_api()

            body = {
                "filter": generate_fql_filter(
                    products=SUPPORTED_INGEST_PRODUCTS,
                    created_after=since,
                ),
                "sort": "created_timestamp.asc",
                "limit": 1,
            }
            # Will only be one item
            list(api.fetch_alerts(body=body))
            return IntegrationHealthCheckResult.PASSED
        except SDKError:
            return IntegrationHealthCheckResult.FAILED


class ReadCorrelationRules(IntegrationHealthCheck):
    name = "Read Correlation Rules"
    description = "Read Correlation Rules from Crowdstrike"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            api = self.integration.get_api()
            api.correlation_rules.get_rules_combined()
            return IntegrationHealthCheckResult.PASSED
        except SDKError:
            return IntegrationHealthCheckResult.FAILED


class WriteCorrelationRules(IntegrationHealthCheck):
    name = "Write Correlation Rules"
    description = "Write Correlation Rules to Crowdstrike"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            start_time = now() + timedelta(days=365 * 5)
            search_dictionary = {
                "outcome": "detection",
                "filter": '#event.kind="alert" (#event.module="falcon" OR #event.module="identity-protection")\n| case{\n #event.module="identity-protection" | user.id:=source.user.id; *\n}\n| selfJoinFilter([user.id], where=[\n // Endpoint detection with a high severity\n {#event.module="falcon" event.severity\u003e=70},\n // Identity detection with an information severity\n {#event.module="identity-protection" event.severity\u003e=30}\n], prefilter=true )\n',
                "lookback": "1h0m",
            }
            operation_dictionary = {
                "schedule": {"definition": "@every 1h0m"},
                "start_on": start_time.strftime(constants.DATE_FORMAT),
                "end_on": start_time.strftime(constants.DATE_FORMAT),
            }
            api = self.integration.get_api()
            cid = self.integration.config["cid"]
            response = api.correlation_rules.create_rule(
                customer_id=cid,
                name="Critical Start Integration - Create Rule Test",
                description="A test rule created by automated health checks",
                operation=operation_dictionary,
                severity=10,
                notifications=[],
                search=search_dictionary,
                status="inactive",
                trigger_on_create=False,
            )

            rule_id = response.resources[0]["id"]
            api.correlation_rules.delete_rules(ids=[rule_id])
            return IntegrationHealthCheckResult.PASSED
        except SDKError:
            return IntegrationHealthCheckResult.FAILED


class NGSiemExecuteSearch(IntegrationHealthCheck):
    name = "NGSIEM - Execute Search"
    description = "Execute Searches against Advanced Event Search"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        repository = "search-all"
        search = {
            "isLive": False,
            "start": "5m",
            "queryString": "head(1)",
        }
        api = self.integration.get_api()
        try:
            response = api.ngsiem.start_search(repository=repository, search=search)
            # It seems this API class doesn't support Pythonic results; thus we interact
            # with raw instead. See https://falconpy.io/Usage/Response-Handling.html#pythonic-responses
            search_id = response.raw["id"]
            # We should only get one response - memory footprint should be small.
            list(
                api.ngsiem.get_search_status(repository=repository, search_id=search_id)
            )
            return IntegrationHealthCheckResult.PASSED
        except SDKError:
            return IntegrationHealthCheckResult.FAILED
