from falconpy._error._exceptions import SDKError

from apps.connectors.integrations import Integration

from .actions import (
    CrowdstrikeNgSiemAddAlertComment,
    CrowdstrikeNgSiemDeleteIoc,
    CrowdstrikeNgSiemEventSync,
    CrowdstrikeNgSiemListIocs,
    CrowdstrikeNgSiemSaveIoc,
    CrowdstrikeNgSiemUpdateLifecycleStatus,
    CrowdstrikeNgSiemV1ListDataSources,
)
from .api import CrowdstrikeNgSiemV1Api
from .health_check import (
    ConnectionHealthCheck,
)


class CrowdstrikeNgSiemV1Integration(Integration):
    api_class = CrowdstrikeNgSiemV1Api

    known_exception_types = (SDKError,)

    actions = (
        CrowdstrikeNgSiemAddAlertComment,
        CrowdstrikeNgSiemEventSync,
        CrowdstrikeNgSiemListIocs,
        CrowdstrikeNgSiemDeleteIoc,
        CrowdstrikeNgSiemSaveIoc,
        CrowdstrikeNgSiemUpdateLifecycleStatus,
        CrowdstrikeNgSiemV1ListDataSources,
    )
    critical_health_checks = (ConnectionHealthCheck,)
