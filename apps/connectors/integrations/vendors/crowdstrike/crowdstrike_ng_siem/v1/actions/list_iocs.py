from typing import Generator

from apps.connectors.integrations.actions.ioc.list_iocs import ListIocs, ListIocsArgs
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC, IOCStatus
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.api import (
    CrowdstrikeNgSiemV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    ReadCorrelationRules,
)


def normalize_status(status: str) -> IOCStatus:
    if status == "active":
        return IOCStatus.ACTIVE
    else:
        return IOCStatus.INACTIVE


def normalize_ioc(ioc: dict) -> IOC:
    return IOC(
        source_data=ioc,
        source_id=ioc["rule_id"],
        status=normalize_status(ioc["status"]),
        title=ioc["name"],
        description=ioc.get("description", ""),
        query=ioc["search"]["filter"],
    )


class CrowdstrikeNgSiemListIocs(ListIocs):
    @normalize(normalize_ioc)
    def execute(self, args: ListIocsArgs, **kwargs) -> Generator[IOC, None, None]:
        api: CrowdstrikeNgSiemV1Api = self.integration.get_api()
        rules = api.correlation_rules.get_rules_combined_v2()
        for rule in rules:
            yield rule

    def get_permission_checks(self, *args, **kwargs):
        return [ReadCorrelationRules]
