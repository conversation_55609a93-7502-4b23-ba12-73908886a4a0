import logging
from datetime import datetime, timezone
from typing import Generator

from apps.connectors.integrations.actions.ioc.list_data_sources import (
    DataSource,
    DataSourceType,
    ListDataSources,
    ListDataSourcesArgs,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.api import (
    CrowdstrikeNgSiemV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    NGSiemExecuteSearch,
)

logger = logging.getLogger(__name__)


def convert_epoch_to_datetime(epoch: str) -> str:
    return datetime.fromtimestamp(float(epoch) / 1000.0, tz=timezone.utc).isoformat()


def normalize_data_source(src: dict) -> DataSource:
    # {'data_source': 'okta.okta-sso', '_count': '20', '_max': '1746637200000'},
    return DataSource(
        source_type=DataSourceType.SOURCE_TYPE,
        source_name=src["data_source"],
        last_log_time=convert_epoch_to_datetime(src["_max"]),
        log_count=src["_count"],
    )


class CrowdstrikeNgSiemV1ListDataSources(ListDataSources):
    @normalize(normalize_data_source)
    def execute(
        self, args: ListDataSourcesArgs, **kwargs
    ) -> Generator[DataSource, None, None]:
        api: CrowdstrikeNgSiemV1Api = self.integration.get_api()

        search = self.generate_query(args.start_time, args.end_time)
        repository = "search-all"
        response = api.ngsiem.start_search(repository=repository, search=search)
        search_id = response.raw["id"]
        logger.debug(f"Started search - {search_id}")
        done = False
        events = []
        while not done:
            status = api.ngsiem.get_search_status(
                repository=repository, search_id=search_id
            )
            done = status.raw["done"]
            if done:
                events = status.raw["events"]

        logger.debug("Normalizing results...")
        for record in events:
            # We ignore zeros to save space in ES
            if record["_count"] == "0":
                continue
            yield record

    def generate_query(self, start: str, end: str):
        start_datetime = datetime.fromisoformat(start)
        end_datetime = datetime.fromisoformat(end)

        ignored_repos_list = [
            # Ignore built-in falcon log data
            "base_sensor",
            "sensor_backup",
            "detections",
            "falcon_for_it",
            "fusion",
            "fcs_csp_events",
            "sensor_metadata",
            "xdr_eventsrepo",
            "xdr_indicatorsrepo",
            "xdr_eventsarchive",
            # This is a repo that is created any-time a customer onboards a generic
            # source like a "Generic CEF" / "Generic HEC" - it's not tied to an actual
            # per product source
            "3pi_auto_raptor*",
        ]
        ignore_list = " or ".join([f"#repo={item}" for item in ignored_repos_list])
        query = (
            f"#repo=* AND NOT ({ignore_list})"
            '| #type!="metadata-parser"'
            # We ignore log messages with parsing errors - because their
            # computed data_source field will not be well-formed.
            "| #error!=true"
            '| separator := "."'
            '| concat(field=[  #Vendor, separator, #type], as="data_source")'
            "| groupBy(field=[data_source], function=[max(@ingesttimestamp), count()])"
        )
        return {
            "timezone": "utc",
            "queryString": query,
            # Backend uses this (presumably) to know if it should prioritize this for an
            # interactive user or not.
            "isLive": False,
            # We use ingest instead of start/end so that we are looking at the ingest
            # time of the log, rather than the parsed time out of the log source
            "ingest_start": start_datetime.timestamp() * 1000,
            "ingest_end": end_datetime.timestamp() * 1000,
        }

    def get_permission_checks(self, *args, **kwargs):
        return [NGSiemExecuteSearch]
