from apps.connectors.integrations.actions.ioc.delete_ioc import (
    <PERSON>eteI<PERSON>,
    <PERSON>eteIocArg<PERSON>,
    DeleteIocResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.api import (
    CrowdstrikeNgSiemV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    ReadCorrelationRules,
    WriteCorrelationRules,
)


class CrowdstrikeNgSiemDeleteIoc(DeleteIoc):
    def execute(self, args: DeleteIocArgs, **kwargs) -> DeleteIocResult:
        api: CrowdstrikeNgSiemV1Api = self.integration.get_api()
        api.correlation_rules.delete_rules(ids=[args.source_id])

        return DeleteIocResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadCorrelationRules, WriteCorrelationRules]
