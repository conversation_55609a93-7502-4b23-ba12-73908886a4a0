from apps.connectors.integrations.actions.ioc.save_ioc import (
    <PERSON><PERSON><PERSON>,
    SaveIocResult,
)
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC, IOCStatus
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.api import (
    CrowdstrikeNgSiemV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    ReadCorrelationRules,
    WriteCorrelationRules,
)


def denormalize_status(status: IOCStatus):
    if status == IOCStatus.ACTIVE:
        return "active"
    return "inactive"


class CrowdstrikeNgSiemSaveIoc(SaveIoc):
    def execute(self, args: IOC, **kwargs) -> SaveIocResult:
        api: CrowdstrikeNgSiemV1Api = self.integration.get_api()

        source_id = args.source_id
        cid = self.integration.config["cid"]
        status = denormalize_status(args.status)

        kwargs = {
            "customer_id": cid,
            "name": args.title,
            "description": args.description,
            "search": args.source_data.get("search"),
            "status": status,
            "operation": args.source_data.get("operation"),
            "severity": args.source_data.get("severity"),
            "notifications": [],
            # For now we don't want to trigger any rules on creation. This would
            # result in a lot of Alerts with possibly "old" detection times coming
            # into CORR. This is the same thing we do for Palo XDR
            "trigger_on_create": False,
        }
        if source_id:
            result = api.correlation_rules.update_rule(id=source_id, **kwargs)
        else:
            result = api.correlation_rules.create_rule(**kwargs)
        return SaveIocResult(source_id=result.resources[0]["rule_id"])

    def get_permission_checks(self, *args, **kwargs):
        return [ReadCorrelationRules, WriteCorrelationRules]
