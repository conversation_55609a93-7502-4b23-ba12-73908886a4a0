import logging
from datetime import datetime
from typing import Generator

import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.constants import (
    SUPPORTED_INGEST_PRODUCTS,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.utils import (
    generate_fql_filter,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.bookmarks import (
    CrowdstrikeNgSiemV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    ReadAlerts,
)

logger = logging.getLogger(__name__)


def get_mitre_ttp(event: dict) -> list[str]:
    mitre_tactic = event.get("tactic_id")
    mitre_technique = event.get("technique_id")
    mitre = []
    if mitre_tactic and mitre_technique:
        # FIXME: Discuss with team - sounds like CRWD has their own MITRE fork :(
        if mitre_tactic.startswith("TA") and mitre_technique.startswith("T"):
            mitre.append(f"{mitre_tactic}.{mitre_technique}")
    return mitre


def normalize_crowdstrike_alert(event: dict) -> Event:
    def map_severity(severity):
        # Per crowdstrike correlation rule API docs:
        # The severity integer of the rule. Available options are:
        #
        #     90: Critical
        #     70: High
        #     50: Medium
        #     30: Low
        #     10: Informational
        if not severity:
            return ocsf.Severity.UNKNOWN
        if severity <= 10:
            return ocsf.Severity.INFORMATIONAL
        elif severity <= 30:
            return ocsf.Severity.LOW
        elif severity <= 50:
            return ocsf.Severity.MEDIUM
        elif severity <= 70:
            return ocsf.Severity.HIGH
        elif severity >= 90:
            return ocsf.Severity.CRITICAL

    def map_evidences(event: dict) -> list[ocsf.EvidenceArtifacts]:
        evidences = []

        # It is *technically* possible that we could create a single EvidenceArtifact
        # from a separate IP and Host, but we have no guarantee (and no way to know
        # for sure) without introspecting the IOC that generated the Event. For now,
        # keep them separate.
        #
        # If the customer creates a rule with:
        #   - detailed mode - we'll get one event per hit
        #   - Summary mode - we'll get a event with a list of hits
        source_hosts = event.get("source_hosts", [])
        for host in source_hosts:
            evidences.append(
                ocsf.EvidenceArtifacts(src_endpoint=ocsf.NetworkEndpoint(hostname=host))
            )
        for ip in event.get("source_ips", []):
            evidences.append(
                ocsf.EvidenceArtifacts(src_endpoint=ocsf.NetworkEndpoint(ip=ip))
            )
        known_ip_source_fields = [
            "local_address_ip4",
            "local_address_ip6",
            "source_endpoint_address_ip4",
            "source_endpoint_address_ip6",
        ]
        # These often are duplicated, so use a set
        local_ip_addresses = set()
        for field in known_ip_source_fields:
            if address := event.get(field):
                local_ip_addresses.add(address)

        for local_ip in local_ip_addresses:
            evidences.append(
                ocsf.EvidenceArtifacts(src_endpoint=ocsf.NetworkEndpoint(ip=local_ip))
            )

        known_ip_dest_fields = [
            "remote_address_ip4",
            "remote_address_ip6",
            "target_endpoint_address_ip4",
            "target_endpoint_address_ip6",
        ]
        remote_ip_addresses = set()
        for field in known_ip_dest_fields:
            if address := event.get(field):
                remote_ip_addresses.add(address)

        for remote_ip in remote_ip_addresses:
            evidences.append(
                ocsf.EvidenceArtifacts(dst_endpoint=ocsf.NetworkEndpoint(ip=remote_ip))
            )

        # This is present for EDR events
        uid = event.get("user_id")
        source_username = event.get("user_name")
        if not uid:
            # This is present for some types of third-party direct ingest alerts (eg:
            # Okta)
            uid = event.get("source_account_object_sid")
        if uid:
            evidences.append(
                ocsf.EvidenceArtifacts(user=ocsf.User(name=source_username, uid=uid))
            )

        # This is present for some types of third-party alerts (eg: Okta)
        usernames = event.get("usernames", [])
        for username in usernames:
            evidences.append(ocsf.EvidenceArtifacts(user=ocsf.User(name=username)))

        if user_agent := event.get("user_agent"):
            evidences.append(
                ocsf.EvidenceArtifacts(
                    http_request=ocsf.HttpRequest(user_agent=user_agent)
                )
            )
        return evidences

    def map_analytic(event: dict):
        if event.get("type") == "correlation-detection":
            return ocsf.Analytic(
                uid=event.get("correlation_rule_id"),
                type=ocsf.AnalyticType.RULE,
                name=event.get("display_name"),
            )
        elif event.get("type") == "xdr":
            # Force these events to be under a single IOCTemplate. We won't monitor them
            # but this way they'll be grouped together and the SecEng team can route it
            # atomically. The customer may still wish to investigate in CORR
            if event.get("xdr_type") == "xdr-one-time-detection":
                return ocsf.Analytic(
                    uid="xdr-one-time-detection",
                    name="One Time Detection (Manually Generated)",
                    type=ocsf.AnalyticType.OTHER,
                )
        elif event.get("type") == "thirdparty":
            return ocsf.Analytic(
                # Generally speaking - NG SIEM takes the vendor's name and uses it.
                # For palo they have added their own name
                #  Okta - User report suspicious activity
                #  Palo NGFW - Intrusion_detection: Allowed Alert
                uid=event.get("name"),
                name=event.get("name"),
                # This will vary by the alert source - so we just leave it to Unknown
                # for the time being.
                type=ocsf.AnalyticType.UNKNOWN,
            )

        return None

    composite_id = event.get("composite_id")
    created_time = datetime.fromisoformat(event.get("created_timestamp"))
    falcon_host_link = event.get("falcon_host_link")
    modified_time = datetime.fromisoformat(event.get("updated_timestamp"))
    severity = map_severity(event.get("severity"))
    source_products = event.get("source_products", [])
    source_vendors = event.get("source_vendors", [])
    title = event.get("name")
    typ = event.get("type")

    product = ocsf.Product(
        # FIXME: In Crowdstrike events this can be an Array. In the case
        #  where the source is from multiple products (eg: correlation rule)
        #  what should we put here?
        name=source_products[0] if len(source_products) > 0 else None,
        vendor_name=source_vendors[0] if len(source_vendors) > 0 else None,
        feature=ocsf.Feature(name=typ) if typ else None,
    )

    # The composite ID is basically Alert ID
    correlation_uid = composite_id
    event_code = composite_id
    message = event.get("name")
    if typ == "ldt":
        # ldt is short for legacy detection
        correlation_uid = event.get("device", {}).get("hostname")
        event_code = event.get("indicator_id", composite_id)
        message = event.get("name")
        if ioa_rule_name := event.get("rule_instance_name"):
            message = ioa_rule_name
    elif typ == "correlation-detection":
        correlation_uid = event.get("correlation_rule_id")
        event_code = event.get("correlation_rule_id")
        message = event.get("name")
    elif typ == "thirdparty":
        correlation_uid = composite_id
        event_code = event.get("name")
        message = event.get("name")

    analytic = map_analytic(event)
    mitre = get_mitre_ttp(event)
    if analytic:
        has_ioc_definition = typ == "correlation-detection"
        ioc = EventIOCInfo(
            external_id=analytic.uid,
            external_name=analytic.name,
            has_ioc_definition=has_ioc_definition,
            mitre_techniques=mitre,
        )
    else:
        # This will cause anything that is third-party to have a new IOC created.
        # We will probably have to revisit this as we learn more about the third
        # party types.
        ioc = EventIOCInfo(
            external_id=f"{typ}.{event_code}",
            external_name=message,
            has_ioc_definition=False,
            mitre_techniques=mitre,
        )

    detection_finding = ocsf.DetectionFinding(
        activity=ocsf.DetectionActivity.CREATE,
        evidences=map_evidences(event),
        finding_info=ocsf.FindingInformation(
            analytic=analytic,
            desc=event.get("description"),
            product=product,
            related_events=[
                ocsf.RelatedEventFinding(uid=event_id)
                for event_id in event.get("related_event_ids", [])
            ],
            title=title,
            types=event.get("data_domains"),
            uid=composite_id,
        ),
        message=message,
        metadata=ocsf.Metadata(
            correlation_uid=correlation_uid,
            event_code=event_code,
            processed_time_dt=created_time,
            product=product,
            profiles=[
                ocsf.Profile.DATETIME,
                ocsf.Profile.INCIDENT,
            ],
            modified_time_dt=modified_time,
            tenant_uid=event.get("cid"),
            uid=composite_id,
        ),
        src_url=falcon_host_link,
        severity=severity.name,
        time_dt=created_time,
        vendor_attributes=ocsf.VendorAttributes(
            severity=severity.name, severity_id=severity.id
        ),
    )
    return Event(
        raw_event=event,
        event_timestamp=created_time,
        ioc=ioc,
        vendor_item_ref=VendorRef(
            id=composite_id,
            title=title,
            url=falcon_host_link,
            created=created_time,
        ),
        ocsf=detection_finding,
    )


class CrowdstrikeNgSiemEventSync(EventSync):
    PAGE_SIZE = 1000

    @normalize(normalize_crowdstrike_alert)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CrowdstrikeNgSiemV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        """
        Fetch Alerts (Formerly called Detections) from the crowdstrike API. By default
        all results are sorted by `created_timestamp` to ensure we have a stable
        sorting order."""
        api = self.integration.get_api()
        body = self._generate_request_body(bookmark)
        idx = 0
        for idx, alert in enumerate(paginate(api.fetch_alerts, body=body)):
            yield alert
            timestamp = alert["created_timestamp"]
            bookmark.last_event_ingested = datetime.fromisoformat(timestamp)
        logger.debug(f"{idx} alerts fetched")

    def get_permission_checks(self):
        return [ReadAlerts]

    def _generate_request_body(self, bookmark: CrowdstrikeNgSiemV1EventSyncBookmark):
        return {
            "include_hidden": False,
            "filter": generate_fql_filter(
                products=SUPPORTED_INGEST_PRODUCTS,
                created_after=bookmark.last_event_ingested,
            ),
            "sort": "created_timestamp.asc",
            "limit": self.PAGE_SIZE,
        }
