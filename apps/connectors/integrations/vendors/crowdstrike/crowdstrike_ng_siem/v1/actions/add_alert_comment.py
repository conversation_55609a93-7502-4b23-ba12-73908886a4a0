from apps.connectors.integrations.actions.add_alert_comment import (
    AddAlertComment,
    AddAlertCommentArgs,
    AddAlertCommentResult,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.api import (
    CrowdstrikeNgSiemV1Api,
)
from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_ng_siem.v1.health_check import (
    ReadAlerts,
)


class CrowdstrikeNgSiemAddAlertComment(AddAlertComment):
    def execute(self, args: AddAlertCommentArgs, **kwargs) -> AddAlertCommentResult:
        api: CrowdstrikeNgSiemV1Api = self.integration.get_api()
        api.alerts.update_alerts_v3(
            composite_ids=[args.vendor_sync_id],
            append_comment=args.comment,
        )
        return AddAlertCommentResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAlerts]
