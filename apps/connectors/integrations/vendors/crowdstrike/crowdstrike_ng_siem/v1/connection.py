from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate
from apps.connectors.integrations.vendors.crowdstrike.connection import (
    CrowdstrikeConfig,
)


class CrowdstrikeNgSiemV1Config(CrowdstrikeConfig):
    cid: str = Field(
        title="CID",
        description="Customer ID (CID) for the CrowdStrike account",
        max_length=1024,
    )


class CrowdstrikeNgSiemV1Connection(ConnectionTemplate):
    id = "crowdstrike_ng_siem"
    name = "Crowdstrike NG SIEM"
    config_model = CrowdstrikeNgSiemV1Config
