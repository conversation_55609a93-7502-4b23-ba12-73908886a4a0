from falconpy import NGSIEM, CorrelationRules

from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.v1.api import (
    CrowdstrikeFalconV1Api,
)


class CrowdstrikeNgSiemV1Api(CrowdstrikeFalconV1Api):
    """
    Crowdstrike Falcon API
    https://falcon.crowdstrike.com/documentation/category/a3a706a8/crowdstrike-api
    """

    def __init__(self, cid=None, client_id=None, client_secret=None):
        super().__init__(client_id, client_secret)
        self.cid = cid
        self.correlation_rules = CorrelationRules(auth_object=self.auth, pythonic=True)
        self.ngsiem = NGSIEM(auth_object=self.auth, pythonic=True)
