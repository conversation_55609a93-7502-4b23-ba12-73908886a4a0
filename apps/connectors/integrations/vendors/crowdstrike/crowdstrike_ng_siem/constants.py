from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon.constants import (
    CrowdstrikeProducts,
)

# These are the products that we will fetch Events (alerts in Crowdstrike) for.
SUPPORTED_INGEST_PRODUCTS = [
    CrowdstrikeProducts.NGSIEM,
    # Overwatch (Crowdstrike's Threat Hunting service) are not going to be consumed in
    # downstream integrations yet - but gather the data so we can see it first. These
    # events are likely rare, hence why I'm being gratuitous and pullin them in.
    CrowdstrikeProducts.OVERWATCH,
    CrowdstrikeProducts.THIRD_PARTY,
    CrowdstrikeProducts.XDR,
]
