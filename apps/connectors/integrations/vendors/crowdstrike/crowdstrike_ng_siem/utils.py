from datetime import datetime
from typing import List

from apps.connectors.integrations.vendors.crowdstrike.crowdstrike_falcon import (
    constants,
)


def generate_fql_filter(
    products: List[constants.CrowdstrikeProducts],
    created_before: datetime = None,
    created_after: datetime = None,
    statuses: List[str] = None,
):
    """Generate a FQL `filter` object used in the Bulk Alerts API request."""
    rendered_products = [p.value for p in products]
    filter_args = [f"product:{str(rendered_products)}"]

    # FQL does not support equality operators - you can't search for a specific
    # time, it must be > or <
    if created_after:
        after = created_after.strftime(constants.DATE_FORMAT)
        filter_args.append(f"created_timestamp:>'{after}'")
    if created_before:
        before = created_before.strftime(constants.DATE_FORMAT)
        filter_args.append(f"created_timestamp:<'{before}'")
    if statuses:
        filter_args.append(f"status:{str(statuses)}")
    rendered = " + ".join(filter_args)

    return rendered
