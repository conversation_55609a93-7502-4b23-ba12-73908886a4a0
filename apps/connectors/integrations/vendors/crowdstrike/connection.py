from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CrowdstrikeConfig(TemplateVersionConfig):
    # Cloud Region Autodiscovery removes the need to specify the API URL
    # https://www.falconpy.io/Usage/Environment-Configuration.html#cloud-region-autodiscovery
    # Note: USGOV1 is not supported by autodiscovery. We don't support it either.
    client_id: str = Field(
        title="Client ID",
        description="API Client ID",
        max_length=1024,
    )
    client_secret: EncryptedStr = Field(
        title="Client Secret",
        description="API Client Secret",
        max_length=1024,
    )


class CrowdstrikeConnection(ConnectionTemplate):
    id = "crowdstrike_falcon"
    name = "CrowdStrike Falcon"
    config_model = CrowdstrikeConfig
