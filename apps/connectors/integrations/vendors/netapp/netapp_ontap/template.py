from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import NetappOntapV1TemplateVersion


class NetappOntapTemplate(Template):
    id = "netapp_ontap"
    name = "NetApp ONTAP"
    category = Template.Category.DATA_BACKUP
    versions = {
        NetappOntapV1TemplateVersion.id: NetappOntapV1TemplateVersion(),
    }
    vendor = Vendors.NETAPP
