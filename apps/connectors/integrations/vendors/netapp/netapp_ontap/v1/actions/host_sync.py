from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.netapp.netapp_ontap.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    os_name = ""
    host_type = HostType.SERVER
    os_family = OsFamily.UNKNOWN
    fqdns = host_data.get("name", {}) or ""
    ip_addresses = [
        interface["ip"]["address"]
        for interface in host_data.get("cluster_interfaces", [])
    ]
    mac_addresses = host_data.get("service_processor", {}).get("mac_address")
    return Host(
        source_id=host_data.get("uuid"),
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("date")),
        source_data=host_data,
    )


class NetappOntapV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        page = api.get_cluster_nodes()
        yield from page["records"]

    def get_permission_checks(self):
        return [ReadDeviceInventory]
