from apps.connectors.integrations import TemplateVersion

from .connection import NetappOntapV1Config, NetappOntapV1Connection
from .integration import NetappOntapV1Integration
from .settings import NetappOntapV1Settings


class NetappOntapV1TemplateVersion(TemplateVersion):
    integration = NetappOntapV1Integration
    id = "v1"
    name = "v1"
    config_model = NetappOntapV1Config
    connection_model = NetappOntapV1Connection
    settings_model = NetappOntapV1Settings
