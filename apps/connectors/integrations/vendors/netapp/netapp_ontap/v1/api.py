from requests.auth import HTTP<PERSON>asic<PERSON>uth

from apps.connectors.integrations.api import ApiBase


class NetappOntapV1Api(ApiBase):
    def __init__(self, cluster_mgmt_url=None, username=None, password=None):
        self.cluster_mgmt_url = cluster_mgmt_url
        self.username = username
        self.password = password
        self.baseurl = self.cluster_mgmt_url
        super().__init__(
            base_url=self.baseurl, static_headers={"Accept": "application/json"}
        )
        self.session.auth = HTTPBasicAuth(self.username, self.password)

    def get_cluster_nodes(self):
        response = self.session.get(self.url("/api/v1/cluster/nodes"))
        return response.json()

    def get_version(self):
        response = self.session.get(self.url("/api/v1/cluster?fields=version"))
        return response.json()
