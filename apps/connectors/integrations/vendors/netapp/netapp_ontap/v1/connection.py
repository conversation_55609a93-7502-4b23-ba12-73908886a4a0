from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class NetappOntapV1Config(TemplateVersionConfig):
    cluster_mgmt_url: HttpUrl = Field(
        title="Cluster Management URL",
        description="The URL of the cluster management interface.",
    )
    username: str = Field(
        title="Username",
        description="The username to authenticate with the cluster.",
    )
    password: EncryptedStr = Field(
        title="Password",
        description="The password to authenticate with the cluster.",
    )


class NetappOntapV1Connection(ConnectionTemplate):
    id = "netapp_ontap"
    name = "NetApp ONTAP"
    config_model = NetappOntapV1Config
