from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import DefenderAtpV1TemplateVersion


class DefenderAtpTemplate(Template):
    id = "defender_atp"
    name = "Microsoft Defender for Endpoint"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        DefenderAtpV1TemplateVersion.id: DefenderAtpV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
    endpoint_coverage_available = True
