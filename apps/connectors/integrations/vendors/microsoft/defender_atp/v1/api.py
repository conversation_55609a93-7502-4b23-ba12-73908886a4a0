from microsoft_client.defender_atp import DefenderATPClient


class DefenderAtpV1Api(DefenderATPClient):
    """
    Defender for Endpoint API
    https://learn.microsoft.com/en-us/microsoft-365/security/defender-endpoint/exposed-apis-list
    """

    URL_GCC = "https://api-gcc.securitycenter.microsoft.us/api/v1.0"
    SCOPES_GCC = ("https://api-gcc.securitycenter.microsoft.us/.default",)

    def __init__(
        self,
        tenant_id=None,
        client_id=None,
        client_secret=None,
        microsoft_cloud_platform=None,
        **kwargs
    ):
        if microsoft_cloud_platform and "gcc" in microsoft_cloud_platform:
            self.URL = self.URL_GCC
            self.SCOPES = self.SCOPES_GCC

        super().__init__(
            None,
            None,
            None,
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

    def get_machines(self, params=None):
        return self.machines.list(params=params)

    def run_advanced_query(self, query):
        return self.advanced_queries.run(query)
