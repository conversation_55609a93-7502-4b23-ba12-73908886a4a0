from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConfig,
    MicrosoftConnection,
)

from .integration import DefenderAtpV1Integration
from .settings import DefenderAtpV1Settings


class DefenderAtpV1TemplateVersion(TemplateVersion):
    integration = DefenderAtpV1Integration
    id = "v1"
    name = "v1"
    config_model = MicrosoftConfig
    connection_model = MicrosoftConnection
    settings_model = DefenderAtpV1Settings
