from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    InternetExposure,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.microsoft.defender_atp.v1.settings import (
    DefenderAtpV1HostSyncSettings,
)


def normalize_host(host_data: dict):
    group_name = host_data.get("rbacGroupName", "")
    public_ip = host_data.get("lastExternalIpAddress")
    public_ips = [public_ip] if public_ip else []
    private_ip = host_data.get("lastIpAddress")
    private_ips = [private_ip] if private_ip else []
    ip_addresses = private_ips + public_ips
    mac_addresses = []
    for entry in host_data.get("ipAddresses"):
        if mac_address := entry.get("macAddress"):
            mac_addresses.append(mac_address)

    is_internet_facing = host_data["isInternetFacing"]
    internet_exposure = (
        InternetExposure.INTERNET_FACING
        if is_internet_facing
        else InternetExposure.NOT_INTERNET_FACING
    )

    os_string = (
        (host_data.get("osPlatform") or "") + " " + (host_data.get("osVersion") or "")
    )
    host_type = HostType.from_os_name(os_string)
    os_family, os_name = OsFamily.from_string(os_string)
    if host_type == HostType.OTHER and os_family == OsFamily.LINUX:
        host_type = HostType.SERVER
    return Host(
        hostname="",  # will be parsed from fqdn
        source_id=host_data["id"],
        group_names=to_list(group_name),
        fqdns=host_data.get("computerDnsName", ""),
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        is_internet_facing=is_internet_facing,
        internet_exposure=internet_exposure,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        aad_id=host_data.get("aadDeviceId"),
        last_seen=normalize_last_seen(host_data.get("lastSeen")),
        source_data=host_data,
    )


class DefenderAtpV1HostSync(HostSync):
    settings: DefenderAtpV1HostSyncSettings

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()

        internet_facing_query = """
        DeviceInfo
        | where IsInternetFacing
        | project DeviceId, IsInternetFacing
        """
        results = api.run_advanced_query(internet_facing_query)["Results"]
        internet_facing_device_ids = {result["DeviceId"] for result in results}

        for page in api.paginate(api.get_machines):
            for machine in page:
                if not self.settings.fetch_inactive_machines:
                    if machine.get("healthStatus") == "Inactive":
                        continue

                if self.settings.fetch_only_onboarded_machines:
                    if machine.get("onboardingStatus") != "Onboarded":
                        continue

                machine["isInternetFacing"] = (
                    machine["id"] in internet_facing_device_ids
                )

                yield machine

    def get_permission_checks(self):
        return []
