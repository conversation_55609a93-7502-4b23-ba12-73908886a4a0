from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class DefenderAtpV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Defender for Endpoint Fetch Devices Settings")

    fetch_inactive_machines: bool = Field(
        title="Fetch inactive devices",
        description="Select whether to fetch MDE inactive devices.",
        default=False,
    )

    fetch_only_onboarded_machines: bool = Field(
        title="Fetch only onboarded devices",
        description="Select whether to fetch only MDE onboarded devices.",
        default=True,
    )


DefenderAtpV1Settings = create_settings_model(
    "DefenderAtpV1Settings",
    {
        IntegrationActionType.HOST_SYNC: DefenderAtpV1HostSyncSettings,
    },
)
