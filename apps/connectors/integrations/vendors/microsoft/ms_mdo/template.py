from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdoV1TemplateVersion


class MsMdoTemplate(Template):
    id = "ms_mdo"
    name = "Microsoft Defender for Office"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        MsMdoV1TemplateVersion.id: MsMdoV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
