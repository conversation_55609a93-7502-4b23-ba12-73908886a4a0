from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import MsMdoV1Integration
from .settings import MsMdoV1Settings


class MsMdoV1TemplateVersion(TemplateVersion):
    integration = MsMdoV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsMdoV1Settings
