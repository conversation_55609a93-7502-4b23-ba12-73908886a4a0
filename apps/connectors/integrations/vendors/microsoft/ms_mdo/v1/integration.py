from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions.event_sync_from_artifact import (
    MsMdoV1EventSyncFromArtifact,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions.get_email_activity import (
    MsMdoV1GetEmailActivityBySender,
    MsMdoV1GetSimilarEmailLinksClicked,
    MsMdoV1GetSimilarEmails,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions.get_office_activity import (
    MsMdoV1GetOfficeActivity,
    MsMdoV1GetOfficeActivityByUser,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions.get_url_clicks import (
    MsMdoV1GetUrlClicksByMessage,
    MsMdoV1GetUrlClicksByMessageSender,
    MsMdoV1GetUrlClicksByUrl,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.api import MsMdoV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.delete_ioc import (
    MsXdrV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_data_sources import (
    MsXdrV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_iocs import (
    MsXdrV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.save_ioc import (
    MsXdrV1SaveIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)


class MsMdoV1Integration(AadClientMixin, Integration):
    api_class = MsMdoV1Api
    actions = (
        MsMdoV1EventSyncFromArtifact,
        MsXdrV1DeleteIoc,
        MsXdrV1ListDataSources,
        MsXdrV1ListIocs,
        MsXdrV1SaveIoc,
        MsMdoV1GetOfficeActivityByUser,
        MsMdoV1GetOfficeActivity,
        MsMdoV1GetUrlClicksByUrl,
        MsMdoV1GetUrlClicksByMessage,
        MsMdoV1GetUrlClicksByMessageSender,
        MsMdoV1GetSimilarEmails,
        MsMdoV1GetEmailActivityBySender,
        MsMdoV1GetSimilarEmailLinksClicked,
    )
    critical_health_checks = (ConnectionHealthCheck,)
