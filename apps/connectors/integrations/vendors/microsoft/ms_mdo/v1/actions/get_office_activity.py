from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    InvocationType,
)
from apps.connectors.integrations.schemas.query_args import QueryArgs, UserQueryArgs
from apps.connectors.integrations.schemas.query_result import UntypedQueryResult
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions import (
    MsMdoV1InternalActions,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.utils import (
    QueryActionMixin,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadThreatHunting,
)


class MsMdoV1GetOfficeActivity(IntegrationAction, QueryActionMixin):
    name = "Get Office Activity"
    action_type = MsMdoV1InternalActions.GET_OFFICE_ACTIVITY
    metadata = IntegrationActionMetadata(
        args_type=QueryArgs,
        result_type=UntypedQueryResult,
    )
    entitlement = Entitlement.mdr
    invocation_type = InvocationType.INTERNAL

    def execute(self, args: QueryArgs, **kwargs) -> UntypedQueryResult:
        query_response = self.execute_query(
            """
            OfficeActivity
""",
            args,
        )

        return UntypedQueryResult(result=query_response)

    def get_permission_checks(self):
        return [ReadThreatHunting]


class MsMdoV1GetOfficeActivityByUser(IntegrationAction, QueryActionMixin):
    name = "Get Office Activity By User"
    action_type = MsMdoV1InternalActions.GET_OFFICE_ACTIVITY_BY_USER
    metadata = IntegrationActionMetadata(
        args_type=UserQueryArgs,
        result_type=UntypedQueryResult,
    )
    entitlement = Entitlement.mdr
    invocation_type = InvocationType.INTERNAL

    def execute(self, args: UserQueryArgs, **kwargs) -> UntypedQueryResult:
        query_response = self.execute_query(
            """
        OfficeActivity
        | where UserId =~ "{{ identifier.value }}"
        | summarize count() by UserType, UserId, OfficeWorkload, RecordType, ItemType, Operation, ClientIP, SourceFileName, Site_Url, ResultStatus, ResultReasonType
        | project-reorder count_, UserType, UserId, OfficeWorkload, RecordType, ItemType, Operation, ClientIP, SourceFileName, ResultStatus, ResultReasonType
        | order by count_ desc
""",
            args,
        )

        return UntypedQueryResult(result=query_response)

    def get_permission_checks(self):
        return [ReadThreatHunting]
