from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    InvocationType,
)
from apps.connectors.integrations.actions.get_emails import (
    GetEmailActivityBySender,
    GetSimilarEmails,
)
from apps.connectors.integrations.actions.utils import to_list
from apps.connectors.integrations.schemas import (
    EmailActivityQueryResult,
    ErrorDetail,
    MailMessageQueryArgs,
    UntypedQueryResult,
    UserQueryArgs,
)
from apps.connectors.integrations.schemas.ocsf import (
    ControlAction,
    Disposition,
    Email,
    EmailActivity,
    EmailActivityType,
    Metadata,
    Profile,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdo.v1.actions import (
    MsMdoV1InternalActions,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.utils import (
    QueryActionMixin,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadThreatHunting,
)

get_similar_emails_query_template = """
    let GET_SIMILAR_EMAILS = () {
    let Attributes = materialize (
        EmailEvents
        | where column_ifexists("NetworkMessageId", false) == "{{ identifier.value }}"
        | distinct
            SenderMailFromAddress,
            SenderMailFromDomain,
            SenderDisplayName,
            SenderFromAddress,
            SenderFromDomain,
            EmailClusterId,
            Subject = tolower(Subject)
        );
    let MailCluster = (Attribute: string, UseSubject: bool) {
        // UseSubject = true : checks to see if the subject is similar
        // UseSubject = false: uses the EmailClusterId to check for similarity
        Attributes
            | where isnotempty(column_ifexists(Attribute, ""))
            | extend Attribute = tostring(column_ifexists(Attribute, ""))
            | project-rename
                InitialSenderMailFromAddress = SenderMailFromAddress,
                InitialSenderMailFromDomain = SenderMailFromDomain,
                InitialSenderDisplayName = SenderDisplayName,
                InitialSenderFromAddress = SenderFromAddress,
                InitialSenderFromDomain = SenderFromDomain,
                InitialEmailClusterId = EmailClusterId,
                InitialSubject = Subject
            | join kind = inner (
                EmailEvents
                | where isnotempty(column_ifexists(Attribute, ""))
                | extend Attribute = tostring(column_ifexists(Attribute, ""))
                )
                on Attribute
            | where
                (not(UseSubject) and isnotempty(InitialEmailClusterId) and InitialEmailClusterId == EmailClusterId)
                or
                (UseSubject and jaccard_index(extract_all("([a-z]+)", InitialSubject), extract_all("([a-z]+)", tolower(Subject))) > 0.5)
            | project-away *1
    };
    union isfuzzy=true
        MailCluster("SenderMailFromAddress", true),
        MailCluster("SenderMailFromDomain", true),
        MailCluster("SenderDisplayName", true),
        MailCluster("SenderFromAddress", true),
        MailCluster("SenderFromDomain", true),
        MailCluster("EmailClusterId", true),
        MailCluster("SenderMailFromAddress", false),
        MailCluster("SenderMailFromDomain", false),
        MailCluster("SenderDisplayName", false),
        MailCluster("SenderFromAddress", false),
        MailCluster("SenderFromDomain", false)
    | summarize arg_max(Timestamp, *) by NetworkMessageId, RecipientEmailAddress
    };
    GET_SIMILAR_EMAILS()
"""

disposition_map = {
    "Delivered": Disposition.ALLOWED,
    "Junked": Disposition.DELETED,
    "Blocked": Disposition.BLOCKED,
}


def normalize_email_activity(src: dict) -> EmailActivity:
    disposition = disposition_map.get(src.get("DeliveryAction", ""), Disposition.OTHER)
    is_blocked = disposition == Disposition.BLOCKED

    return EmailActivity(
        activity=EmailActivityType.RECEIVE,
        action=ControlAction.DENIED if is_blocked else ControlAction.ALLOWED,
        disposition=disposition,
        metadata=Metadata(
            correlation_uid=src.get("ReportId"),
            event_code=src.get("Type"),
            profiles=[
                Profile.DATETIME,
                Profile.SECURITY_CONTROL,
            ],
        ),
        time_dt=src["Timestamp"],
        message=src.get("Subject"),
        email=Email(
            from_=src.get("SenderMailFromAddress"),
            to=to_list(src.get("RecipientEmailAddress")),
            subject=src.get("Subject"),
            uid=src.get("NetworkMessageId"),
            message_uid=src.get("InternetMessageId"),
            x_originating_ip=to_list(src.get("SenderIPv4")),
        ),
    )


class MsMdoV1GetEmailActivityBySender(GetEmailActivityBySender, QueryActionMixin):
    is_required = True

    def execute(self, args: UserQueryArgs, **kwargs) -> EmailActivityQueryResult:
        raw_activities = self.execute_query(
            """
    EmailEvents
    | where SenderMailFromAddress == "{{ identifier.value }}"
        or SenderFromAddress == "{{ identifier.value }}"
        or SenderMailFromDomain == "{{ identifier.value }}"
        or SenderFromDomain == "{{ identifier.value }}"
            """,
            args,
        )
        normalized_results = [normalize_email_activity(src) for src in raw_activities]
        return EmailActivityQueryResult(result=normalized_results)

    def get_permission_checks(self):
        return [ReadThreatHunting]


class MsMdoV1GetSimilarEmails(GetSimilarEmails, QueryActionMixin):
    def execute(self, args: MailMessageQueryArgs, **kwargs) -> EmailActivityQueryResult:
        raw_activities = self.execute_query(
            get_similar_emails_query_template,
            args,
        )
        normalized_results = [normalize_email_activity(src) for src in raw_activities]
        return EmailActivityQueryResult(result=normalized_results)

    def get_permission_checks(self):
        return [ReadThreatHunting]


class MsMdoV1GetSimilarEmailLinksClicked(IntegrationAction, QueryActionMixin):
    name = "Get Similar Email Links Clicked"
    action_type = MsMdoV1InternalActions.GET_SIMILAR_EMAIL_LINKS_CLICKED
    metadata = IntegrationActionMetadata(
        args_type=MailMessageQueryArgs,
        result_type=UntypedQueryResult,
    )
    entitlement = Entitlement.mdr
    invocation_type = InvocationType.INTERNAL

    def execute(self, args: MailMessageQueryArgs, **kwargs) -> UntypedQueryResult:
        raw_email_activities = self.execute_query(
            get_similar_emails_query_template, args
        )
        normalized_email_activities = [
            normalize_email_activity(src) for src in raw_email_activities
        ]

        network_message_ids = [
            email_activity.email.uid for email_activity in normalized_email_activities
        ]

        if not network_message_ids:
            return UntypedQueryResult(
                result=[], error=ErrorDetail(message="No email activities found.")
            )

        query_response = self.execute_query(
            """
    let ARRAY_FROM_TABLE_COLUMN = (TABLE: (*), COLUMN:string) {
        set_difference(toscalar(TABLE | summarize make_set(column_ifexists(COLUMN, ""))), dynamic([""]))
    };
    let NETWORK_MESSAGE_IDS = datatable(id: string)[{% for network_message_id in network_message_ids %}"{{ network_message_id }}"{% if not loop.last %}, {% endif %}{% endfor %}];
    let URL_CLICKED_EVENTS = UrlClickEvents
    | where NetworkMessageId in (NETWORK_MESSAGE_IDS)
    | where ActionType =~ "ClickAllowed" or IsClickedThrough;
    let ACCOUNT_UPNS = ARRAY_FROM_TABLE_COLUMN(URL_CLICKED_EVENTS, "AccountUpn");
    URL_CLICKED_EVENTS
    | where isnotempty(AccountUpn)
    | extend AccountUpn=tolower(AccountUpn)
    | join kind=inner(
        IdentityInfo
        | where AccountUpn in~(ACCOUNT_UPNS) or EmailAddress in~(ACCOUNT_UPNS)
        | distinct
            AccountUpn=iff(AccountUpn in~(ACCOUNT_UPNS), tolower(AccountUpn), tolower(EmailAddress)),
            AccountObjectId
        ) on AccountUpn
    | summarize arg_max(Timestamp, UrlChain) by AccountUpn, AccountObjectId, NetworkMessageId, Url, ActionType, IsClickedThrough
    | project-reorder Timestamp, NetworkMessageId, AccountUpn, AccountObjectId, ActionType, IsClickedThrough, Url, UrlChain
""",
            args,
            additional_args={"network_message_ids": network_message_ids},
        )

        return UntypedQueryResult(result=query_response)

    def get_permission_checks(self):
        return [ReadThreatHunting]
