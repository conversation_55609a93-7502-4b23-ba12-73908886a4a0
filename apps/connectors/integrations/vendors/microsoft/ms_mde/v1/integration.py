from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.actions.event_sync_from_artifact import (
    MsMdeV1EventSyncFromArtifact,
)
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.api import MsMdeV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.delete_ioc import (
    MsXdrV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_data_sources import (
    MsXdrV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_iocs import (
    MsXdrV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.save_ioc import (
    MsXdrV1SaveIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)


class MsMdeV1Integration(AadClientMixin, Integration):
    api_class = MsMdeV1Api
    actions = (
        MsMdeV1EventSyncFromArtifact,
        MsXdrV1DeleteIoc,
        MsXdrV1ListDataSources,
        MsXdrV1ListIocs,
        MsXdrV1SaveIoc,
    )
    critical_health_checks = (ConnectionHealthCheck,)
