from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdeV1TemplateVersion


class MsMdeTemplate(Template):
    id = "ms_mde"
    name = "Microsoft Defender for Endpoint (new)"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        MsMdeV1TemplateVersion.id: MsMdeV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
