from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.bookmarks import (
    MsXdrV1Bookmarks,
)

from .integration import MsXdrV1Integration
from .settings import MsXdrV1Settings


class MsXdrV1TemplateVersion(TemplateVersion):
    integration = MsXdrV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsXdrV1Settings
    bookmarks_model = MsXdrV1Bookmarks
