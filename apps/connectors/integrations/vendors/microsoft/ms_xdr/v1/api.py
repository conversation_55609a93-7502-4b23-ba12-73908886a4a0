from microsoft_client.graph import MicrosoftGraphClient


class MsXdrV1Api:
    def __init__(self, tenant_id=None, client_id=None, client_secret=None, **kwargs):
        self.client = MicrosoftGraphClient(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

        self.enumerate = self.client.enumerate
        self.paginate = self.client.paginate

    def get_alerts(self, params=None, headers=None):
        return self.client.request(
            "GET", "/security/alerts_v2", params=params, headers=headers
        )

    def get_incidents(self, params=None, headers=None):
        return self.client.request(
            "GET", "/security/incidents", params=params, headers=headers
        )

    def update_alert(self, alert_id, data):
        return self.client.request(
            "PATCH", f"/security/alerts_v2/{alert_id}", json=data
        )

    def create_alert_comment(self, alert_id, comment):
        return self.client.request(
            "POST",
            f"/security/alerts_v2/{alert_id}/comments",
            json={"comment": comment},
        )

    def create_incident_comment(self, incident_id, comment):
        return self.client.request(
            "POST",
            f"/security/incidents/{incident_id}/comments",
            json={"comment": comment},
        )

    def create_detection_rule(self, data):
        return self.client.request("POST", "/security/rules/detectionRules", json=data)

    def update_detection_rule(self, rule_id, data):
        return self.client.request(
            "PATCH", f"/security/rules/detectionRules/{rule_id}", json=data
        )

    def delete_detection_rule(self, rule_id):
        return self.client.request(
            "DELETE", f"/security/rules/detectionRules/{rule_id}"
        )

    def list_detection_rules(self, params=None, headers=None):
        return self.client.request(
            "GET", "/security/rules/detectionRules", params=params, headers=headers
        )

    def run_hunting_query(self, query, timespan=None):
        body = {"Query": query}
        if timespan:
            body["Timespan"] = timespan

        return self.client.request("POST", "/security/runHuntingQuery", json=body)

    def get_permissions(self):
        return self.client.permissions

    def has_permission(self, permission):
        return self.client.has_permission(permission)
