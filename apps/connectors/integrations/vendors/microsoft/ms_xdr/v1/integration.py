from microsoft_client.exceptions import MicrosoftHTTPError

from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.add_alert_comment import (
    MsXdrV1AddAlertComment,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.delete_ioc import (
    MsXdrV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
    MsXdrV1EventSync,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync_from_artifact import (
    MsXdrV1EventSyncFromArtifact,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_data_sources import (
    MsXdrV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_iocs import (
    MsXdrV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.save_ioc import (
    MsXdrV1SaveIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.update_lifecycle_status import (
    MsXdrV1UpdateLifecycleStatus,
)

from .api import MsXdrV1Api
from .health_check import ConnectionHealthCheck


class MsXdrV1Integration(AadClientMixin, Integration):
    api_class = MsXdrV1Api
    exception_types = (MicrosoftHTTPError,)
    actions = (
        MsXdrV1EventSync,
        MsXdrV1AddAlertComment,
        MsXdrV1DeleteIoc,
        MsXdrV1ListDataSources,
        MsXdrV1ListIocs,
        MsXdrV1SaveIoc,
        MsXdrV1UpdateLifecycleStatus,
        MsXdrV1EventSyncFromArtifact,
    )
    critical_health_checks = (ConnectionHealthCheck,)
