from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    SimplePermissionsCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadAlerts(SimplePermissionsCheck):
    name = "Read Security Alerts"
    description = "Read Alerts in Microsoft Graph Security API"
    value = "Read Security Alerts"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["SecurityAlert.Read.All", "SecurityAlert.ReadWrite.All"]


class ReadWriteAlerts(SimplePermissionsCheck):
    name = "Read and Write Security Alerts"
    description = "Read and Write Alerts in Microsoft Graph Security API"
    value = "Read and Write Security Alerts"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "SecurityAlert.ReadWrite.All",
    ]


class ReadWriteCustomDetections(SimplePermissionsCheck):
    name = "Read and Write Custom Detections"
    description = "Read and Write Custom Detections in Microsoft Graph Security API"
    value = "Read and Write Custom Detections"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "CustomDetection.ReadWrite.All",
    ]


class ReadThreatHunting(SimplePermissionsCheck):
    name = "Read Threat Hunting"
    description = (
        "Allows execution of Threat Hunting queries in Microsoft Graph Security API"
    )
    value = "Read Threat Hunting"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "ThreatHunting.Read.All",
    ]


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_permissions")
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED

        return IntegrationHealthCheckResult.PASSED
