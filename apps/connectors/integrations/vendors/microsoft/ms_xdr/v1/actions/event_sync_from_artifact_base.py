from abc import ABC, abstractmethod
from enum import StrEnum
from typing import Generator

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventSyncFromArtifact,
    EventSyncFromArtifactArgs,
)


class MsXdrServiceSource(StrEnum):
    MICROSOFT_DEFENDER_FOR_ENDPOINT = "microsoftDefenderForEndpoint"
    MICROSOFT_DEFENDER_FOR_IDENTITY = "microsoftDefenderForIdentity"
    MICROSOFT_DEFENDER_FOR_CLOUD_APPS = "microsoftDefenderForCloudApps"
    MICROSOFT_DEFENDER_FOR_OFFICE_365 = "microsoftDefenderForOffice365"
    AZURE_AD_IDENTITY_PROTECTION = "azureAdIdentityProtection"
    MICROSOFT_SENTINEL = "microsoftSentinel"


class EventSyncFromArtifactBase(EventSyncFromArtifact, ABC):
    """
    Base class for event sync from artifact actions that can be used by related integrations.
    """

    @abstractmethod
    def is_applicable(self, event) -> bool:
        """
        Allow subclasses to implement their own logic for determining if an event is applicable.
        """
        ...

    def execute(
        self,
        args: EventSyncFromArtifactArgs,
        **kwargs,
    ) -> Generator[Event, None, None]:
        from apps.connectors.services import artifact_service

        artifact_id = args.artifact_id

        artifact_key = artifact_service.convert_id_to_key(artifact_id)

        for event in artifact_service.read_lines(artifact_key):
            if self.is_applicable(event):
                yield event

    def get_permission_checks(self):
        return []
