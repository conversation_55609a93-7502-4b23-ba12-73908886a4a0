from typing import Generator

from apps.connectors.integrations.actions.ioc.list_data_sources import (
    DataSource,
    DataSourceType,
    ListDataSources,
    ListDataSourcesArgs,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadThreatHunting,
)


def normalize_data_source(src: dict) -> DataSource:
    return DataSource(
        source_type=DataSourceType.TABLE,
        source_name=src["TableName"],
        last_log_time=src["LastLogTime"] if src["LastLogTime"] else None,
        log_count=src["Count"],
    )


class MsXdrV1ListDataSources(ListDataSources):
    @normalize(normalize_data_source)
    def execute(
        self, args: ListDataSourcesArgs, **kwargs
    ) -> Generator[DataSource, None, None]:
        api: MsXdrV1Api = self.integration.get_api()

        query = (
            "union * "
            '| where isnotempty(Type) and Type !="Usage" '
            "| summarize Count=count(),LastLogTime = max(coalesce(Timestamp, column_ifexists('TimeGenerated', ingestion_time()))) by TableName = Type"
        )

        response = api.run_hunting_query(
            query, "{}/{}".format(args.start_time, args.end_time)
        )

        yield from response["results"]

    def get_permission_checks(self, *args, **kwargs):
        return [ReadThreatHunting]
