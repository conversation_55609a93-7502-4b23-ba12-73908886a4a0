from apps.connectors.integrations.actions.ioc.save_ioc import (
    <PERSON><PERSON><PERSON>,
    SaveIocResult,
)
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC, IOCStatus
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadWriteCustomDetections,
)


class MsXdrV1SaveIoc(SaveIoc):
    def execute(self, args: IOC, **kwargs) -> SaveIocResult:
        api: MsXdrV1Api = self.integration.get_api()
        api.client.URL = api.client.BETA

        source_id = args.source_id
        source_data = args.source_data

        source_data["isEnabled"] = args.status == IOCStatus.ACTIVE
        source_data["queryCondition"] = source_data.get("queryCondition", {})
        source_data["queryCondition"]["queryText"] = args.query
        source_data["displayName"] = args.title

        if source_id:
            api.update_detection_rule(self._lookup_rule_id(source_id), source_data)
        else:
            source_id = api.create_detection_rule(source_data)["detectorId"]

        return SaveIocResult(source_id=source_id)

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteCustomDetections]

    def _lookup_rule_id(self, detector_id: str):
        api: MsXdrV1Api = self.integration.get_api()
        api.client.URL = api.client.BETA

        return next(
            filter(
                lambda rule: rule["detectorId"] == detector_id,
                api.enumerate(api.list_detection_rules),
            ),
            {},
        ).get("id")
