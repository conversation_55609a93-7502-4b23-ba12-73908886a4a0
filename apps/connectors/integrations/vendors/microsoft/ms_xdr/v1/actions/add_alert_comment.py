from apps.connectors.integrations.actions.add_alert_comment import (
    AddAlertComment,
    AddAlertCommentArgs,
    AddAlertCommentResult,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadWriteAlerts,
)


class MsXdrV1AddAlertComment(AddAlertComment):
    def execute(self, args: AddAlertCommentArgs, **kwargs) -> AddAlertCommentResult:
        api: MsXdrV1Api = self.integration.get_api()
        api.create_incident_comment(args.vendor_sync_id, args.comment)

        return AddAlertCommentResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteAlerts]
