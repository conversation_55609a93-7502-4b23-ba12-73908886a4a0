from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync_from_artifact_base import (
    EventSyncFromArtifactBase,
    MsXdrServiceSource,
)


class MsXdrV1EventSyncFromArtifact(EventSyncFromArtifactBase):
    """
    Event sync from artifact action for Microsoft XDR integration.
    """

    name = "Fetch XDR Events"

    def is_applicable(self, event) -> bool:
        known_sources = {e.value for e in MsXdrServiceSource}
        ss = event.get("raw_event").get("serviceSource")
        ds = event.get("raw_event").get("detectionSource")

        # only include events from sources that aren't implemented in another integration
        return ss not in known_sources and ds != "microsoftDefenderForIoT"
