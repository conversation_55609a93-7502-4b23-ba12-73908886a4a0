from typing import List

from django.template import Context, Template

from apps.connectors.integrations.schemas.query_args import QueryArgs
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api


class QueryActionMixin:
    def execute_query(
        self,
        query: str,
        args: QueryArgs,
        additional_args: dict = {},
    ) -> List[dict]:
        api: MsXdrV1Api = self.integration.get_api()

        timespan = None
        if args.time_range:
            timespan = "{}/{}".format(
                args.time_range.start_time, args.time_range.end_time
            )

        combined_args = {
            **args.model_dump(),
            **additional_args,
        }

        query = Template(query).render(Context(combined_args))

        response = api.run_hunting_query(query, timespan=timespan)

        return response["results"]
