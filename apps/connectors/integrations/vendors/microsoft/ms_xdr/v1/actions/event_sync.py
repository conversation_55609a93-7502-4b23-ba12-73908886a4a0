import datetime
import logging
from typing import Generator

import numpy as np

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.actions.utils import to_list
from apps.connectors.integrations.schemas.ocsf import (
    Account,
    AccountType,
    Agent,
    Analytic,
    Api,
    Databucket,
    DetectionActivity,
    DetectionFinding,
    DetectionStatus,
    Device,
    DeviceHwInfo,
    Email,
    EndpointType,
    EvidenceArtifacts,
    Feature,
    File,
    FindingInformation,
    Fingerprint,
    GeoLocation,
    Group,
    HashAlgorithm,
    HttpRequest,
    KeyValueObject,
    Metadata,
    NetworkConnectionInfo,
    NetworkEndpoint,
    NetworkInterface,
    OperatingSystem,
    OSType,
    Process,
    Product,
    Profile,
    RegistryValueType,
    RegKey,
    RegValue,
    ResourceDetails,
    RiskLevel,
    Service,
    Session,
    Severity,
    Url,
    User,
    Verdict,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.bookmarks import (
    MsXdrV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ReadAlerts,
)

logger = logging.getLogger(__name__)


def format_datetime(dt: np.datetime64) -> str:
    return str(np.datetime_as_string(dt, timezone="UTC"))


def parse_datetime(dt: np.datetime64) -> np.datetime64:
    return np.datetime64(dt.rstrip("Z"))


def _normalize_user(evidence: dict) -> EvidenceArtifacts:
    """
    Handles user evidence.
    """
    user_account = evidence.get("userAccount", {})
    return EvidenceArtifacts(
        user=User(
            name=user_account.get("accountName"),
            domain=user_account.get("domainName"),
            account=Account(
                uid=user_account.get("userSid"),
                type=AccountType.WINDOWS_ACCOUNT
                if user_account.get("userSid")
                else None,
            ),
            uid=user_account.get("azureAdUserId"),
            uid_alt=user_account.get("userPrincipalName"),
            full_name=user_account.get("displayName"),
        ),
        http_request=HttpRequest(
            uid=evidence.get("requestId"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_cloud_logon_session(evidence: dict) -> EvidenceArtifacts:
    """
    Handles cloud logon session evidence.
    """
    return EvidenceArtifacts(
        connection_info=NetworkConnectionInfo(
            session=Session(
                uid=evidence.get("sessionId"),
                created_time_dt=evidence.get("startUtcDateTime"),
            )
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_ip(evidence: dict) -> EvidenceArtifacts:
    """
    Handles IP evidence.
    """

    location = evidence.get("location") or {}
    return EvidenceArtifacts(
        src_endpoint=NetworkEndpoint(
            ip=evidence.get("ipAddress"),
            location=GeoLocation(
                country=evidence.get("countryLetterCode"),
                region=location.get("state"),
                city=location.get("city"),
                long=location.get("longitude"),
                lat=location.get("latitude"),
            ),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_iot_device(evidence: dict) -> EvidenceArtifacts:
    """
    Handles IoT device evidence according to OCSF Device schema and Microsoft Graph mapping.
    """
    agent = None
    if agent_id := evidence.get("ioTSecurityAgentId"):
        agent = Agent(uid=agent_id)

    network_interfaces = [
        NetworkInterface(
            mac=nic.get("macAddress"),
            ip=nic.get("ipAddress", {}).get("ipAddress"),
        )
        for nic in evidence.get("nics", [])
    ]

    location = None
    site = evidence.get("site")
    zone = evidence.get("zone")

    if site or zone:
        location_desc = f"{site} - {zone}" if site and zone else site or zone
        if location_desc:
            location = GeoLocation(desc=location_desc)

    owner = None
    if owners := evidence.get("owners"):
        owner = User(name=owners[0])

    url = None
    if source_ref := evidence.get("sourceRef", {}).get("url"):
        url = Url(url_string=source_ref)

    return EvidenceArtifacts(
        device=Device(
            agent_list=[agent] if agent else None,
            desc=evidence.get("deviceType"),
            hw_info=DeviceHwInfo(
                serial_number=evidence.get("serialNumber"),
                vendor_name=evidence.get("manufacturer"),
            ),
            ip=evidence.get("ipAddress", {}).get("ipAddress"),
            is_trusted=evidence.get("isAuthorized"),
            location=location,
            mac=evidence.get("macAddress"),
            model=evidence.get("model"),
            name=evidence.get("deviceName"),
            network_interfaces=network_interfaces or None,
            owner=owner,
            os=OperatingSystem(
                name=evidence.get("operatingSystem"),
            ),
            type=EndpointType.IOT,
            uid=evidence.get("deviceId"),
            vendor_name=evidence.get("source"),
        ),
        url=url,
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_file_details(file_details: dict) -> File:
    """
    Handles file details according to OCSF File schema and Microsoft Graph mapping.
    """

    hashes = []
    if md5_value := file_details.get("md5"):
        md5 = Fingerprint(value=md5_value, algorithm=HashAlgorithm.MD5)
        hashes.append(md5)
    if sha1_value := file_details.get("sha1"):
        sha1 = Fingerprint(value=sha1_value, algorithm=HashAlgorithm.SHA1)
        hashes.append(sha1)
    if sha256_value := file_details.get("sha256"):
        sha256 = Fingerprint(value=sha256_value, algorithm=HashAlgorithm.SHA256)
        hashes.append(sha256)

    return File(
        name=file_details.get("fileName"),
        path=file_details.get("filePath"),
        size=file_details.get("fileSize"),
        hashes=hashes,
    )


def _normalize_process(evidence: dict) -> EvidenceArtifacts:
    """
    Handles process evidence according to OCSF Process schema and Microsoft Graph mapping.
    """

    if file := evidence.get("imageFile"):
        file = _normalize_file_details(file)
    if user := evidence.get("userAccount"):
        user = User(
            name=user.get("accountName"),
            domain=user.get("domainName"),
            uid=user.get("azureAdUserId"),
            full_name=user.get("displayName"),
            uid_alt=user.get("userPrincipalName"),
        )

    parent_process = None
    if parent_pid := evidence.get("parentProcessId"):
        if parent_pid != evidence.get("processId"):
            parent_process = Process(pid=parent_pid)

    return EvidenceArtifacts(
        process=Process(
            cmd_line=evidence.get("processCommandLine"),
            created_time_dt=evidence.get("processCreationDateTime"),
            pid=evidence.get("processId"),
            user=user,
            file=file,
            parent_process=parent_process,
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_device(evidence: dict) -> EvidenceArtifacts:
    """
    Handles device evidence according to OCSF Device schema and Microsoft Graph mapping.
    """

    risk_level_map = {
        "informational": RiskLevel.INFO,
        "low": RiskLevel.LOW,
        "medium": RiskLevel.MEDIUM,
        "high": RiskLevel.HIGH,
    }

    # TODO: Evaluate mappings for additional fields
    # defenderAvStatus
    # healthStatus
    # loggedOnUsers
    # onboardingStatus
    # osPlatform
    # rbacGroupId
    # rbacGroupName
    # vmMetadata

    operating_system = None
    if os_version := evidence.get("version"):
        os_type = OSType.WINDOWS if os_version.startswith("Windows") else OSType.OTHER
        operating_system = OperatingSystem(
            name=os_version,
            type=os_type,
            build=str(evidence.get("osBuild")),
        )

    return EvidenceArtifacts(
        device=Device(
            uid=evidence.get("azureAdDeviceId"),
            uid_alt=evidence.get("mdeDeviceId"),
            os=operating_system,
            hostname=evidence.get("hostName"),
            domain=evidence.get("dnsDomain") or evidence.get("ntDomain"),
            ip=evidence.get("lastIpAddress"),
            network_interfaces=[
                NetworkInterface(
                    ip=interface,
                )
                for interface in evidence.get("ipInterfaces", [])
            ],
            risk_level=risk_level_map.get(evidence.get("riskScore"), RiskLevel.OTHER),
            first_seen_time_dt=evidence.get("firstSeenDateTime"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_file(evidence: dict) -> EvidenceArtifacts:
    """
    Handles file evidence according to OCSF File schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        file=_normalize_file_details(evidence.get("fileDetails")),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_analyzed_message(evidence: dict) -> EvidenceArtifacts:
    """
    Handles analyze message evidence according to OCSF Email schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        email=Email(
            subject=evidence.get("subject"),
            to=[evidence.get("recipientEmailAddress")],
            from_=evidence.get("p1sender", {}).get("emailAddress"),
            from_mailbox=evidence.get("p2sender", {}).get("emailAddress"),
            received_time_dt=evidence.get("receivedDateTime"),
            urls=[Url(url_string=url) for url in evidence.get("urls", [])],
            x_originating_ip=to_list(evidence.get("senderIp")),
            message_uid=evidence.get("internetMessageId"),
            uid=evidence.get("networkMessageId"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_url(evidence: dict) -> EvidenceArtifacts:
    """
    Handles URL evidence according to OCSF Url schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        url=Url(
            url_string=evidence.get("url"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_registry_key(evidence: dict) -> EvidenceArtifacts:
    """
    Handles registry key evidence according to OCSF RegistryKey schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        reg_key=RegKey(
            path=evidence.get("registryKey"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_registry_value(evidence: dict) -> EvidenceArtifacts:
    """
    Handles registry value evidence according to OCSF RegistryValue schema and Microsoft Graph mapping.
    """

    value_type_map = {
        "Unknown": RegistryValueType.UNKNOWN,
        "String": RegistryValueType.REG_SZ,
        "ExpandString": RegistryValueType.REG_EXPAND_SZ,
        "MultiString": RegistryValueType.REG_MULTI_SZ,
        "Dword": RegistryValueType.REG_DWORD,
        "Binary": RegistryValueType.REG_BINARY,
    }

    return EvidenceArtifacts(
        reg_value=RegValue(
            path=evidence.get("registryKey"),
            name=evidence.get("registryValueName"),
            type=value_type_map.get(
                evidence.get("registryValueType"), RegistryValueType.UNKNOWN
            ),
            # TODO: mdeDeviceId
            # TODO: registryValue
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_status(status: str) -> DetectionStatus:
    map = {
        "unknown": DetectionStatus.UNKNOWN,
        "new": DetectionStatus.NEW,
        "inProgress": DetectionStatus.IN_PROGRESS,
        "resolved": DetectionStatus.RESOLVED,
    }

    return map.get(status, DetectionStatus.OTHER)


def _normalize_severity(severity: str) -> Severity:
    map = {
        "unknown": Severity.UNKNOWN,
        "informational": Severity.INFORMATIONAL,
        "low": Severity.LOW,
        "medium": Severity.MEDIUM,
        "high": Severity.HIGH,
    }

    return map.get(severity, Severity.OTHER)


def _normalize_alert_classification(verdict: str) -> Verdict:
    map = {
        "unknown": Verdict.UNKNOWN,
        "falsePositive": Verdict.FALSE_POSITIVE,
        "truePositive": Verdict.TRUE_POSITIVE,
        "informationalExpectedActivity": Verdict.BENIGN,
    }

    return map.get(verdict, Verdict.OTHER)


def _normalize_evidence_verdict(evidence: dict) -> Verdict:
    verdict_map = {
        "unknown": Verdict.UNKNOWN,
        "suspicious": Verdict.SUSPICIOUS,
        "malicious": Verdict.SECURITY_RISK,
        "noThreatsFound": Verdict.FALSE_POSITIVE,
    }

    # TOODO: apply additional logic to determine the verdict based on detection and remediation status
    # detection_status = evidence.get("detectionStatus")
    # remediation_status = evidence.get("remediationStatus")
    verdict = evidence.get("verdict")

    return verdict_map.get(verdict, Verdict.OTHER)


def _normalize_cloud_application(evidence: dict) -> EvidenceArtifacts:
    """
    Handles cloud application evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        api=Api(
            service=Service(
                name=evidence.get("displayName"),
                uid=str(evidence.get("appId")),
            )
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_azure_resource(evidence: dict) -> ResourceDetails:
    """
    Handles Azure cloud resources evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    return ResourceDetails(
        uid=evidence.get("resourceId"),
        type=evidence.get("resourceType"),
        name=evidence.get("resourceName"),
    )


def _normalize_azure_resource_evidence(evidence: dict) -> EvidenceArtifacts:
    """
    Handles AWS cloud resources evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        resources=[_normalize_azure_resource(evidence)],
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_amazon_resource(evidence: dict) -> EvidenceArtifacts:
    """
    Handles AWS cloud resources evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    return EvidenceArtifacts(
        resources=[
            ResourceDetails(
                uid=evidence.get("amazonResourceId"),
                type=evidence.get("resourceType"),
                name=evidence.get("resourceName"),
            )
        ],
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_google_resource(evidence: dict) -> EvidenceArtifacts:
    """
    Handles Google cloud resources evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    # TODO: Determine how to build a valid resource ID for Google Cloud resources
    return EvidenceArtifacts(
        resources=[
            ResourceDetails(
                type=evidence.get("resourceType"),
                name=evidence.get("resourceName"),
            )
        ],
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_blob_container_evidence(evidence: dict) -> EvidenceArtifacts:
    """
    Handles Azure Blob Storage container evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    storage_resource = evidence.get("storageResource", {})
    return EvidenceArtifacts(
        resources=[
            ResourceDetails(
                type=storage_resource.get("resourceType"),
                name=storage_resource.get("resourceName"),
                uid=storage_resource.get("resourceId"),
            ),
        ],
        databucket=Databucket(
            name=evidence.get("name"),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_algorithm(algorithm: str) -> HashAlgorithm:
    """
    Maps algorithm string to OCSF HashAlgorithm enum.
    """
    algorithm_map = {
        "md5": HashAlgorithm.MD5,
        "sha1": HashAlgorithm.SHA1,
        "sha256": HashAlgorithm.SHA256,
        "sha512": HashAlgorithm.SHA512,
    }
    return algorithm_map.get(algorithm, HashAlgorithm.UNKNOWN)


def _normalize_blob_evidence(evidence: dict) -> EvidenceArtifacts:
    """
    Handles Azure Blob Storage evidence according to OCSF Cloud schema and Microsoft Graph mapping.
    """

    blob_container = evidence.get("blobContainer", {})
    storage_resource = blob_container.get("storageResource", {})
    return EvidenceArtifacts(
        resources=[
            ResourceDetails(
                type=storage_resource.get("resourceType"),
                name=storage_resource.get("resourceName"),
                uid=storage_resource.get("resourceId"),
            ),
        ],
        databucket=Databucket(
            name=blob_container.get("name"),
            file=File(
                name=evidence.get("name"),
                hashes=[
                    Fingerprint(
                        value=hash.get("value"),
                        algorithm=_normalize_algorithm(hash.get("algorithm")),
                    )
                    for hash in evidence.get("fileHashes", [])
                ],
            ),
        ),
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_security_group_evidence(evidence: dict) -> EvidenceArtifacts:
    return EvidenceArtifacts(
        resources=[
            ResourceDetails(
                group=Group(
                    uid=evidence.get("securityGroupId"),
                    name=evidence.get("displayName"),
                )
            )
        ],
        verdict=_normalize_evidence_verdict(evidence),
    )


def _normalize_malware_evidence(evidence: dict) -> list[EvidenceArtifacts]:
    results = []
    for file in evidence.get("files", []):
        results.append(_normalize_file(file))
    for process in evidence.get("processes", []):
        results.append(_normalize_process(process))
    return results


evidence_handlers = {
    "#microsoft.graph.security.userEvidence": _normalize_user,
    "#microsoft.graph.security.cloudLogonSessionEvidence": _normalize_cloud_logon_session,
    "#microsoft.graph.security.ipEvidence": _normalize_ip,
    "#microsoft.graph.security.iotDeviceEvidence": _normalize_iot_device,
    "#microsoft.graph.security.processEvidence": _normalize_process,
    "#microsoft.graph.security.deviceEvidence": _normalize_device,
    "#microsoft.graph.security.fileEvidence": _normalize_file,
    "#microsoft.graph.security.analyzedMessageEvidence": _normalize_analyzed_message,
    "#microsoft.graph.security.urlEvidence": _normalize_url,
    "#microsoft.graph.security.registryKeyEvidence": _normalize_registry_key,
    "#microsoft.graph.security.registryValueEvidence": _normalize_registry_value,
    "#microsoft.graph.security.cloudApplicationEvidence": _normalize_cloud_application,
    "#microsoft.graph.security.azureResourceEvidence": _normalize_azure_resource_evidence,
    "#microsoft.graph.security.amazonResourceEvidence": _normalize_amazon_resource,
    "#microsoft.graph.security.googleResourceEvidence": _normalize_google_resource,
    "#microsoft.graph.security.blobContainerEvidence": _normalize_blob_container_evidence,
    "#microsoft.graph.security.blobEvidence": _normalize_blob_evidence,
    "#microsoft.graph.security.securityGroupEvidence": _normalize_security_group_evidence,
    "#microsoft.graph.security.malwareEvidence": _normalize_malware_evidence,
}


def convert_to_ocsf(event: dict):
    evidences: list[EvidenceArtifacts] = []
    for evidence in event.get("evidence", []):
        evidence_type = evidence.get("@odata.type")
        handler = evidence_handlers.get(evidence_type)
        if not handler:
            continue

        result = handler(evidence)
        if not isinstance(result, list):
            result = [result]

        evidences.extend(result)

    process_by_pid = {
        evidence.process.pid: evidence.process
        for evidence in evidences
        if evidence.process and evidence.process.pid
    }

    # set parent process references
    for process in process_by_pid.values():
        if process.parent_process:
            if parent_process := process_by_pid.get(process.parent_process.pid):
                process.parent_process = parent_process

    return DetectionFinding(
        activity=DetectionActivity.OTHER,
        is_alert=True,
        finding_info=FindingInformation(
            analytic=Analytic(
                uid=event.get("detectorId"),
            ),
            desc=event.get("description"),
            src_url=event.get("alertWebUrl"),
            title=event.get("title"),
            product=Product(
                uid=event.get("serviceSource"),
                feature=Feature(
                    uid=event.get("detectionSource"),
                ),
                name=event.get("productName"),
            ),
            created_time_dt=event.get("createdDateTime"),
            modified_time_dt=event.get("lastUpdateDateTime"),
            first_seen_time_dt=event.get("firstActivityDateTime"),
            last_seen_time_dt=event.get("lastActivityDateTime"),
            tags=[KeyValueObject(name=tag) for tag in event.get("systemTags", [])],
            uid=event.get("id"),
            uid_alt=event.get("providerAlertId"),
        ),
        message=event.get("title"),
        metadata=Metadata(
            correlation_uid=event.get("incidentId"),
            event_code=event.get("detectorId"),
            profiles=[Profile.DATETIME, Profile.INCIDENT],
            tenant_uid=event.get("tenantId"),
            uid=event.get("id"),
        ),
        assignee=User(name=event.get("assignedTo")),
        evidences=evidences,
        status=_normalize_status(event.get("status")),
        severity=_normalize_severity(event.get("severity")),
        verdict=_normalize_alert_classification(event.get("classification")),
        time_dt=event.get("lastUpdateDateTime"),
    )


def normalize_event(event: dict) -> Event:
    return Event(
        raw_event=event,
        event_timestamp=event["lastUpdateDateTime"],
        ioc=EventIOCInfo(
            external_id=event["detectorId"],
            external_name=event["title"],
            has_ioc_definition=event["detectionSource"] == "customDetection",
            mitre_techniques=event["mitreTechniques"],
        ),
        vendor_item_ref=VendorRef(
            id=event["id"],
            title=event["title"],
            url=event.get("alertWebUrl", ""),
            created=event["createdDateTime"],
        ),
        vendor_group_ref=VendorRef(
            id=event["incidentId"],
            title=event["incident"].get("displayName", ""),
            url=event["incident"].get("incidentWebUrl", ""),
            created=event["incident"].get("createdDateTime"),
        ),
        ocsf=convert_to_ocsf(event),
    )


class MsXdrV1EventSync(EventSync):
    name = "Fetch Events Framework for Microsoft"

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: MsXdrV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: MsXdrV1Api = self.integration.get_api()

        headers = {
            "Prefer": "include-unknown-enum-members",
        }

        # There are some subtle edge cases related to paging, in which an alert being updated midway through paging, causing its own page to rise (no
        # problem since we'd just get a dupe of it) and causing the alert whose place it's taking to fall to an earlier page (problem since we may have already
        # retrieved that page).  To avoid this, we sort the query by lastUpdateDateTime *desc* and set the upper bound to the current time.
        query_upper_bound = datetime.datetime.now().isoformat() + "Z"

        params = {
            "$select": "*",
            "$filter": f"lastUpdateDateTime gt {bookmark.latest_event_update_datetime} and lastUpdateDateTime lt {query_upper_bound}",
            "$orderby": "lastUpdateDateTime desc",
        }

        last_update_datetime = parse_datetime(bookmark.latest_event_update_datetime)
        alerts = []
        for alert in api.enumerate(api.get_alerts, params=params, headers=headers):
            current = parse_datetime(alert["lastUpdateDateTime"])
            if current > last_update_datetime:
                last_update_datetime = current

            alerts.append(alert)

        # Enrich each alert with its incident info.
        # To grab the incident info, we make a follow up call to the incidents endpoint, batching to avoid hitting
        # the 414 error (URI too long).  In any case, the Incidents endpoint has a max page size of 50 so we can't really get any
        # efficiencies from a large batch size.
        unique_incident_ids = list({a["incidentId"] for a in alerts})
        incidents = {}
        batch_size = 250
        batches = [
            unique_incident_ids[i : i + batch_size]
            for i in range(0, len(unique_incident_ids), batch_size)
        ]
        for batch in batches:
            incident_params = {
                "$filter": f"id in ('" + "', '".join(batch) + "')",
            }
            for incident in api.enumerate(
                api.get_incidents, params=incident_params, headers=headers
            ):
                incidents[incident["id"]] = incident

        for alert in alerts:
            alert["incident"] = incidents.get(alert["incidentId"])
            yield alert

        bookmark.latest_event_update_datetime = format_datetime(last_update_datetime)

    def get_permission_checks(self):
        return [ReadAlerts]
