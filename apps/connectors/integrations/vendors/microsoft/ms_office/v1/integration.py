from microsoft_client.exceptions import MicrosoftHTTP<PERSON>rror

from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_email import (
    MsOfficeV1DeleteEmail,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_email_by_upn import (
    MsOfficeV1DeleteEmailByUPN,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_mailbox_rule import (
    MsOfficeV1DeleteMailboxRule,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.delete_mailbox_rule_by_upn import (
    MsOfficeV1DeleteMailboxRuleByUPN,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.restore_email import (
    MsOfficeV1RestoreEmail,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.actions.restore_email_by_upn import (
    MsOfficeV1RestoreEmailByUPN,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)

from .api import MsOfficeV1Api


class MsOfficeV1Integration(AadClientMixin, Integration):
    api_class = MsOfficeV1Api
    exception_types = (MicrosoftHTTPError,)
    actions = (
        MsOfficeV1DeleteEmail,
        MsOfficeV1DeleteEmailByUPN,
        MsOfficeV1RestoreEmail,
        MsOfficeV1RestoreEmailByUPN,
        MsOfficeV1DeleteMailboxRule,
        MsOfficeV1DeleteMailboxRuleByUPN,
    )
    critical_health_checks = (ConnectionHealthCheck,)
