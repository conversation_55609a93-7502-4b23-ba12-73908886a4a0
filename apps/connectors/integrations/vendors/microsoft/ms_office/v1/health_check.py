from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class SimplePermissionsCheck(IntegrationPermissionsHealthCheck):
    allowed_values = []

    def _has_permission(self, permission: str) -> bool:
        return self.integration.invoke("has_permission", permission=permission)

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            if any(
                self._has_permission(permission) for permission in self.allowed_values
            ):
                return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            pass

        return IntegrationHealthCheckResult.FAILED


class ReadWriteMailMessage(SimplePermissionsCheck):
    name = "Read and Write Mail Messages"
    description = "Read and Write Mail Messages in Microsoft Graph API"
    value = "Read and Write Mail Messages"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Mail.ReadWrite",
    ]


class ReadWriteMailboxSettings(SimplePermissionsCheck):
    name = "Read and Write Mailbox Settings"
    description = "Read and Write Mailbox Settings in Microsoft Graph API"
    value = "Read and Write Mailbox Settings"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "MailboxSettings.ReadWrite",
    ]
