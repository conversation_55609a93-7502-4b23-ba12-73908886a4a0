from apps.connectors.integrations.actions.email import (
    EmailActionByUPNArgs,
    EmailActionResult,
    RestoreEmailByUPNAction,
)
from apps.connectors.integrations.schemas import ErrorDetail, Message
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.api import (
    MsOfficeV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.health_check import (
    ReadWriteMailMessage,
)


class MsOfficeV1RestoreEmailByUPN(RestoreEmailByUPNAction):
    """
    Restore email by UPN action for Microsoft Office 365 integration.
    """

    def execute(self, args: EmailActionByUPNArgs, **kwargs) -> EmailActionResult:
        api: MsOfficeV1Api = self.integration.get_api()

        params = {
            f"$filter": f"internetMessageId eq '{args.message_id.value}'",
            "$select": "id",
        }

        messages = api.get_messages(
            args.upn.value, "recoverableitemsdeletions", params=params
        )["value"]
        if not messages:
            return EmailActionResult(
                error=ErrorDetail(
                    message=f"Message with ID {args.message_id.value} not found."
                )
            )

        api.move_message(args.upn.value, messages[0]["id"], "inbox")

        return EmailActionResult(
            result=Message(message=f"Message with ID {args.message_id.value} restored.")
        )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteMailMessage]  # pragma: no cover
