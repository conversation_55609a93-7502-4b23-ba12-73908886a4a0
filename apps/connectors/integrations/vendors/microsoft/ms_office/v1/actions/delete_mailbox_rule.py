from apps.connectors.integrations.actions.email import (
    DeleteMailboxRuleAction,
    DeleteMailboxRuleArgs,
    DeleteMailboxRuleResult,
)
from apps.connectors.integrations.schemas import ErrorDetail, Message
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.api import (
    MsOfficeV1Api,
)
from apps.connectors.integrations.vendors.microsoft.ms_office.v1.health_check import (
    ReadWriteMailboxSettings,
)


class MsOfficeV1DeleteMailboxRule(DeleteMailboxRuleAction):
    """
    Delete mailbox rule action for Microsoft Office 365 integration.
    """

    def execute(self, args: DeleteMailboxRuleArgs, **kwargs) -> DeleteMailboxRuleResult:
        api: MsOfficeV1Api = self.integration.get_api()

        response = api.list_message_rules(
            user_id=args.user_id.value,
            params={"$filter": f"displayName eq '{args.rule_id.value}'"},
        )
        rules = response["value"]
        if not rules:
            return DeleteMailboxRuleResult(
                error=ErrorDetail(
                    message=f"Rule with ID {args.rule_id.value} not found."
                )
            )
        if len(rules) > 1:
            return DeleteMailboxRuleResult(
                error=ErrorDetail(
                    message=f"Multiple rules found with ID {args.rule_id.value}. "
                    f"Please specify a unique rule ID."
                )
            )

        api.delete_message_rule(args.user_id.value, rules[0]["id"])

        return DeleteMailboxRuleResult(
            result=Message(message=f"Rule with ID {args.rule_id.value} deleted.")
        )

    def get_permission_checks(self, *args, **kwargs):
        return [ReadWriteMailboxSettings]
