from requests import HTTPError

from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
    SimplePermissionsCheck,
)


class ReadAll(IntegrationPermissionsHealthCheck):
    name = "Read all directory data"
    description = "Read user profiles, groups, group members, etc."
    value = "Directory.Read.All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke(
                "has_permission", permission="Directory.Read.All"
            )
            if response:
                return IntegrationHealthCheckResult.PASSED
            else:
                return IntegrationHealthCheckResult.FAILED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class UserReadAll(SimplePermissionsCheck):
    name = "User Read All"
    description = "Allows the app to read user profiles"
    value = "User Read All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["User.Read.All", "User.ReadWrite.All", "Directory.ReadWrite.All"]


class UserManageIdentitiesAll(SimplePermissionsCheck):
    name = "User Manage Identities All"
    description = "Allows the app to manage identities of users in the organization"
    value = "User Manage Identities All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "User.ManageIdentities.All",
        "User.EnableDisableAccount.All",
        "User.ReadWrite.All",
        "Directory.ReadWrite.All",
    ]


class RevokeSessions(SimplePermissionsCheck):
    name = "Revoke Sessions"
    description = "Allows the app to revoke sessions for a user"
    value = "Revoke Sessions"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["User.RevokeSessions.All"]


class IdentityRiskyUserReadWriteAll(SimplePermissionsCheck):
    name = "Identity Risky User Read Write All"
    description = "Allows the app to read and write risky user information"
    value = "Identity Risky User Read Write All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["IdentityRiskyUser.ReadWrite.All"]


class AuditLogReadAll(SimplePermissionsCheck):
    name = "Audit Log Read All"
    description = "Allows the app to read audit logs"
    value = "Audit Log Read All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["AuditLog.Read.All", "Directory.Read.All"]


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.client.service_principal_id
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
        else:
            return IntegrationHealthCheckResult.PASSED
