from microsoft_client.exceptions import MicrosoftHTTPError

from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.actions.event_sync_from_artifact import (
    AzureAdV1EventSyncFromArtifact,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.delete_ioc import (
    MsXdrV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_data_sources import (
    MsXdrV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_iocs import (
    MsXdrV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.save_ioc import (
    MsXdrV1SaveIoc,
)

from .actions.confirm_risky_user import (
    AzureAdV1ConfirmRiskyUser,
    AzureAdV1ConfirmRiskyUserByUPN,
)
from .actions.disable_user_login import (
    AzureAdV1DisableUserLogin,
    AzureAdV1DisableUserLoginByUPN,
)
from .actions.dismiss_risky_user import (
    AzureAdV1DismissRiskyUser,
    AzureAdV1DismissRiskyUserByUPN,
)
from .actions.enable_user_login import (
    AzureAdV1EnableUserLogin,
    AzureAdV1EnableUserLoginByUPN,
)
from .actions.get_external_user_profile_link import AzureAdV1GetExternalUserProfileLink
from .actions.get_hosts import AzureAdV1HostSync
from .actions.get_sign_in_logs_by_ip import AzureAdV1GetSignInLogsByIp
from .actions.get_sign_in_logs_by_user import (
    AzureAdV1GetSignInLogsByUPN,
    AzureAdV1GetSignInLogsByUser,
)
from .actions.get_user_info import AzureAdV1GetUserInfo, AzureAdV1GetUserInfoByUPN
from .actions.reset_user_password import (
    AzureAdV1ResetUserPassword,
    AzureAdV1ResetUserPasswordByUPN,
)
from .actions.revoke_user_sessions import (
    AzureAdV1RevokeUserSessions,
    AzureAdV1RevokeUserSessionsByUPN,
)
from .api import AzureAdV1Api
from .health_check import ConnectionHealthCheck


class AzureAdV1Integration(AadClientMixin, Integration):
    api_class = AzureAdV1Api
    exception_types = (MicrosoftHTTPError,)
    actions = (
        AzureAdV1HostSync,
        AzureAdV1EventSyncFromArtifact,
        AzureAdV1ConfirmRiskyUser,
        AzureAdV1ConfirmRiskyUserByUPN,
        AzureAdV1DismissRiskyUser,
        AzureAdV1DismissRiskyUserByUPN,
        AzureAdV1DisableUserLogin,
        AzureAdV1DisableUserLoginByUPN,
        AzureAdV1EnableUserLogin,
        AzureAdV1EnableUserLoginByUPN,
        AzureAdV1ResetUserPassword,
        AzureAdV1ResetUserPasswordByUPN,
        AzureAdV1RevokeUserSessions,
        AzureAdV1RevokeUserSessionsByUPN,
        AzureAdV1GetUserInfo,
        AzureAdV1GetUserInfoByUPN,
        AzureAdV1GetExternalUserProfileLink,
        AzureAdV1GetSignInLogsByUser,
        AzureAdV1GetSignInLogsByUPN,
        AzureAdV1GetSignInLogsByIp,
        MsXdrV1DeleteIoc,
        MsXdrV1ListDataSources,
        MsXdrV1ListIocs,
        MsXdrV1SaveIoc,
    )
    critical_health_checks = (ConnectionHealthCheck,)
