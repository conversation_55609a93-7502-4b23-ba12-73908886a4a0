from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import AzureAdV1Integration
from .settings import AzureAdV1Settings


class AzureAdV1TemplateVersion(TemplateVersion):
    integration = AzureAdV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = AzureAdV1Settings
