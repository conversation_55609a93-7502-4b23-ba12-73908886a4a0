from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class AzureAdV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Entra ID Fetch Devices Settings")

    fetch_from_last_x_days: int = Field(
        title="Fetch devices with activity in the last X days",
        description="Enter a value to fetch devices with activity within those number of days. "
        "A value of 0 will fetch all devices (from the beginning of time).",
        default=30,
    )
    fetch_disabled_devices: bool = Field(
        title="Fetch disabled devices",
        description="Select whether to fetch devices where Device Enabled field equals No.",
        default=True,
    )
    fetch_entra_joined_devices: bool = Field(
        title="Fetch Entra joined devices",
        description="Select whether to fetch Entra joined devices.",
        default=True,
    )
    include_device_owner: bool = Field(
        title="Include device owner",
        description="Select whether to include device ownership (username and email) information.",
        default=True,
    )
    include_device_groups: bool = Field(
        title="Include device groups",
        description="Select whether to include the Entra ID groups for every device.",
        default=True,
    )
    fetch_by_groups: str = Field(
        title="Fetch by Entra ID groups",
        description="Specify a comma-separated list of Entra ID groups. If supplied, this integration will "
        "only fetch devices with any of the groups provided.",
        default_factory=str,
    )
    custom_filter_expression: str = Field(
        title="Custom filter expression for fetching devices",
        description="Enter a filter expression to further filter devices from the fetch. For more "
        "information, see https://learn.microsoft.com/en-us/graph/filter-query-parameter and "
        "https://learn.microsoft.com/en-us/graph/api/resources/device?view=graph-rest-1.0#properties",
        default_factory=str,
    )


AzureAdV1Settings = create_settings_model(
    "Entra ID Advanced Settings",
    {
        IntegrationActionType.HOST_SYNC: AzureAdV1HostSyncSettings,
    },
)
