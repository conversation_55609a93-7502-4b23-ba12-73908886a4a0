from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api


class AzureAdV1Api(MsXdrV1Api):
    def confirm_risky_user(self, user_id):
        return self.client.identity_protection.risky_users.confirm([user_id])

    def dismiss_risky_user(self, user_id):
        return self.client.identity_protection.risky_users.dismiss([user_id])

    def get_devices(self, params=None):
        return self.client.devices.list(params=params)

    def get_risky_user(self, user_id, params=None):
        return self.client.identity_protection.risky_users.get(user_id, params=params)

    def get_user(self, user_id, params=None):
        return self.client.users.get(user_id, params=params)

    def revoke_user_sessions(self, user_id, params=None):
        return self.client.users.revoke_signin_sessions(user_id, params=params)

    def update_user(self, user_id, data, params=None):
        return self.client.users.update(user_id, data, params=params)

    def get_sign_in_logs(self, params=None):
        return self.client.audit_logs.signin.list(params=params)
