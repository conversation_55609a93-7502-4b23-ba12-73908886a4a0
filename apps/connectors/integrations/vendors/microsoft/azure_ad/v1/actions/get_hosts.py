from datetime import timed<PERSON><PERSON>
from typing import Generator

from django.utils import timezone

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    ReadAll,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.settings import (
    AzureAdV1HostSyncSettings,
)
from apps.connectors.utils import split_cs


def normalize_host(host_data: dict):
    os_name = " ".join(
        [
            host_data.get("operatingSystem") or "",
            host_data.get("operatingSystemVersion") or "",
        ]
    ).strip()
    os_name = os_name or None
    hostname = host_data.get("displayName") or ""
    domain_name = host_data.get("domainName")

    group_names = [
        member["displayName"]
        for member in host_data.get("memberOf", []) or []
        if ("group" in member["@odata.type"] and "displayName" in member)
    ]

    owners = [
        OwnerAttributes(
            name=owner["displayName"],
            email=owner.get("mail") or None,
        )
        for owner in host_data.get("registeredOwners") or []
        if owner.get("displayName")
    ]

    return Host(
        source_id=host_data.get("id"),
        group_names=group_names,
        hostname=hostname,
        _domain=domain_name,
        _os_name=os_name,
        owners=owners,
        aad_id=host_data.get("deviceId"),
        last_seen=normalize_last_seen(host_data.get("approximateLastSignInDateTime")),
        source_data=host_data,
    )


class AzureAdV1HostSync(HostSync):
    settings: AzureAdV1HostSyncSettings

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        def check_group_membership(device):
            fetch_by_group_ids = split_cs(self.settings.fetch_by_groups)
            if not fetch_by_group_ids:
                return True
            if any(
                group["id"] in fetch_by_group_ids
                for group in device.get("memberOf", [])
            ):
                return True
            return False

        ########################################################
        # Construct the filter expression
        ########################################################
        filters = []
        if self.settings.fetch_from_last_x_days:
            fetch_since = (
                timezone.now() - timedelta(days=self.settings.fetch_from_last_x_days)
            ).isoformat()
            filters.append(
                f"(onPremisesLastSyncDateTime ge {fetch_since} OR approximateLastSignInDateTime ge {fetch_since})"
            )

        if not self.settings.fetch_disabled_devices:
            filters.append("accountEnabled eq true")

        if self.settings.custom_filter_expression:
            if self.settings.custom_filter_expression.startswith("("):
                filters.append(self.settings.custom_filter_expression)
            else:
                filters.append(f"({self.settings.custom_filter_expression})")
        filters_joined = " AND ".join(filters)

        api = self.integration.get_api()

        ########################################################
        # Construct a mapping from device id to owners
        ########################################################
        owners_by_device_id = {}
        params = {
            "$filter": filters_joined,
            "$select": "id",
        }
        if self.settings.include_device_owner:
            params["$expand"] = "registeredOwners($select=id, displayName, mail)"
            devices_with_owners = api.paginate(api.get_devices, params)
            for page in devices_with_owners:
                for device in page:
                    owners_by_device_id[device["id"]] = device["registeredOwners"]

        ########################################################
        # Fetch the devices and merge with owners
        ########################################################
        params = {"$filter": filters_joined}
        if self.settings.include_device_groups:
            params["$expand"] = "memberOf"
        devices_with_groups = api.paginate(api.get_devices, params)
        for page in devices_with_groups:
            for device in page:
                if not check_group_membership(device):
                    continue
                if self.settings.include_device_owner:
                    owners = owners_by_device_id.get(device["id"], [])
                    device["registeredOwners"] = owners
                if not self.settings.fetch_entra_joined_devices:
                    if device["trustType"] == "AzureAd":
                        continue

                yield device

    def get_permission_checks(self):
        return [ReadAll]
