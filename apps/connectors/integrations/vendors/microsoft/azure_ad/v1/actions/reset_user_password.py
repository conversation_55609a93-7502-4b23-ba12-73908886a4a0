from apps.connectors.integrations.actions.user import (
    ResetUserPassword,
    ResetUserPasswordByUPN,
    ResetUserPasswordResult,
    ResetUserPasswordStatus,
)
from apps.connectors.integrations.schemas import UPNIdentifierArgs, UserIdentifierArgs
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    UserManageIdentitiesAll,
)


class AzureAdV1ResetUserPassword(ResetUserPassword):
    def execute(self, args: UserIdentifierArgs) -> ResetUserPasswordResult:
        api = self.integration.get_api()

        #  Set a flag on the user's login requiring them to MFA and change their password upon next sign-in
        api.update_user(
            args.user_id.value,
            {"passwordProfile": {"forceChangePasswordNextSignInWithMfa": True}},
        )
        return ResetUserPasswordResult(result=ResetUserPasswordStatus(reset=True))

    def get_permission_checks(self, *args, **kwargs):
        return [UserManageIdentitiesAll]


class AzureAdV1ResetUserPasswordByUPN(ResetUserPasswordByUPN):
    def execute(self, args: UPNIdentifierArgs) -> ResetUserPasswordResult:
        api = self.integration.get_api()

        #  Set a flag on the user's login requiring them to MFA and change their password upon next sign-in
        api.update_user(
            args.upn.value,
            {"passwordProfile": {"forceChangePasswordNextSignInWithMfa": True}},
        )
        return ResetUserPasswordResult(result=ResetUserPasswordStatus(reset=True))

    def get_permission_checks(self, *args, **kwargs):
        return [UserManageIdentitiesAll]
