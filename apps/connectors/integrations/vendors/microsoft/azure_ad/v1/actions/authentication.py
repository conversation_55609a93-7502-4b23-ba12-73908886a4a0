from apps.connectors.integrations.schemas.ocsf import (
    Actor,
    Authentication,
    AuthenticationActivity,
    AuthorizationResult,
    Device,
    EventStatus,
    GeoLocation,
    Metadata,
    NetworkEndpoint,
    OperatingSystem,
    Policy,
    Product,
    Profile,
    RiskLevel,
    Service,
    Severity,
    User,
)

risk_level_map = {
    "none": RiskLevel.INFO,
    "low": RiskLevel.LOW,
    "medium": RiskLevel.MEDIUM,
    "high": RiskLevel.HIGH,
    "unknownfuturevalue": RiskLevel.OTHER,
}


def normalize_sign_in(sign_in: dict) -> Authentication:
    applied_polices = sign_in.get("appliedConditionalAccessPolicies", [])
    authorizations = [
        AuthorizationResult(
            policy=Policy(
                name=p.get("displayName"),
                uid=p.get("id"),
                is_applied=True if p.get("result") == "Enabled" else False,
            )
        )
        for p in applied_polices
    ]
    location = sign_in.get("location", {})
    sign_in_status = sign_in.get("status", {})
    device_detail = sign_in.get("deviceDetail", {})
    user_risk_level = sign_in.get("riskLevelAggregated")

    return Authentication(
        activity=AuthenticationActivity.LOGON,
        severity=Severity.INFORMATIONAL,
        time_dt=sign_in.get("createdDateTime"),
        message="",
        metadata=Metadata(
            event_code=None,
            product=Product(
                name="Microsoft signIn",
                vendor_name="Microsoft",
            ),
            profiles=[Profile.DATETIME, Profile.HOST],
            uid=sign_in.get("id"),
            correlation_uid=sign_in.get("id"),
        ),
        actor=Actor(
            app_name=sign_in.get("appDisplayName"),
            app_uid=sign_in.get("appId"),
            authorizations=authorizations,
            user=User(
                risk_level=risk_level_map.get(user_risk_level, RiskLevel.OTHER),
                full_name=sign_in.get("userDisplayName"),
                uid=sign_in.get("userId"),
                uid_alt=sign_in.get("userPrincipalName"),
            ),
        ),
        device=Device(
            name=device_detail.get("displayName"),
            uid=device_detail.get("deviceId"),
            is_compliant=device_detail.get("isCompliant", False),
            is_managed=device_detail.get("isManaged", False),
            os=OperatingSystem(
                name=device_detail.get("operatingSystem"),
            ),
            is_trusted=device_detail.get("trustType") is not None,
        ),
        src_endpoint=NetworkEndpoint(
            ip=sign_in.get("ipAddress"),
            location=GeoLocation(
                city=location.get("city"),
                region=location.get("state"),
                country=location.get("countryOrRegion"),
                lat=location.get("geoCoordinates", {}).get("latitude"),
                long=location.get("geoCoordinates", {}).get("longitude"),
            )
            if location
            else None,
        ),
        service=Service(
            name=sign_in.get("resourceDisplayName"),
            uid=sign_in.get("resourceId"),
        ),
        status=EventStatus.SUCCESS
        if sign_in_status.get("errorCode") == 0
        else EventStatus.FAILURE,
        status_code=sign_in_status.get("additionalDetails"),
        status_detail=sign_in_status.get("failureReason"),
    )
