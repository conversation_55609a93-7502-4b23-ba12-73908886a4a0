from apps.connectors.integrations.actions.user.risky_user import (
    DismissRiskyUser,
    DismissRiskyUserByUPN,
    RiskyUserResult,
    RiskyUserState,
    RiskyUserStatus,
)
from apps.connectors.integrations.schemas import (
    UPNIdentifierArgs,
    UserIdentifierArgs,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    IdentityRiskyUserReadWriteAll,
)


def execute(api, source_id: str) -> RiskyUserResult:
    risky_user = api.get_risky_user(source_id)
    is_processing = risky_user["isProcessing"]
    try:
        risk_state = RiskyUserState(risky_user["riskState"])
    except ValueError:
        risk_state = None

    if risk_state != RiskyUserState.DISMISSED and not is_processing:
        api.dismiss_risky_user(source_id)
        is_processing = True

    return RiskyUserResult(
        result=RiskyUserStatus(
            is_processing=is_processing,
            risk_state=risk_state,
        )
    )


class AzureAdV1DismissRiskyUser(DismissRiskyUser):
    def execute(self, args: UserIdentifierArgs) -> RiskyUserResult:
        api = self.integration.get_api()
        source_id = args.user_id.value

        return execute(api, source_id)

    def get_permission_checks(self, *args, **kwargs):
        return [IdentityRiskyUserReadWriteAll]


class AzureAdV1DismissRiskyUserByUPN(DismissRiskyUserByUPN):
    def execute(self, args: UPNIdentifierArgs) -> RiskyUserResult:
        api = self.integration.get_api()
        user = api.get_user(args.upn.value)
        source_id = user["id"]

        return execute(api, source_id)

    def get_permission_checks(self, *args, **kwargs):
        return [IdentityRiskyUserReadWriteAll]
