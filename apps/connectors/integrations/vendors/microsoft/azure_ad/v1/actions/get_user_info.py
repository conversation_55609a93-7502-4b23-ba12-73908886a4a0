from apps.connectors.integrations.actions.user import (
    GetUserInfo,
    GetUserInfoByUPN,
    UserInfoResult,
)
from apps.connectors.integrations.schemas import (
    UPNIdentifierArgs,
    UserIdentifierArgs,
    ocsf,
)
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    UserReadAll,
)


def create_result(user) -> UserInfoResult:
    return UserInfoResult(
        result=ocsf.User(
            account=ocsf.Account(type=ocsf.AccountType.AZURE_AD_ACCOUNT),
            display_name=user["displayName"],
            is_enabled=user["accountEnabled"],
            uid=user["id"],
            uid_alt=user["userPrincipalName"],
        )
    )


class AzureAdV1GetUserInfo(GetUserInfo):
    def execute(self, args: UserIdentifierArgs) -> UserInfoResult:
        api = self.integration.get_api()
        params = {"$select": "id,displayName,accountEnabled,userPrincipalName"}
        user = api.get_user(args.user_id.value, params=params)
        return create_result(user)

    def get_permission_checks(self, *args, **kwargs):
        return [UserReadAll]


class AzureAdV1GetUserInfoByUPN(GetUserInfoByUPN):
    def execute(self, args: UPNIdentifierArgs) -> UserInfoResult:
        api = self.integration.get_api()
        params = {"$select": "id,displayName,accountEnabled,userPrincipalName"}
        user = api.get_user(args.upn.value, params=params)
        return create_result(user)

    def get_permission_checks(self, *args, **kwargs):
        return [UserReadAll]
