from apps.connectors.integrations.actions.external_user_profile_link import (
    ExternalUserProfileLink,
    ExternalUserProfileLinkResult,
)
from apps.connectors.integrations.schemas.identifiers import UserIdentifierArgs


class AzureAdV1GetExternalUserProfileLink(ExternalUserProfileLink):
    def execute(self, args: UserIdentifierArgs) -> ExternalUserProfileLinkResult:
        return ExternalUserProfileLinkResult(
            template=f"https://portal.azure.com/#view/Microsoft_AAD_UsersAndTenants/UserProfileMenuBlade/~/overview/userId/{args.user_id.value}"
        )

    def get_permission_checks(self, *args, **kwargs):
        return []
