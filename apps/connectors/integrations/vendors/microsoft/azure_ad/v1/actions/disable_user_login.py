from apps.connectors.integrations.actions.user import (
    DisableUser<PERSON>ogin,
    Disable<PERSON>serLoginByUPN,
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UPNIdentifierArgs, UserIdentifierArgs
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    UserManageIdentitiesAll,
)


class AzureAdV1DisableUserLogin(DisableUserLogin):
    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        api.update_user(args.user_id.value, {"accountEnabled": False})

        return UserLoginResult(result=UserLoginStatus(enabled=False))

    def get_permission_checks(self, *args, **kwargs):
        return [UserManageIdentitiesAll]


class AzureAdV1DisableUserLoginByUPN(DisableUserLoginByUPN):
    def execute(self, args: UPNIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        api.update_user(args.upn.value, {"accountEnabled": False})

        return UserLoginResult(result=UserLoginStatus(enabled=False))

    def get_permission_checks(self, *args, **kwargs):
        return [UserManageIdentitiesAll]
