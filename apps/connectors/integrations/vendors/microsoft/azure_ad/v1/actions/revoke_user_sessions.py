from apps.connectors.integrations.actions.user import (
    RevokeUserSessions,
    RevokeUserSessionsByUPN,
    RevokeUserSessionsResult,
    RevokeUserSessionsStatus,
)
from apps.connectors.integrations.schemas import UPNIdentifierArgs, UserIdentifierArgs
from apps.connectors.integrations.vendors.microsoft.azure_ad.v1.health_check import (
    RevokeSessions,
)


class AzureAdV1RevokeUserSessions(RevokeUserSessions):
    def execute(self, args: UserIdentifierArgs) -> RevokeUserSessionsResult:
        api = self.integration.get_api()

        response = api.revoke_user_sessions(args.user_id.value)

        return RevokeUserSessionsResult(
            result=RevokeUserSessionsStatus(revoked=response["value"])
        )

    def get_permission_checks(self, *args, **kwargs):
        return [RevokeSessions]


class AzureAdV1RevokeUserSessionsByUPN(RevokeUserSessionsByUPN):
    def execute(self, args: UPNIdentifierArgs) -> RevokeUserSessionsResult:
        api = self.integration.get_api()

        response = api.revoke_user_sessions(args.upn.value)

        return RevokeUserSessionsResult(
            result=RevokeUserSessionsStatus(revoked=response["value"])
        )

    def get_permission_checks(self, *args, **kwargs):
        return [RevokeSessions]
