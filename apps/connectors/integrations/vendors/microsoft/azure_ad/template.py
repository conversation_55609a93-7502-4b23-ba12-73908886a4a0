from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import AzureAdV1TemplateVersion


class AzureAdTemplate(Template):
    id = "azure_ad"
    name = "Microsoft Entra ID (Azure AD)"
    category = Template.Category.IDENTITY_SECURITY
    versions = {
        AzureAdV1TemplateVersion.id: AzureAdV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
