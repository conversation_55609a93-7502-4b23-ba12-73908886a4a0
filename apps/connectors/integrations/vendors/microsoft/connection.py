from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.mixins import AadClientConfigMixin
from apps.connectors.integrations.types import AadClientId, AadTenantId


class MicrosoftCloudPlatform(StrEnum):
    COMMERCIAL = "Commercial"
    GCC = "Government Community Cloud (gcc)"
    # Note that these platforms (gcc_high and dod) are currently not support by Critical Start.
    # They are included here for completeness but more than just uncommenting would be required to support these
    # additional platforms
    # GCC_HIGH = "Government Community Cloud High (gcc-high)"
    # DOD = "Department of Defense (dod)"


class MicrosoftConfig(AadClientConfigMixin, TemplateVersionConfig):
    microsoft_cloud_platform: MicrosoftCloudPlatform = Field(
        title="Microsoft Cloud Platform",
        default=MicrosoftCloudPlatform.COMMERCIAL,
    )
    tenant_id: AadTenantId
    client_id: AadClientId


class MicrosoftConnection(ConnectionTemplate):
    id = "ms_aad_app"
    name = "Microsoft"
    config_model = MicrosoftConfig
