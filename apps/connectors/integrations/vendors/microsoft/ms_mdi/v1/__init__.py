from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import MsMdiV1Integration
from .settings import MsMdiV1Settings


class MsMdiV1TemplateVersion(TemplateVersion):
    integration = MsMdiV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsMdiV1Settings
