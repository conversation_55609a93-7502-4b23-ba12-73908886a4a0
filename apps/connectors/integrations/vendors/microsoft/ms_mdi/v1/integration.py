from apps.connectors.integrations import Integration
from apps.connectors.integrations.mixins import AadClientMixin
from apps.connectors.integrations.vendors.microsoft.ms_mdi.v1.actions.event_sync_from_artifact import (
    MsMdiV1EventSyncFromArtifact,
)
from apps.connectors.integrations.vendors.microsoft.ms_mdi.v1.api import MsMdiV1Api
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.delete_ioc import (
    MsXdrV1DeleteIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_data_sources import (
    MsXdrV1ListDataSources,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.list_iocs import (
    MsXdrV1ListIocs,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.save_ioc import (
    MsXdrV1SaveIoc,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)


class MsMdiV1Integration(AadClientMixin, Integration):
    api_class = MsMdiV1Api
    actions = (
        MsMdiV1EventSyncFromArtifact,
        MsXdrV1DeleteIoc,
        MsXdrV1ListDataSources,
        MsXdrV1ListIocs,
        MsXdrV1SaveIoc,
    )
    critical_health_checks = (ConnectionHealthCheck,)
