from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsMdcaV1TemplateVersion


class MsMdcaTemplate(Template):
    id = "ms_mdca"
    name = "Microsoft Defender for Cloud Apps"
    category = Template.Category.SAAS_SECURITY
    versions = {
        MsMdcaV1TemplateVersion.id: MsMdcaV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
