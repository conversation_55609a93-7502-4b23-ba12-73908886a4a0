from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import MsMdcaV1Integration
from .settings import MsMdcaV1Settings


class MsMdcaV1TemplateVersion(TemplateVersion):
    integration = MsMdcaV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsMdcaV1Settings
