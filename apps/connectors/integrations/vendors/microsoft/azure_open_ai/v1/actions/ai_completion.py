from apps.connectors.integrations.actions.ai_completion import (
    <PERSON><PERSON>omple<PERSON>,
    AiCompletionArgs,
    AiCompletionResponse,
)
from apps.connectors.integrations.vendors.microsoft.azure_open_ai.v1.api import (
    AzureOpenAiV1Api,
)
from apps.connectors.integrations.vendors.microsoft.azure_open_ai.v1.settings import (
    AzureOpenAiCompletionSettings,
)


class AzureOpenAiV1AiCompletion(AiCompletion):
    settings: AzureOpenAiCompletionSettings

    def execute(self, args: AiCompletionArgs, **kwargs) -> AiCompletionResponse:
        api: AzureOpenAiV1Api = self.integration.get_api()
        response = api.completion(
            args.system_prompt_text,
            args.prompt_text,
            args.deployment_name,
            self.settings.temperature,
        )
        return AiCompletionResponse(
            response=response["response"],
            total_tokens=response["total_tokens"],
            prompt_tokens=response["prompt_tokens"],
            completion_tokens=response["completion_tokens"],
        )

    def get_permission_checks(self, *args, **kwargs):
        return []
