from apps.connectors.integrations import TemplateVersion

from .connection import AzureOpenAiV1Config, AzureOpenAiV1Connection
from .integration import AzureOpenAiV1Integration
from .settings import AzureOpenAiV1Settings


class AzureOpenAiV1TemplateVersion(TemplateVersion):
    integration = AzureOpenAiV1Integration
    id = "v1"
    name = "v1"
    config_model = AzureOpenAiV1Config
    connection_model = AzureOpenAiV1Connection
    settings_model = AzureOpenAiV1Settings
