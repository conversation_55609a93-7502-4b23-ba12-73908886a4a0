from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class AzureOpenAiV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Azure OpenAI.",
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key used to authenticate with Azure OpenAI.",
    )
    temperature: float = Field(
        title="Temperature",
        description="What sampling temperature to use, between 0 and 2.",
    )


class AzureOpenAiV1Connection(ConnectionTemplate):
    id = "azure_open_ai"
    name = "Azure OpenAI"
    config_model = AzureOpenAiV1Config
