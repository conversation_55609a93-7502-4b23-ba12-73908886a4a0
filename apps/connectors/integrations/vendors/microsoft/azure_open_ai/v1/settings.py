from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class AzureOpenAiCompletionSettings(TemplateVersionActionSettings):
    temperature: float = 0.0


AzureOpenAiV1Settings = create_settings_model(
    "AzureOpenAiV1Settings",
    {IntegrationActionType.AI_COMPLETION: AzureOpenAiCompletionSettings},
)
