from langchain.schema import HumanMessage, SystemMessage
from langchain_community.callbacks import get_openai_callback
from langchain_openai import AzureChatOpenAI

LLM_ROLE_USER = "user"
LLM_ROLE_ASSISTANT = "assistant"


class AzureOpenAiV1Api:
    """
    Client for interacting with LLM using LangChain
    """

    def __init__(
        self,
        url=None,
        api_key=None,
        **kwargs,
    ):
        self.url = url
        self.api_key = api_key

    def completion(self, system_prompt_text, prompt_text, deployment_name, temperature):
        """
        Prompts LLM using provided prompt text to get a completion
        :param system_prompt_text: System prompt to set boundaries for LLM
        :param prompt_text: Prompt text to get a completion for
        """
        self.llm = AzureChatOpenAI(
            deployment_name=deployment_name,
            openai_api_key=self.api_key,
            azure_endpoint=self.url,
            openai_api_version="2024-08-01-preview",
            temperature=temperature,
        )

        if not prompt_text:
            raise TypeError("Prompt text is required")
        messages = []

        if system_prompt_text:
            messages.append(SystemMessage(content=system_prompt_text))

        messages.append(HumanMessage(content=prompt_text))

        with get_openai_callback() as cb:
            ai_message = self.llm.invoke(messages)
            response = ai_message.content

        return {
            "response": response,
            "total_tokens": cb.total_tokens,
            "prompt_tokens": cb.prompt_tokens,
            "completion_tokens": cb.completion_tokens,
        }
