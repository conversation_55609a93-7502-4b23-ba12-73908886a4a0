from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import AzureOpenAiV1TemplateVersion


class AzureOpenAiTemplate(Template):
    is_internal = True
    id = "azure_open_ai"
    name = "Azure OpenAI"
    category = Template.Category.OPERATIONAL_TECHNOLOGY
    versions = {
        AzureOpenAiV1TemplateVersion.id: AzureOpenAiV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
