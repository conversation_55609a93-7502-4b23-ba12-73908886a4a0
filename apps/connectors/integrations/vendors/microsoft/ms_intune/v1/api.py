from ata_common.chunking import chunks
from microsoft_client.graph import MicrosoftGraphClient

GRAPH_BATCH_LIMIT = 20


class MsIntuneV1Api:
    def __init__(self, tenant_id=None, client_id=None, client_secret=None, **kwargs):
        self.client = MicrosoftGraphClient(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

        self.paginate = self.client.paginate

    def get_devices(self, params=None):
        # https://learn.microsoft.com/en-us/graph/api/resources/intune-devices-manageddevice
        return self.client.device_management.devices.list(params=params)

    def get_ethernet_mac_addresses(self, managed_devices):
        """
        Retrieves the Ethernet MAC addresses for a list of managed devices.
        Returns:
            dict: A dictionary mapping device IDs to their Ethernet MAC addresses.
        """
        lookup = {}
        for batch in chunks(managed_devices, GRAPH_BATCH_LIMIT):
            batch_request = self.__create_batch_request(batch)
            responses = self.client.device_management.devices.api.request(
                "POST",
                "/$batch",
                json=batch_request,
            )["responses"]
            lookup.update(
                {
                    response["id"]: response["body"]["ethernetMacAddress"]
                    for response in responses
                    if response["status"] == 200
                }
            )

        return lookup

    def has_permission(self, permission):
        return self.client.has_permission(permission=permission)

    @staticmethod
    def __create_batch_request(batch):
        """
        Creates a batch request to retrieve the Ethernet MAC addresses for a list of managed devices.
        """
        requests_list = [
            {
                "id": device["id"],
                "method": "GET",
                "url": f"/deviceManagement/managedDevices/{device['id']}?$select=ethernetMacAddress",
            }
            for device in batch
        ]
        return {"requests": requests_list}
