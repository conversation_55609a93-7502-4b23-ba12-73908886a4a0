from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .connection import MsIntuneV1Config, MsIntuneV1Connection
from .integration import MsIntuneV1Integration
from .settings import MsIntuneV1Settings


class MsIntuneV1TemplateVersion(TemplateVersion):
    integration = MsIntuneV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsIntuneV1Settings
