from requests import HTTPError

from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadAllManagedDevices(IntegrationPermissionsHealthCheck):
    name = "Read all managed devices"
    description = "Read Microsoft Intune devices"
    value = "DeviceManagementManagedDevices.Read.All"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke("has_permission", permission=self.value)
            result = (
                IntegrationHealthCheckResult.PASSED
                if response
                else IntegrationHealthCheckResult.FAILED
            )
        except IntegrationError:
            result = IntegrationHealthCheckResult.FAILED
        return result


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.client.service_principal_id
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
        else:
            return IntegrationHealthCheckResult.PASSED
