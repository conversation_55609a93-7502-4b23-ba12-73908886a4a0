from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.mixins import AadClientConfigMixin
from apps.connectors.integrations.types import AadClientId, AadTenantId


class MsIntuneV1Config(AadClientConfigMixin, TemplateVersionConfig):
    tenant_id: AadTenantId
    client_id: AadClientId


class MsIntuneV1Connection(ConnectionTemplate):
    id = "ms_intune"
    name = "Microsoft Intune"
    config_model = MsIntuneV1Config
