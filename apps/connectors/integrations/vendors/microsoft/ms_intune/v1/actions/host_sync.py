from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.vendors.microsoft.ms_intune.v1.health_check import (
    ReadAllManagedDevices,
)


def normalize_host(host_data: dict):
    mac_addresses = [
        host_data.get("wiFiMacAddress"),
        host_data.get("ethernetMacAddress"),
    ]
    os_name = (
        " ".join(
            [
                host_data.get("operatingSystem", ""),
                host_data.get("osVersion", ""),
            ]
        ).strip()
        or None
    )
    owners = (
        [
            OwnerAttributes(
                name=host_data.get("userDisplayName"),
                email=host_data.get("emailAddress") or None,
            )
        ]
        if host_data.get("userDisplayName")
        else []
    )

    aad_id = host_data.get("azureADDeviceId")
    if aad_id == "00000000-0000-0000-0000-000000000000":
        aad_id = None

    return Host(
        source_id=host_data["id"],
        hostname=host_data.get("deviceName"),
        mac_addresses=mac_addresses,
        _os_name=os_name,
        owners=owners,
        aad_id=aad_id,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("lastSyncDateTime")),
        source_data=host_data,
    )


class MsIntuneV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in api.paginate(api.get_devices):
            lookup = api.get_ethernet_mac_addresses(page)
            for device in page:
                if mac := lookup.get(device["id"]):
                    device["ethernetMacAddress"] = mac
                yield device

    def get_permission_checks(self):
        return [ReadAllManagedDevices]
