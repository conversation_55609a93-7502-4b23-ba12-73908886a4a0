from pydantic import ConfigDict

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class MsIntuneV1HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Microsoft Intune Fetch Managed Devices Settings")


MsIntuneV1Settings = create_settings_model(
    "MsIntuneV1Settings",
    {
        IntegrationActionType.HOST_SYNC: MsIntuneV1HostSyncSettings,
    },
)
