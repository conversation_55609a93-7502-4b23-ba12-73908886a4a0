from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MsIntuneV1TemplateVersion


class MsIntuneTemplate(Template):
    id = "ms_intune"
    name = "Microsoft Intune"
    category = Template.Category.ASSET_SOURCE
    versions = {
        MsIntuneV1TemplateVersion.id: MsIntuneV1TemplateVersion(),
    }
    vendor = Vendors.MICROSOFT
