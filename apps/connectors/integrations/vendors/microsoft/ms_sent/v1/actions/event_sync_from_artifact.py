import logging

from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync_from_artifact_base import (
    EventSyncFromArtifactBase,
    MsXdrServiceSource,
)

logger = logging.getLogger(__name__)


class MsSentV1EventSyncFromArtifact(EventSyncFromArtifactBase):
    def is_applicable(self, event) -> bool:
        ss = event.get("raw_event").get("serviceSource")
        return ss == MsXdrServiceSource.MICROSOFT_SENTINEL
