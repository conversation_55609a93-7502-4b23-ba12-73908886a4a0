from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import MsSentV1Integration
from .settings import MsSentV1Settings


class MsSentV1TemplateVersion(TemplateVersion):
    integration = MsSentV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsSentV1Settings
