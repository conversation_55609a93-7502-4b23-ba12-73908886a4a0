import logging

from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync_from_artifact_base import (
    EventSyncFromArtifactBase,
)

logger = logging.getLogger(__name__)


class MsMdiotV1EventSyncFromArtifact(EventSyncFromArtifactBase):
    def is_applicable(self, event) -> bool:  # pragma: no cover
        # Check if the event's detection source is 'microsoftDefenderIot'
        ds = event.get("raw_event").get("detectionSource")
        return ds == "microsoftDefenderForIoT"
