from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.microsoft.connection import (
    MicrosoftConnection,
)

from .integration import MsMdiotV1Integration
from .settings import MsMdiotV1Settings


class MsMdiotV1TemplateVersion(TemplateVersion):
    integration = MsMdiotV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = MicrosoftConnection
    settings_model = MsMdiotV1Settings
