from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import QualysGavV1TemplateVersion


class QualysGavTemplate(Template):
    id = "qualys_gav"
    name = "Qualys Global AssetView"
    category = Template.Category.ASSET_SOURCE
    versions = {
        QualysGavV1TemplateVersion.id: QualysGavV1TemplateVersion(),
    }
    vendor = Vendors.QUALYS
