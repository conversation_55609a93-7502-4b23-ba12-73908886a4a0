from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


# enum class with url choices
class QualysGAVUrl(StrEnum):
    US1 = "https://gateway.qg1.apps.qualys.com"
    US2 = "https://gateway.qg2.apps.qualys.com"
    US3 = "https://gateway.qg3.apps.qualys.com"
    US4 = "https://gateway.qg4.apps.qualys.com"
    EU1 = "https://gateway.qg1.apps.qualys.eu"
    EU2 = "https://gateway.qg2.apps.qualys.eu"
    EU3 = "https://gateway.qg3.apps.qualys.it"
    IN1 = "https://gateway.qg1.apps.qualys.in"
    CA1 = "https://gateway.qg1.apps.qualys.ca"
    AE1 = "https://gateway.qg1.apps.qualys.ae"
    UK1 = "https://gateway.qg1.apps.qualys.co.uk"
    AU1 = "https://gateway.qg1.apps.qualys.com.au"
    KSA1 = "https://gateway.qg1.apps.qualysksa.com"


class QualysGavV1Config(TemplateVersionConfig):
    url: QualysGAVUrl = Field(
        title="URL",
        description="API Gateway URL. See https://www.qualys.com/platform-identification/ for more information.",
        max_length=1024,
    )
    username: str = Field(
        title="Username",
        description="API Username",
        max_length=1024,
    )
    password: EncryptedStr = Field(
        title="Password",
        description="API Password",
        max_length=1024,
    )


class QualysGavV1Connection(ConnectionTemplate):
    id = "qualys_gav"
    name = "Qualys Global AssetView"
    config_model = QualysGavV1Config
