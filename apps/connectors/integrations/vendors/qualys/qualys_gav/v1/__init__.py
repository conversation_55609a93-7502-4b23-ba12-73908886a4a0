from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig

from .connection import QualysGavV1Config, QualysGavV1Connection
from .integration import QualysGavV1Integration
from .settings import QualysGavV1Settings


class QualysGavV1TemplateVersion(TemplateVersion):
    integration = QualysGavV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = QualysGavV1Connection
    settings_model = QualysGavV1Settings
