from typing import Generator

from apps.connectors.integrations.actions.host_sync import Host, HostSync, HostSyncArgs
from apps.connectors.integrations.actions.utils import normalize, normalize_last_seen
from apps.connectors.integrations.vendors.qualys.qualys_gav.v1.api import paginate
from apps.connectors.integrations.vendors.qualys.qualys_gav.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    hostname = host_data.get("assetName")

    os_name = host_data.get("operatingSystem", {}).get("osName", "")
    network_interface_list_data = host_data.get("networkInterfaceListData") or {}
    network_interface_array = network_interface_list_data.get("networkInterface", [])
    ip_addresses = []
    mac_addresses = []

    for network_interface in network_interface_array:
        if ip := network_interface.get("addressIpV4"):
            # The value can be a string or a comma separated list of strings.
            # If the list is comma separated, we want to remove the spaces if existent.
            ip = ip.replace(" ", "").split(",")
            ip_addresses.extend(ip)
        if mac := network_interface.get("macAddress"):
            mac_addresses.append(mac)

    return Host(
        source_id=str(host_data["assetId"]),
        hostname=hostname,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        last_seen=normalize_last_seen(
            [
                host_data.get("lastModifiedDate"),
                host_data.get("sensorLastUpdatedDate"),
                host_data.get("lastBoot"),
            ]
        ),
        source_data=host_data,
    )


class QualysGavV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_assets, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
