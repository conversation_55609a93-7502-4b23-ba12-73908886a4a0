from urllib.parse import urlparse

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    assets = response.get("assetListData", {}).get("asset", [])
    yield assets

    while response.get("hasMore"):
        last_seen_asset_id = response.get("lastSeenAssetId")
        response = bound_method(**kwargs, last_seen_asset_id=last_seen_asset_id)
        assets = response.get("assetListData", {})
        if ("asset" in assets) and len(assets["asset"]) > 0:
            yield assets["asset"]


class QualysGavV1Api(ApiBase):
    def __init__(self, url=None, username=None, password=None, **kwargs):
        self.username = username
        self.password = password
        self.hostname = urlparse(url).hostname

        super().__init__(base_url=url)

    def get_auth_token(self):
        data = {
            "username": self.username,
            "password": self.password,
            "token": "true",
        }

        self.session.headers.update(
            {"Content-Type": "application/x-www-form-urlencoded; charset=utf-8"}
        )
        response = self.session.post(self.url("auth"), data=data)

        return response.text

    def ensure_token(self):
        if not hasattr(self, "_token"):
            self.session.headers.pop("Authorization", None)
            self._token = self.get_auth_token()
            self.session.headers.update(
                {"Authorization": f"Bearer {self._token}"},
            )
        return self._token

    def get_assets(self, last_seen_asset_id=0, page_size=100):
        assert 1 <= page_size <= 300
        query_params = {
            "pageSize": page_size,
            "lastSeenAssetId": last_seen_asset_id,
        }
        self.ensure_token()
        url_path = self.url("rest/2.0/search/am/asset")
        self.session.headers.update({"content-type": "application/json"})
        response = self.session.post(url_path, params=query_params)

        return response.json()
