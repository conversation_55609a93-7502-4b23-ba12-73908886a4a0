from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v2_0 import QualysVmpcV20TemplateVersion


class QualysVmpcTemplate(Template):
    id = "qualys_vmpc"
    name = "Qualys VMDR"
    category = Template.Category.VULNERABILITY_MANAGEMENT
    versions = {
        QualysVmpcV20TemplateVersion.id: QualysVmpcV20TemplateVersion(),
    }
    vendor = Vendors.QUALYS
    vulnerability_coverage_available = True
