from requests.exceptions import ConnectionError

from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadHosts(IntegrationPermissionsHealthCheck):
    name = "Read hosts"
    description = "Read hosts from Qualys Cloud Platform"
    value = "assets:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("list_hosts", truncation_limit=1)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ReadDetections(IntegrationPermissionsHealthCheck):
    name = "Read detections"
    description = "Read detections from Qualys Cloud Platform"
    value = "detected_vulnerabilities:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("list_detections", truncation_limit=1)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ReadKnowledgeBase(IntegrationPermissionsHealthCheck):
    name = "Read knowledge base"
    description = "Read vulnerabilities from Qualys’ KnowledgeBase"
    value = "vendor_vulnerabilities:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            # 1 is an invalid id but this will return a 200 status code with no data
            self.integration.invoke("list_knowledge_base_vuln_stream", ids=1)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class EulaAccepted(IntegrationPermissionsHealthCheck):
    name = "EULA accepted"
    description = "EULA accepted by API User in Qualys Cloud Platform"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            res = self.integration.invoke("accept_eula")
            if res["@status"] == "SUCCESS":
                return IntegrationHealthCheckResult.PASSED
            elif "already accepted" in res["#text"]:
                return IntegrationHealthCheckResult.PASSED
        except (IntegrationError, KeyError):
            pass
        return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            if api.session_login() == "Logged in":
                return IntegrationHealthCheckResult.PASSED
            else:
                return IntegrationHealthCheckResult.FAILED
        except ConnectionError:
            return IntegrationHealthCheckResult.FAILED
