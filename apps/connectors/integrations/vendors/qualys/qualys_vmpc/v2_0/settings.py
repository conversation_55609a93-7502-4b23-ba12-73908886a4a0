from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class QualysVmpcV20HostSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Qualys VMPC Fetch Hosts Settings")

    fetch_by_tags: str = Field(
        title="Fetch by tags",
        description="Specify a comma-separated list of tags in Qualys Cloud Platform. If supplied, the connection for "
        "this adapter will only fetch devices tagged in Qualys with the tags provided in this list.",
        default_factory=str,
    )
    fetch_asset_groups: bool = Field(
        title="Fetch asset groups",
        description="Specify whether to fetch Asset Groups.",
        default=True,
    )


QualysVmpcV20Settings = create_settings_model(
    "QualysVmpcV20Settings",
    {
        IntegrationActionType.HOST_SYNC: QualysVmpcV20HostSyncSettings,
    },
)
