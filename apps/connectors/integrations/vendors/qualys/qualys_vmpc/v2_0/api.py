from urllib.parse import parse_qs, urlparse

import qualysapi
import xmltodict
from requests import HTTPError

CHUNK_SIZE = 50000000  # 50 MB

LIST_TAGS = (
    "HOST",
    "TAG",
    "ASSET_GROUP",
    "DETECTION",
    "SOFTWARE",
    "THREAT_INTEL",
    "COMPL<PERSON>NCE",
    "EXPLT",
    "MW_INFO",
    "AUTH_TYPE",
    "BUGTRAQ",
    "VENDOR_REFERENCE",
    "CVE",
)


def paginate(func, **kwargs):
    has_more_results = True
    while has_more_results:
        items, id_min = func(**kwargs)
        if id_min is not None:
            kwargs["id_min"] = id_min
            has_more_results = True
        else:
            has_more_results = False

        yield items


def parse_simple_response(simple_response):
    response = simple_response["SIMPLE_RETURN"]["RESPONSE"]

    if "CODE" in response:
        if response["CODE"] in ("1904", "1905"):
            # No items match selected filters or tags
            return [], None
        else:
            raise HTTPError(response["TEXT"])
    else:
        # no error code and http 200, so assume it was a success and return
        # the text
        return response["TEXT"]


def parse_streaming_response(func, item_name, *args, **kwargs):
    def read_until(iterator, target):
        result = ""
        # Assume target is returned before iterator gets exhausted
        current = next(iterator)
        while current.strip() != target:
            result += current
            current = next(iterator)
        return result + current

    response = func(*args, **kwargs)
    response.raise_for_status()
    lines = response.iter_lines(chunk_size=CHUNK_SIZE, decode_unicode=True)

    for line in lines:
        if line.strip() == "<SIMPLE_RETURN>":
            xml_response = line + read_until(lines, "</SIMPLE_RETURN>")
            parsed_response = xmltodict.parse(xml_response)
            return parse_simple_response(parsed_response)

        if line.strip() != f"<{item_name}>":
            continue
        item = line + read_until(lines, f"</{item_name}>")
        parsed_item = xmltodict.parse(item, force_list=LIST_TAGS)
        yield parsed_item[item_name]


def parse_response(func, item_name, *args, **kwargs):
    items = []
    id_min = None
    try:
        xml_response = func(*args, **kwargs)
    except HTTPError as e:
        xml_response = e.response.text
        # When no results match selected tags, the API returns a 400 error.
        # We don't want to raise an exception in this case.
        if e.response.status_code != 400 or "SIMPLE_RETURN" not in xml_response:
            raise

    parsed_response = xmltodict.parse(xml_response, force_list=LIST_TAGS)
    assert len(parsed_response.keys()) == 1, "Unexpected response format"

    if "SIMPLE_RETURN" in parsed_response:
        return parse_simple_response(parsed_response)

    # The API returns a list of results under a different key for each endpoint.
    key = list(parsed_response.keys())[0]

    try:
        items = parsed_response[key]["RESPONSE"][f"{item_name}_LIST"][item_name]
    except KeyError:
        items = []

    try:
        warning = parsed_response[key]["RESPONSE"]["WARNING"]
        if warning["CODE"] == "1980":
            url = warning["URL"]
            id_min = parse_qs(urlparse(url).query)["id_min"][0]
    except KeyError:
        id_min = None

    return items, id_min


class QualysVmpcV20Api:
    """
    Qualys API (VM/PC)
    https://www.qualys.com/documentation/#apis
    """

    def __init__(self, url=None, username=None, password=None):
        hostname = urlparse(url).hostname
        self.connect = qualysapi.connect(
            username=username,
            password=password,
            hostname=hostname,
            # The library uses a string for max_retries, but this causes an error. We could
            # convert the default to an int here, but we don't see a reason for any retries.
            # Setting max_retries to 0 is inline with the other connectors.
            max_retries=0,
        )

    def accept_eula(self):
        session = self.connect.session
        url = self.connect.url_api_version(api_version=1)
        xml_response = session.get(f"{url}acceptEULA.php", auth=self.connect.auth)
        xml_response.raise_for_status()
        # The API returns a 200 status code even when the EULA has already been accepted.
        # keys returned are '@status' and '#text'
        return xmltodict.parse(xml_response.text)["GENERIC_RETURN"]["RETURN"]

    def session_login(self, **kwargs):
        kwargs["action"] = "login"
        kwargs["username"], kwargs["password"] = self.connect.auth

        response = parse_response(
            self.connect.request, None, "/api/2.0/fo/session/", kwargs
        )
        return response

    def list_hosts(self, **kwargs):
        kwargs["action"] = "list"

        return parse_response(
            self.connect.request, "HOST", "/api/2.0/fo/asset/host/", kwargs
        )

    def list_groups(self, **kwargs):
        kwargs["action"] = "list"

        return parse_response(
            self.connect.request, "ASSET_GROUP", "/api/2.0/fo/asset/group/", kwargs
        )

    def list_detections(self, **kwargs):
        kwargs["action"] = "list"

        return parse_response(
            self.connect.request,
            "HOST",
            "/api/2.0/fo/asset/host/vm/detection/",
            kwargs,
        )

    def list_knowledge_base_vuln_stream(self, **kwargs):
        kwargs["action"] = "list"

        return parse_streaming_response(
            self.connect.request_streaming,
            "VULN",
            "/api/2.0/fo/knowledge_base/vuln/",
            kwargs,
        )
