# enum class with url choices
from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class QualysVmpcV20Url(StrEnum):
    US1 = "https://qualysapi.qualys.com"
    US2 = "https://qualysapi.qg2.apps.qualys.com"
    US3 = "https://qualysapi.qg3.apps.qualys.com"
    US4 = "https://qualysapi.qg4.apps.qualys.com"
    EU1 = "https://qualysapi.qualys.eu"
    EU2 = "https://qualysapi.qg2.apps.qualys.eu"
    EU3 = "https://qualysapi.qg3.apps.qualys.it"
    IN1 = "https://qualysapi.qg1.apps.qualys.in"
    CA1 = "https://qualysapi.qg1.apps.qualys.ca"
    AE1 = "https://qualysapi.qg1.apps.qualys.ae"
    UK1 = "https://qualysapi.qg1.apps.qualys.co.uk"
    AU1 = "https://qualysapi.qg1.apps.qualys.com.au"
    KSA1 = "https://qualysapi.qg1.apps.qualysksa.com"


class QualysVmpcV20Config(TemplateVersionConfig):
    url: QualysVmpcV20Url = Field(
        title="URL",
        description="API URL. See https://www.qualys.com/platform-identification/ for more information.",
        max_length=1024,
    )
    username: str = Field(
        title="Username",
        description="API Username",
        max_length=1024,
    )
    password: EncryptedStr = Field(
        title="Password",
        description="API Password",
        max_length=1024,
    )


class QualysVmpcV20Connection(ConnectionTemplate):
    id = "qualys_vmpc"
    name = "Qualys VMPC"
    config_model = QualysVmpcV20Config
