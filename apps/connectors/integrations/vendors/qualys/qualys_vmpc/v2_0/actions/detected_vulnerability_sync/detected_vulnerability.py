import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Generator

from django.utils import timezone
from django.utils.dateparse import parse_datetime

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.detected_vulnerability_sync import (
    DetectedVulnerability,
    DetectedVulnerabilityConfidenceLevel,
    DetectedVulnerabilityConfigurationState,
    DetectedVulnerabilityKernelState,
    DetectedVulnerabilityServiceState,
    DetectedVulnerabilityStatus,
    DetectedVulnerabilitySync,
    DetectedVulnerabilitySyncArgs,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.actions.host_sync import (
    normalize_host,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.api import paginate
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.health_check import (
    ReadDetections,
    ReadHosts,
)

logger = logging.getLogger(__name__)

# maps qualys status to active boolean
active_mapping = {
    "New": True,
    "Active": True,
    "Re-Opened": True,
    "Fixed": False,
}

# maps qualys status to DetectedVulnerabilityStatus
status_mapping = {
    "New": DetectedVulnerabilityStatus.OPEN,
    "Active": DetectedVulnerabilityStatus.OPEN,
    "Re-Opened": DetectedVulnerabilityStatus.REOPENED,
    "Fixed": DetectedVulnerabilityStatus.FIXED,
}

confidence_level_mapping = {
    "Informational": DetectedVulnerabilityConfidenceLevel.INFORMATIONAL,
    "Potential": DetectedVulnerabilityConfidenceLevel.POTENTIAL,
    "Confirmed": DetectedVulnerabilityConfidenceLevel.CONFIRMED,
}


def normalize_detected_vulnerabilities_for_host(
    detected_vulnerability: dict,
) -> DetectedVulnerability:
    source_id = detected_vulnerability.get("UNIQUE_VULN_ID")
    vendor_vulnerability_source_id = detected_vulnerability["QID"]

    first_seen_at = parse_datetime(
        detected_vulnerability.get("FIRST_FOUND_DATETIME", "")
    )
    last_seen_at = parse_datetime(detected_vulnerability.get("LAST_FOUND_DATETIME", ""))
    last_activated_at = parse_datetime(
        detected_vulnerability.get("LAST_REOPENED_DATETIME", "")
    )
    if not last_activated_at:
        last_activated_at = first_seen_at

    qualys_status = detected_vulnerability.get("STATUS")
    active = active_mapping[qualys_status]
    status = status_mapping[qualys_status]

    qualys_confidence_level = detected_vulnerability.get("TYPE")
    confidence_level = confidence_level_mapping[qualys_confidence_level]

    affected_kernel = detected_vulnerability.get("AFFECT_RUNNING_KERNEL")
    if affected_kernel:
        if affected_kernel == "0":
            kernel_state = DetectedVulnerabilityKernelState.NON_RUNNING_KERNEL
        elif affected_kernel == "1":
            kernel_state = DetectedVulnerabilityKernelState.RUNNING_KERNEL
    else:
        kernel_state = DetectedVulnerabilityKernelState.NON_KERNEL_RELATED

    affected_service = detected_vulnerability.get("AFFECT_RUNNING_SERVICE")
    if affected_service:
        if affected_service == "0":
            service_state = DetectedVulnerabilityServiceState.NON_RUNNING_SERVICE
        elif affected_service == "1":
            service_state = DetectedVulnerabilityServiceState.RUNNING_SERVICE
    else:
        service_state = DetectedVulnerabilityServiceState.NON_SERVICE_RELATED

    affected_config = detected_vulnerability.get("AFFECT_EXPLOITABLE_CONFIG")
    if affected_config:
        if affected_config == "0":
            configuration_state = (
                DetectedVulnerabilityConfigurationState.NON_EXPLOITABLE_CONFIGURATION
            )
        elif affected_config == "1":
            configuration_state = (
                DetectedVulnerabilityConfigurationState.EXPLOITABLE_CONFIGURATION
            )
    else:
        configuration_state = (
            DetectedVulnerabilityConfigurationState.NON_CONFIGURATION_RELATED
        )

    patch_superseded = detected_vulnerability.get("patch_superseded", False)

    asset = detected_vulnerability["host"]

    return DetectedVulnerability(
        source_id=source_id,
        technology_id="qualys_vmpc",
        asset=asset,
        vendor_vulnerability_source_id=vendor_vulnerability_source_id,
        first_seen_at=first_seen_at,
        last_seen_at=last_seen_at,
        last_activated_at=last_activated_at,
        active=active,
        status=status,
        confidence_level=confidence_level,
        kernel_state=kernel_state,
        service_state=service_state,
        configuration_state=configuration_state,
        patch_superseded=patch_superseded,
    )


class QualysVmpcV20DetectedVulnerabilitySync(DetectedVulnerabilitySync):
    @normalize(normalize_detected_vulnerabilities_for_host)
    def execute(
        self, args: DetectedVulnerabilitySyncArgs, **kwargs
    ) -> Generator[DetectedVulnerability, None, None]:
        # For an initial sync we want to get
        #   - any active detected vulns no matter how old (to handle the unusual case
        #     where a detection is open but hasn't had a change (e.g. a new detection
        #     in the last 30 days)
        #   - detected vulns that were fixed in the last 30 days
        #
        # For an incremental sync we want to get
        #   - any detected vulns that have had any update since the last sync (regardless
        #     of what that change was (status, new detection, etc.)
        #
        # In either case we get detected vulnerabilities that are confirmed or potential
        # but NOT informational.  The Qualys API doesn't allow you to specify you want
        # confirmed and potential but not informational so we have to make two calls to
        # get everything we want.  The other option would be to get everything and just
        # ignore the informational ones but that would be a lot of extra data to process
        # as it would often double the number of detected vulnerabilities we retrieve.

        # First, get all the hosts so that we have the criticality for each one.
        # Unfortunately, the Qualys API doesn't return criticality in the detection
        # and, we currently can't be sure that AV will have all the hosts that we
        # see detections for so we need to get criticality ourselves.
        asset_criticalities = self._get_asset_criticality_scores()

        # we are using default truncation_limit=1000
        params = {
            "show_asset_id": 1,
            "arf_kernel_filter": 0,  # do not filter but do include the AFFECT_RUNNING_KERNEL field
            "arf_service_filter": 0,  # do not filter but do include the AFFECT_RUNNING_SERVICE field
            "arf_config_filter": 0,  # do not filter but do include the AFFECT_EXPLOITABLE_CONFIG field
            "show_tags": 1,  # need tags for is_internet_facing
        }

        initial_sync = False
        if args.since:
            # get detections with any status updated since the given timestamp
            statuses = "New,Active,Re-Opened,Fixed"
            params["detection_updated_since"] = args.since.strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            )
        else:
            initial_sync = True
            # get any active detections no matter how old
            statuses = "New,Active,Re-Opened"

        params["status"] = statuses

        # get confirmed
        params["include_vuln_type"] = "confirmed"
        yield from self._get_detected_vulnerabilities(asset_criticalities, params)

        # get potential
        params["include_vuln_type"] = "potential"
        yield from self._get_detected_vulnerabilities(asset_criticalities, params)

        if initial_sync:
            # get fixed detections from the last 30 days

            params["status"] = "Fixed"
            since = timezone.now() - timedelta(days=30)
            params["detection_updated_since"] = since.strftime("%Y-%m-%dT%H:%M:%SZ")

            # get fixed/confirmed
            params["include_vuln_type"] = "confirmed"

            yield from self._get_detected_vulnerabilities(asset_criticalities, params)

            # get fixed/potential
            params["include_vuln_type"] = "potential"
            yield from self._get_detected_vulnerabilities(asset_criticalities, params)

    def _get_asset_criticality_scores(self) -> dict[str, str]:
        params = {
            "show_asset_id": 1,
            "details": "Basic",
            "show_tags": 0,
            "host_metadata": "all",
            "show_trurisk": 1,
        }

        api = self.integration.get_api()
        asset_criticalities = {}
        for page in paginate(api.list_hosts, **params):
            hosts = list(page)
            for host_data in hosts:
                asset_id = host_data["ID"]
                criticality_score = host_data.get("ASSET_CRITICALITY_SCORE")
                if criticality_score:
                    asset_criticalities[asset_id] = criticality_score

        return asset_criticalities

    def _list_host_detections(self, params) -> Generator[dict, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.list_detections, **params):
            for host_detected_vuln_data in page:
                yield host_detected_vuln_data

    def _get_detected_vulnerabilities(
        self, asset_criticalities: dict[str, str], params
    ) -> Generator[DetectedVulnerability, None, None]:
        self.integration.get_api()

        # First get all the detections but filtering out those that have QIDs with
        # superseded patches.  We will then get ALL the detections and mark which
        # detections have superseded patches (as this API does not provide the info,
        # it only allows you to filter it.
        detections_without_superseded_patches = set()

        params["filter_superseded_qids"] = 1
        for host_detected_vuln_data in self._list_host_detections(params):
            detection_list = host_detected_vuln_data.pop("DETECTION_LIST")
            if detection_list is None:
                detection_list = []
            else:
                detection_list = detection_list.get("DETECTION", [])

            for detected_vulnerability in detection_list:
                detections_without_superseded_patches.add(
                    detected_vulnerability.get("UNIQUE_VULN_ID")
                )

        params.pop("filter_superseded_qids")
        for host_detected_vuln_data in self._list_host_detections(params):
            detection_list = host_detected_vuln_data.pop("DETECTION_LIST")
            if detection_list is None:
                detection_list = []
            else:
                detection_list = detection_list.get("DETECTION", [])

            # add the critiality to the host data
            asset_critiality_score = asset_criticalities.get(
                host_detected_vuln_data.get("ID")
            )
            if asset_critiality_score:
                host_detected_vuln_data[
                    "ASSET_CRITICALITY_SCORE"
                ] = asset_critiality_score

            host = normalize_host(host_detected_vuln_data)

            # If we get a host with no hostname we are going to populate it with
            # the IP address.  This code is here only (and not in normalize host)
            # because we believe this indicates a "non-host" asset in Qualys and for
            # now those only come into VP.  This can be refactored if AV starts
            # taking in non-host assets
            if not host.hostname:
                if host.ip_addresses:
                    host.hostname = host.ip_addresses[0]

            # if we still don't have a hostname after all this we will let the
            # downstream consumer decide what to do with it but let's log it here
            # to help with debugging
            if not host.hostname:
                logger.warning(f"Asset with no hostname found: {host.source_id}")

            # exclude the source_data for now as vuln doesn't need it and it can be large
            host = host.model_dump(exclude="source_data")

            for detected_vulnerability in detection_list:
                # set asset data on each detected vulnerability, so we can use it in the normalizer
                detected_vulnerability["host"] = host

                detected_vulnerability["patch_superseded"] = (
                    detected_vulnerability.get("UNIQUE_VULN_ID")
                    not in detections_without_superseded_patches
                )

                yield detected_vulnerability

    def get_permission_checks(self):
        return [ReadDetections, ReadHosts]
