from requests.exceptions import ConnectionError

from apps.connectors.integrations import Integration

from .actions.detected_vulnerability_sync.detected_vulnerability import (
    QualysVmpcV20DetectedVulnerabilitySync,
)
from .actions.host_sync import (
    QualysVmpcV20HostSync,
    QualysVmpcV20VulnerabilityAssetSync,
)
from .actions.vendor_vulnerability_sync.vendor_vulnerability import (
    QualysVmpcV20VendorVulnerabilitySync,
)
from .api import QualysVmpcV20Api
from .health_check import ConnectionHealthCheck, EulaAccepted


class QualysVmpcV20Integration(Integration):
    api_class = QualysVmpcV20Api
    exception_types = (ConnectionError,)
    actions = (
        QualysVmpcV20HostSync,
        QualysVmpcV20VulnerabilityAssetSync,
        QualysVmpcV20DetectedVulnerabilitySync,
        QualysVmpcV20VendorVulnerabilitySync,
    )
    critical_health_checks = (ConnectionHealthCheck,)

    def get_eula_health_checks(self) -> list[EulaAccepted]:
        return [<PERSON><PERSON>Accepted(self)]
