from apps.connectors.integrations import TemplateVersion

from .connection import FreshworksServiceV1Config, FreshworksServiceV1Connection
from .integration import FreshworksServiceV1Integration
from .settings import FreshworksServiceV1Settings


class FreshworksServiceV1TemplateVersion(TemplateVersion):
    integration = FreshworksServiceV1Integration
    id = "v1"
    name = "v1"
    config_model = FreshworksServiceV1Config
    connection_model = FreshworksServiceV1Connection
    settings_model = FreshworksServiceV1Settings
