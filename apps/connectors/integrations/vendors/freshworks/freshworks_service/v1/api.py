from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["assets"]
    page = 1
    while True:
        page = page + 1
        response = bound_method(**kwargs, page=page)
        if ("assets" in response) and len(response["assets"]) > 0:
            yield response["assets"]
        else:
            break


class FreshworksServiceV1Api(ApiBase):
    def __init__(self, url=None, api_key=None, **kwargs):
        self.api_key = api_key
        static_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {api_key}",
        }
        super().__init__(base_url=url, static_headers=static_headers)

    def get_asset_types(self):
        url = self.url("/api/v2/asset_types")
        response = self.session.get(url)
        return response.json()

    def get_assets(self, per_page=30, page=1, asset_type_ids=None, **kwargs):
        assert 1 <= per_page <= 300

        # Filter query for asset types
        query = ""
        if asset_type_ids:
            query += " OR ".join([f"asset_type_id:{id}" for id in asset_type_ids])

        url = self.url("/api/v2/assets")
        data = {
            "include": "type_fields",
            "filter": query,
        }
        query_params = {
            "per_page": per_page,
            "page": page,
        }
        response = self.session.get(url, params=query_params, json=data)
        return response.json()
