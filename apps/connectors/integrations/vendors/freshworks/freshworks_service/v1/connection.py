from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class FreshworksServiceV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="Freshworks Service URL",
        description="The URL of the Freshworks Service instance.",
    )
    api_key: str = Field(
        title="API Key",
        description="The API key used to authenticate with the Freshworks Service instance.",
    )


class FreshworksServiceV1Connection(ConnectionTemplate):
    id = "freshworks_service"
    name = "Freshworks Service"
    config_model = FreshworksServiceV1Config
