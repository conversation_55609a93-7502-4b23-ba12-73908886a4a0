from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.actions.utils import normalize, normalize_last_seen
from apps.connectors.integrations.schemas.operating_system import (
    HostType,
    OsAttributes,
    OsFamily,
)
from apps.connectors.integrations.vendors.freshworks.freshworks_service.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.freshworks.freshworks_service.v1.health_check import (
    ReadAllHosts,
)

asset_types = [
    "Computer",
    "Desktop",
    "Laptop",
    "Server",
    "Unix Server",
    "Aix Server",
]


def normalize_host(host_data: dict):
    hostname = host_data.get("name")
    os_name = None
    host_type = HostType.from_os_name(os_name)
    os_family, __ = OsFamily.from_string(os_name)

    return Host(
        source_id=host_data["id"],
        group_names=[],
        hostname=hostname,
        fqdns=[],
        ip_addresses=[],
        mac_addresses=[],
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=[],
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("updated_at")),
        source_data=host_data,
    )


class FreshworksServiceV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        response = api.get_asset_types()

        response_asset_types = response["asset_types"]
        asset_type_ids = []

        for asset_type in response_asset_types:
            if asset_type["name"] in asset_types:
                asset_type_ids.append(asset_type["id"])

        for page in paginate(api.get_assets, asset_type_ids=asset_type_ids, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
