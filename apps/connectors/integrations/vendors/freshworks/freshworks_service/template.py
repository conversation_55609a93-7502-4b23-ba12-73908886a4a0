from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import FreshworksServiceV1TemplateVersion


class FreshworksServiceTemplate(Template):
    id = "freshworks_service"
    name = "Freshworks Freshservice"
    category = Template.Category.ASSET_SOURCE
    versions = {
        FreshworksServiceV1TemplateVersion.id: FreshworksServiceV1TemplateVersion(),
    }
    vendor = Vendors.FRESHWORKS
