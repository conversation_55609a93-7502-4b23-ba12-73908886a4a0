from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.absolute.absolute.v1.api import paginate
from apps.connectors.integrations.vendors.absolute.absolute.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    ips = []
    mac_addresses = []
    if "networkAdapters" in host_data:
        for network_adapter in host_data.get("networkAdapters"):
            ips.append(network_adapter["ipV4Address"])
            mac_addresses.append(network_adapter["macAddress"])

    os_name = host_data.get("operatingSystem").get("name")
    os_family, __ = OsFamily.from_string(os_name)
    os = OsAttributes(host_type=HostType.WORKSTATION, family=os_family, name=os_name)
    hostname = host_data.get("deviceName", "")
    groups = host_data.get("deviceGroupIds", [])
    owners = []
    if username := host_data.get("username"):
        owners.append(OwnerAttributes(name=username, email=None))

    return Host(
        source_id=host_data["deviceUid"],
        group_names=groups,
        hostname=hostname,
        _domain=host_data.get("domain"),
        ip_addresses=ips,
        mac_addresses=mac_addresses,
        os=os,
        owners=owners,
        last_seen=normalize_last_seen(host_data.get("lastConnectedDateTimeUtc")),
        source_data=host_data,
    )


class AbsoluteV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_devices, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
