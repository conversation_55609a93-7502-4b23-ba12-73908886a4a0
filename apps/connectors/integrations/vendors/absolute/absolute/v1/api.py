import json
import time

from authlib.jose import JsonWebSignature

from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["data"]
    items_count = len(response["data"])
    while items_count > 0:
        next_page = response["metadata"]["pagination"]["nextPage"]
        response = bound_method(next_page=next_page, **kwargs)
        items_count = len(response["data"])
        if items_count > 0:
            yield response["data"]


SECRET_KEY = "CHANGE_IT"


class AbsoluteV1Api(ApiBase):
    def __init__(self, url=None, token_id=None, token_secret=None):
        self.url = url
        self.token_id = token_id
        self.token_secret = token_secret
        static_headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
        super().__init__(base_url=self.url, static_headers=static_headers)

    def get_devices(self, page_size=500, next_page=None, **kwargs):
        # https://api.absolute.com/v3/reporting/devices
        request = {
            "method": "GET",
            "contentType": "application/json",
            "uri": "/v3/reporting/devices",
            "queryString": "pageSize=" + str(page_size),
            "payload": {},
        }
        query_params = {
            "pageSize": page_size,
        }
        if next_page:
            query_params["nextPage"] = next_page
            request["queryString"] = request["queryString"] + "&nextPage=" + next_page

        request_payload_data = {"data": request["payload"]}
        issued_at = round(time.time() * 1000)

        headers = {
            "alg": "HS256",
            "kid": self.token_id,
            "method": request["method"],
            "content-type": request["contentType"],
            "uri": request["uri"],
            "query-string": request["queryString"],
            "issuedAt": issued_at,
        }
        jws = JsonWebSignature()
        signed = jws.serialize_compact(
            headers, json.dumps(request_payload_data), self.token_secret
        )
        response = self.session.post(f"{self.url}", data=signed)
        return response.json()
