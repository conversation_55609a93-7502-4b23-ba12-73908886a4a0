from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class AbsoluteV1Urls(StrEnum):
    CA = "https://api.absolute.com/jws/validate"
    EU = "https://api.eu2.absolute.com/jws/validate"
    US = "https://api.us.absolute.com/jws/validate"


class AbsoluteV1Config(TemplateVersionConfig):
    url: AbsoluteV1Urls = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Absolute",
    )
    token_id: EncryptedStr = Field(
        title="Token ID",
        description="Token ID used to authenticate with Absol<PERSON>",
    )
    token_secret: EncryptedStr = Field(
        title="Token Secret",
        description="Token Secret used to authenticate with Absolute",
    )


class AbsoluteV1Connection(ConnectionTemplate):
    id = "absolute"
    name = "Absolute"
    config_model = AbsoluteV1Config
