from requests import HTTPError

from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadDevices(IntegrationPermissionsHealthCheck):
    name = "Read devices"
    description = "Read devices from Commvault"
    value = "devices:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke("get_entity_count")
            return (
                IntegrationHealthCheckResult.PASSED
                if response
                else IntegrationHealthCheckResult.FAILED
            )
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.get_entity_count()
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
