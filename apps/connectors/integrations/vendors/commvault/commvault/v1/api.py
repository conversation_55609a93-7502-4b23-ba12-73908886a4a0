import time

import requests
from ata_common.http import TimeoutSession

from apps.connectors.integrations import ApiBase


class CommvaultV1Api(ApiBase):
    def __init__(self, url=None, username=None, password=None, **kwargs):
        self.base_url = url
        self.username = username
        self.password = password
        self.token = None
        self.token_expiration_epoch = 0
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    # https://api.commvault.com/docs/SP34/api/cv/AuthenticationOperations/login
    @property
    def get_access_token(self) -> TimeoutSession:
        if self.token or self.token_expiration_epoch > int(time.time()):
            return self.session
        payload = {"username": self.username, "password": self.password}
        response = requests.post(
            self.url("commandcenter/api/Login"),
            headers=self.get_headers(),
            data=payload,
        )
        response = response.json()
        self.token = response["data"]["authToken"]
        self.token_expiration_epoch = int(time.time()) + 30 * 60 * 1000 - 1
        static_headers = {"Authorization": "Bearer " + self.token}
        self.session.headers.update(static_headers)
        return self.session

    def get_laptop_devices(self):
        response = self.get_access_token.get(self.url("commandcenter/api/Device"))
        return response.json()["clientsFileSystem"]

    def get_entity_count(self):
        response = self.get_access_token.get(
            self.url("/commandcenter/api/clients/count?type=laptop")
        )
        return response.status_code

    def get_headers(self):
        return {"Content-Type": "application/json", "Accept": "application/json"}
