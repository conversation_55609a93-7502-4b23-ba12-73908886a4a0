import logging

from apps.connectors.integrations import Integration

from .actions.host_sync import CommvaultV1HostSync
from .api import CommvaultV1Api
from .health_check import ConnectionHealthCheck

logger = logging.getLogger(__name__)


class CommvaultV1Integration(Integration):
    api_class = CommvaultV1Api
    actions = (CommvaultV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
