from datetime import datetime
from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.vendors.commvault.commvault.v1.health_check import (
    ReadDevices,
)


def normalize_last_seen(host_data: dict) -> datetime:
    return datetime.fromtimestamp(host_data.get("lastBackupTime").get("time"))


# https://api.commvault.com/docs/SP34/api/cv/LaptopDevices/get-laptop-devices
def normalize_host(host_data: dict):
    hostname = host_data.get("subClient").get("hostName")
    domain = host_data.get("subClient").get("clientName")
    sub_client_id = str(host_data.get("subClient").get("subclientId"))
    owners = []
    for owner in host_data.get("clientOwners").get("owners"):
        owners.append(OwnerAttributes(name=owner.get("userName"), email=None))
    return Host(
        source_id=sub_client_id,
        hostname=hostname,
        _domain=domain,
        owners=owners,
        last_seen=normalize_last_seen(host_data),
        source_data=host_data,
    )


class CommvaultV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        result = api.get_laptop_devices()
        yield from result

    def get_permission_checks(self):
        return [ReadDevices]
