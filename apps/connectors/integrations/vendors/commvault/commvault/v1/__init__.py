from apps.connectors.integrations import TemplateVersion

from .connection import CommvaultV1Config, CommvaultV1Connection
from .integration import CommvaultV1Integration
from .settings import CommvaultV1Settings


class CommvaultV1TemplateVersion(TemplateVersion):
    integration = CommvaultV1Integration
    id = "v1"
    name = "v1"
    config_model = CommvaultV1Config
    connection_model = CommvaultV1Connection
    settings_model = CommvaultV1Settings
