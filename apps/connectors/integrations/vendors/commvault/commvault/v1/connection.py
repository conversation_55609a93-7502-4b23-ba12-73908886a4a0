from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


# https://api.commvault.com/docs/SP34/api/cv/AuthenticationOperations/login
class CommvaultV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API URL",
        description="API URL for the Commvault server",
    )
    username: str = Field(
        title="Username",
        description="Username",
        max_length=1024,
    )
    password: EncryptedStr = Field(
        title="Password",
        description="Password",
        max_length=1024,
    )


class CommvaultV1Connection(ConnectionTemplate):
    id = "commvault"
    name = "Commvault"
    config_model = CommvaultV1Config
