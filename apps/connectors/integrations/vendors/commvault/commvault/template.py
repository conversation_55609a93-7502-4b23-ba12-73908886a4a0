from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CommvaultV1TemplateVersion


class CommvaultTemplate(Template):
    id = "commvault"
    name = "Commvault"
    category = Template.Category.DATA_BACKUP
    vendor = Vendors.COMMVAULT
    versions = {
        CommvaultV1TemplateVersion.id: CommvaultV1TemplateVersion(),
    }
