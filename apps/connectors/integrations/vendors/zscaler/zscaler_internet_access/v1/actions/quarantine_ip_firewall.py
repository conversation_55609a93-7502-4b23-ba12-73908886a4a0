from apps.connectors.integrations.actions.ip_address import (
    Message,
    QuarantineIpAddress,
    QuarantineIpAddressArgs,
    QuarantineIpAddressResult,
)
from apps.connectors.integrations.vendors.zscaler.zscaler_internet_access.v1.api import (
    ZscalerInternetAccessV1Api,
)
from apps.connectors.integrations.vendors.zscaler.zscaler_internet_access.v1.settings import (
    ZscalerInternetAccessV1FirewallSettings,
)


class ZscalerInternetAccessQuarantineIpAddress(QuarantineIpAddress):
    settings: ZscalerInternetAccessV1FirewallSettings

    def execute(self, args: QuarantineIpAddressArgs) -> QuarantineIpAddressResult:
        api: ZscalerInternetAccessV1Api = self.integration.get_api()
        rule = api.get_firewall_filtering_rules(self.settings.rule_id)
        rule["srcIps"].append(args.ip_address.value)
        api.update_firewall_filtering_rule(self.settings.rule_id, rule)
        return QuarantineIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was quarantined.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
