from apps.connectors.integrations.actions.ip_address import (
    Message,
    UnquarantineIpAddress,
    UnquarantineIpAddressArgs,
    UnquarantineIpAddressResult,
)
from apps.connectors.integrations.vendors.zscaler.zscaler_internet_access.v1.api import (
    ZscalerInternetAccessV1Api,
)
from apps.connectors.integrations.vendors.zscaler.zscaler_internet_access.v1.settings import (
    ZscalerInternetAccessV1FirewallSettings,
)


class ZscalerInternetAccessUnQurantineIpAddress(UnquarantineIpAddress):
    settings: ZscalerInternetAccessV1FirewallSettings

    def execute(self, args: UnquarantineIpAddressArgs) -> UnquarantineIpAddressResult:
        api: ZscalerInternetAccessV1Api = self.integration.get_api()
        rule = api.get_firewall_filtering_rules(self.settings.rule_id)
        rule["srcIps"].remove(args.ip_address.value)
        api.update_firewall_filtering_rule(self.settings.rule_id, rule)
        return UnquarantineIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was unquarantined.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
