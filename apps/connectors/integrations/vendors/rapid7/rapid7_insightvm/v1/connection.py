from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class RegionEnum(StrEnum):
    US = "United States"
    EU = "Europe"
    CA = "Canada"
    AU = "Australia"
    AP = "Japan"


class Rapid7InsightvmV1Config(TemplateVersionConfig):
    # "https://us.api.insight.rapid7.com"
    # https://help.rapid7.com/insightvm/en-us/api/integrations.html#tag/Asset
    region: RegionEnum = Field(
        title="Region",
        description="Region of the Rapid7 InsightVM server",
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key used to authenticate with Rapid7 InsightVM",
    )


class Rapid7InsightvmV1Connection(ConnectionTemplate):
    id = "rapid7_insightvm"
    name = "Rapid7 InsightVM"
    config_model = Rapid7InsightvmV1Config
