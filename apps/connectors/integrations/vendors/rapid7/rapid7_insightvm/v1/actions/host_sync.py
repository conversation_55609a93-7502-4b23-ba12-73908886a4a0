from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
    parse_fqdn,
)
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.actions.utils import normalize_mac_addresses
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.rapid7.rapid7_insightvm.v1.api import paginate
from apps.connectors.integrations.vendors.rapid7.rapid7_insightvm.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    os_name = host_data.get("os_name")
    host_type = HostType.from_os_name(os_name)
    os_family, _ = OsFamily.from_string(os_name)
    fqdns = []
    hostname, domain = parse_fqdn(host_data.get("host_name"))
    ip_addresses = [host_data.get("ip")]
    mac_addresses = [host_data.get("mac")]
    fqdns.append(host_data.get("host_name"))
    owners = []
    group_names = []
    return Host(
        source_id=host_data.get("id"),
        group_names=group_names,
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=normalize_mac_addresses(mac_addresses),
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=owners,
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen([host_data.get("last_scan_end")]),
        source_data=host_data,
    )


class Rapid7InsightvmV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_assets, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
