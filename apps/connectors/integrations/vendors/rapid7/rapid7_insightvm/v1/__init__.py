from apps.connectors.integrations import TemplateVersion

from .connection import Rapid7InsightvmV1Config, Rapid7InsightvmV1Connection
from .integration import Rapid7InsightvmV1Integration
from .settings import Rapid7InsightvmV1Settings


class Rapid7InsightvmV1TemplateVersion(TemplateVersion):
    integration = Rapid7InsightvmV1Integration
    id = "v1"
    name = "v1"
    config_model = Rapid7InsightvmV1Config
    connection_model = Rapid7InsightvmV1Connection
    settings_model = Rapid7InsightvmV1Settings
