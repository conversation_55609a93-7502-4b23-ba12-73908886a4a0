from apps.connectors.integrations.api import ApiBase

regions = {
    "United States": "us",
    "Europe": "eu",
    "Canada": "ca",
    "Australia": "au",
    "Japan": "ap",
}


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["data"]
    items_count = len(response["data"])
    page = 1
    while items_count > 0:
        page += 1
        response = bound_method(**kwargs, page=page)
        items_count = len(response["data"])
        if items_count > 0:
            yield response["data"]
        else:
            break


class Rapid7InsightvmV1Api(ApiBase):
    def __init__(self, base_url=None, api_key=None, region=None):
        self.base_url = "https://{}.api.insight.rapid7.com".format(regions[region])
        self.api_key = api_key
        self.region = region
        super().__init__(base_url=self.base_url)
        self.session.headers.update(
            {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-Api-Key": self.api_key,
            }
        )

    # https://help.rapid7.com/insightvm/en-us/api/integrations.html#tag/Asset/operation/searchIntegrationAssets
    def get_assets(self, page=1, size=1000):
        return self.session.post(
            self.url("/vm/v4/integration/assets"),
            params={"page": page, "size": size},
            json={"region": regions[self.region]},
        ).json()

    def get_health_check(self):
        return self.session.get(self.url("/vm/admin/health")).json()
