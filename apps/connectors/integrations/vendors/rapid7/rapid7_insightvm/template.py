from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import Rapid7InsightvmV1TemplateVersion


class Rapid7InsightvmTemplate(Template):
    id = "rapid7_insightvm"
    name = "Rapid7 InsightVM"
    category = Template.Category.VULNERABILITY_MANAGEMENT
    versions = {
        Rapid7InsightvmV1TemplateVersion.id: Rapid7InsightvmV1TemplateVersion(),
    }
    vendor = Vendors.RAPID7
    vulnerability_coverage_available = True
