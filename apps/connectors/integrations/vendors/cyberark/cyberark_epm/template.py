from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CyberarkEpmV1TemplateVersion


class CyberarkEpmTemplate(Template):
    id = "cyberark_epm"
    name = "CyberArk Endpoint Privilege Manager"
    category = Template.Category.ASSET_SOURCE
    vendor = Vendors.CYBERARK
    versions = {
        CyberarkEpmV1TemplateVersion.id: CyberarkEpmV1TemplateVersion(),
    }
