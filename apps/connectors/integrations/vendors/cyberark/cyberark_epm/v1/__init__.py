from apps.connectors.integrations import TemplateVersion

from .connection import CyberarkEpmV1Config, CyberarkEpmV1Connection
from .integration import CyberarkEpmV1Integration
from .settings import CyberarkEpmV1Settings


class CyberarkEpmV1TemplateVersion(TemplateVersion):
    integration = CyberarkEpmV1Integration
    id = "v1"
    name = "v1"
    config_model = CyberarkEpmV1Config
    connection_model = CyberarkEpmV1Connection
    settings_model = CyberarkEpmV1Settings
