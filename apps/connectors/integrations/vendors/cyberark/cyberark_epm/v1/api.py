from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, item_key="Sets", set_id=None, **kwargs):
    if set_id:
        response = bound_method(set_id=set_id, **kwargs)
    else:
        response = bound_method(**kwargs)
    yield response
    offset = len(response[item_key])
    while True:
        if set_id:
            response = bound_method(set_id=set_id, offset=offset, **kwargs)
        else:
            response = bound_method(**kwargs, offset=offset)
        items_count = len(response[item_key])
        offset = offset + items_count
        if items_count > 0:
            yield response
        else:
            break


class CyberarkEpmV1Api(ApiBase):
    def __init__(
        self,
        epm_dispatcher_server=None,
        email=None,
        password=None,
        application_id="Critical Start",
    ):
        self.epm_dispatcher_server = epm_dispatcher_server
        self.email = email
        self.password = password
        self.application_id = application_id
        self.base_url = self.epm_dispatcher_server
        self.token = None
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.auth_url = f"{self.epm_dispatcher_server}"
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    def get_session(self):
        data = {
            "username": self.email,
            "password": self.password,
            "ApplicationID": self.application_id,
        }
        self.base_url = self.epm_dispatcher_server
        response = self.session.post(
            self.url(f"/EPM/API/Auth/EPM/Logon"),
            headers=self.headers,
            data=data,
        )
        self.token = response.json()["EPMAuthenticationResult"]
        self.base_url = response.json()["ManagerURL"]
        self.headers["Authorization"] = "Bearer " + self.token
        self.session.headers.update(self.headers)

        return self.session

    # {
    #         "SetsCount": number,
    #         "Sets": [
    #         {
    #             "Id": "string",
    #             "Name": "string",
    #             "Description": "string",
    #             "IsNPVDI": boolean
    #         }]
    # }
    def get_sets_lists(self, limit=1000, offset=0):
        response = self.get_session().get(
            self.url(f"/EPM/API/Sets"),
            params={"Limit": limit, "Offset": offset},
        )
        return response.json()

    # {
    # "Computers": [
    #     {
    #         "AgentId": "string",
    #         "AgentVersion": "string",
    #         "ComputerName": "string",
    #         "ComputerType": "string",
    #         "Platform": "string",
    #         "InstallTime": "date/time",
    #         "Status": "string"
    #         "LastSeen": "date/time"
    #         "LoggedIn": "string"
    #     }
    # ],
    # "TotalCount": number
    # }
    # https://docs.cyberark.com/epm/latest/en/Content/WebServices/GetComputers.htm#Resourceinformation
    def get_computers(self, set_id=None, limit=5000, offset=0):
        response = self.get_session().get(
            self.url(f"/EPM/API/" + set_id + "/Computers"),
            params={"limit": limit, "offset": offset},
        )
        return response.json()
