from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.cyberark.cyberark_epm.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.cyberark.cyberark_epm.v1.health_check import (
    ReadDeviceInventory,
)

computer_type_to_host_type = {
    "Desktop": HostType.WORKSTATION,
    "Laptop": HostType.WORKSTATION,
    "Server": HostType.SERVER,
}

platform_to_os_family = {
    "Windows": OsFamily.WINDOWS,
    "MacOS": OsFamily.MAC,
}


def normalize_host(host_data: dict):
    host_type = computer_type_to_host_type.get(
        host_data.get("ComputerType"), HostType.UNKNOWN
    )
    os_family = platform_to_os_family.get(host_data.get("Platform"), OsFamily.UNKNOWN)
    fqdns = host_data.get("ComputerName", {}) or ""

    return Host(
        source_id=host_data["AgentId"],
        fqdns=fqdns,
        os=OsAttributes(host_type=host_type, family=os_family, name=None),
        last_seen=normalize_last_seen(host_data.get("LastSeen")),
        source_data=host_data,
    )


class CyberarkEpmV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_sets_lists, item_key="Sets", **kwargs):
            set_ids = page["Sets"]
            for set_id in set_ids:
                bound_method = api.get_computers
                for computers_page in paginate(
                    bound_method, item_key="Computers", set_id=set_id["Id"], **kwargs
                ):
                    yield from computers_page["Computers"]

    def get_permission_checks(self):
        return [ReadDeviceInventory]
