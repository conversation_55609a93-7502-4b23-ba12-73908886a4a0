from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


# enum class with url choices
class CyberarkEpmV1Url(StrEnum):
    USA = "https://login.epm.cyberark.com/login"
    Germany = "https://eu.epm.cyberark.com/login"
    Switzerland = "https://ch.epm.cyberark.com/login"
    UK = "https://uk.epm.cyberark.com/login"
    Australia = "https://au.epm.cyberark.com/login"
    Canada = "https://ca.epm.cyberark.com/login"
    India = "https://in.epm.cyberark.com/login"
    Japan = "https://jp.epm.cyberark.com/login"
    Singapore = "https://sg.epm.cyberark.com/login"
    Italy = "https://it.epm.cyberark.com/login"
    Federal = "https://login.epm.cyberarkgov.cloud/login"


class CyberarkEpmV1Config(TemplateVersionConfig):
    # https://docs.cyberark.com/epm/latest/en/Content/WebServices/ServerAuthentication.htm
    epm_dispatcher_server: CyberarkEpmV1Url = Field(
        title="EPM Dispatcher Server",
        description="The URL of the CyberArk EPM dispatcher server. "
        "See https://docs.cyberark.com/epm/latest/en/Content/WebServices/WebServicesIntro.htm#dispatc for more information.",
    )
    email: str = Field(
        title="Email",
        description="The email to authenticate with the CyberArk EPM server",
    )
    password: EncryptedStr = Field(
        title="Password",
        description="The password to authenticate with the CyberArk EPM server",
    )


class CyberarkEpmV1Connection(ConnectionTemplate):
    id = "cyberark_epm"
    name = "CyberArk EPM"
    config_model = CyberarkEpmV1Config
