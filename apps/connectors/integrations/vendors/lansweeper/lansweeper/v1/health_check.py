from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.get_sites()
            return IntegrationHealthCheckResult.PASSED
        except:
            return IntegrationHealthCheckResult.FAILED


class ReadDeviceInventory(IntegrationPermissionsHealthCheck):
    name = "Read devices inventory"
    description = "Read devices inventory from Lansweeper."
    value = "devices_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_sites")
            return IntegrationHealthCheckResult.PASSED
        except:
            return IntegrationHealthCheckResult.FAILED
