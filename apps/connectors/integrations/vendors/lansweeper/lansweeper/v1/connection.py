from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class LansweeperV1Config(TemplateVersionConfig):
    # https://docs.lansweeper.com/docs/api/authenticate#personal-application
    token: str = Field(
        title="Token",
        description="The application token provided by Lansweeper. "
        "Application creation instructions can be found here: https://docs.lansweeper.com/docs/api/authenticate#personal-application.",
    )


class LansweeperV1Connection(ConnectionTemplate):
    id = "lansweeper"
    name = "Lansweeper"
    config_model = LansweeperV1Config
