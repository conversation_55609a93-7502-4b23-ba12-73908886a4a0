from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
    parse_fqdn,
)
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.actions.utils import normalize_mac_addresses
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.lansweeper.lansweeper.v1.api import paginate
from apps.connectors.integrations.vendors.lansweeper.lansweeper.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    asset_info = host_data.get("assetBasicInfo", {})
    os_name = asset_info.get("description")
    host_type = HostType.from_os_name(os_name)
    os_family, _ = OsFamily.from_string(os_name)
    fqdns = [asset_info.get("fqdn")]
    hostname, domain = parse_fqdn(fqdns[0])
    ip_addresses = [asset_info.get("ipAddress")]
    mac_addresses = [asset_info.get("mac")]
    owners = [OwnerAttributes(name=asset_info.get("userName"), email=None)]
    group_names = [asset_info.get("typeGroup")]

    return Host(
        source_id=asset_info.get("assetUnique"),
        group_names=group_names,
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=normalize_mac_addresses(mac_addresses),
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=owners,
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(asset_info.get("lastSeen")),
        source_data=host_data,
    )


class LansweeperV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        sites_data = api.get_sites()
        for site in sites_data["authorizedSites"]["sites"]:
            for page in paginate(api.get_resources, site_id=site["id"], **kwargs):
                yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
