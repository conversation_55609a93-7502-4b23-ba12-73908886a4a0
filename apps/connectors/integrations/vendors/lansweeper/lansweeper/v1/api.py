from gql import Client, gql
from gql.transport.requests import RequestsHTTPTransport

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    items = response["site"]["assetResources"]["items"]
    yield items
    total_count = response["site"]["assetResources"]["total"]
    total_count -= len(items)
    items_count = len(items)
    page = "NEXT"
    while items_count > 0 and total_count > 0:
        response = bound_method(**kwargs, page=page)
        items = response["site"]["assetResources"]["items"]
        items_count = len(items)
        total_count -= len(items)
        if items_count > 0:
            yield items
        else:
            break


class LansweeperV1Api(ApiBase):
    def __init__(self, token=None):
        self.access_token = token
        self.graphql_url = "https://api.lansweeper.com/api/v2/graphql"
        super().__init__(base_url=self.graphql_url)
        self.session.headers.update(
            {"Accept": "application/json", "Content-Type": "application/json"}
        )

    def get_gql_client(self):
        transport = RequestsHTTPTransport(
            url=self.graphql_url,
            headers={"Authorization": f"Bearer {self.access_token}"},
            use_json=True,
        )
        return Client(transport=transport, fetch_schema_from_transport=False)

    def get_sites(self):
        # https://docs.lansweeper.com/docs/api/getting-data
        client = self.get_gql_client()
        query = gql(
            """
            {
                authorizedSites {
                    sites {
                        id
                        name
                    }
                }
            }
            """
        )
        return client.execute(query)

    def get_resources(self, site_id=None, page="FIRST", limit=100):
        # https://docs.lansweeper.com/docs/api/getting-data
        client = self.get_gql_client()
        query = gql(
            """
            query getAssetResources {
                site(id: "$site_id") {
                    assetResources(
                    assetPagination: { limit: $limit, page: $PAGE }
                    fields: [
                        "assetBasicInfo.name"
                        "assetBasicInfo.domain"
                        "assetBasicInfo.userName"
                        "assetBasicInfo.fqdn"
                        "assetBasicInfo.typeGroup"
                        "assetBasicInfo.ipAddress"
                        "assetBasicInfo.lastSeen"
                        "assetBasicInfo.mac"
                        "assetBasicInfo.description"
                        "assetBasicInfo.typeGroup"
                    ]
                    ) {
                    total
                    pagination {
                        limit
                        current
                        next
                        page
                    }
                    items
                    }
                }
            }
            """
        )
        return client.execute(
            query, variable_values={"site_id": site_id, "limit": limit, "page": page}
        )
