from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import LansweeperV1TemplateVersion


class LansweeperTemplate(Template):
    id = "lansweeper"
    name = "Lansweeper"
    category = Template.Category.ASSET_SOURCE
    versions = {
        LansweeperV1TemplateVersion.id: LansweeperV1TemplateVersion(),
    }
    vendor = Vendors.LANSWEEPER
