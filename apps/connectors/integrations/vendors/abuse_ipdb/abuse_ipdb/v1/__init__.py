from apps.connectors.integrations import TemplateVersion

from .connection import AbuseIpdbV1Config, AbuseIpdbV1Connection
from .integration import AbuseIpdbV1Integration
from .settings import AbuseIpdbV1Settings


class AbuseIpdbV1TemplateVersion(TemplateVersion):
    integration = AbuseIpdbV1Integration
    id = "v1"
    name = "v1"
    config_model = AbuseIpdbV1Config
    connection_model = AbuseIpdbV1Connection
    settings_model = AbuseIpdbV1Settings
