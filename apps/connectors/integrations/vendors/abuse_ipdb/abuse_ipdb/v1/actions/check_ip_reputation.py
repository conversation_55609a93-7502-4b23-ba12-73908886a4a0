from apps.connectors.integrations.actions.check_ip_reputation import (
    CheckIpReputation,
    CheckIpReputationArgs,
    CheckIpReputationResult,
)
from apps.connectors.integrations.schemas.ocsf import (
    OSINT,
    AutonomousSystem,
    Confidence,
    DNSAnswer,
    DNSAnswerFlag,
    GeoLocation,
    OSINTIndicatorType,
    OSINTVendor,
    WhoisInfo,
)


def normalize_osint(input: dict) -> OSINT:
    data = input.get("data", {})
    autonomous_system = AutonomousSystem(
        name=data.get("isp"),
        # Abuse IPDB doesn't provide an ASN
        number=None,
    )

    def normalize_confidence_score(abuse_confidence_score):
        # From AbuseIPDB API documentation:
        # """
        # abuseConfidenceScore is our calculated evaluation on how abusive the IP is based on the users
        # that reported it. We place a hard minimum of 25% on the abuseConfidenceScore.
        # """
        if abuse_confidence_score is None or abuse_confidence_score == 0:
            return Confidence.UNKNOWN
        elif abuse_confidence_score < 50:
            return Confidence.LOW
        elif abuse_confidence_score < 75:
            return Confidence.MEDIUM
        elif abuse_confidence_score <= 100:
            return Confidence.HIGH
        else:
            return Confidence.UNKNOWN  # pragma: no cover

    confidence = normalize_confidence_score(data.get("abuseConfidenceScore"))

    hostnames = data.get("hostnames")
    dns_answers = None
    if hostnames:
        dns_answers = [
            DNSAnswer(
                class_="IN",
                flags=[DNSAnswerFlag.AUTHENTIC_DATA],
                rdata=hostname,
                type="PTR",
            )
            for hostname in hostnames
        ]

    location = None
    country_code = data.get("countryCode")
    isp = data.get("isp")
    if country_code and isp:
        location = GeoLocation(
            country=country_code,
            isp=isp,
        )

    return OSINT(
        answers=dns_answers,
        confidence=confidence,
        autonomous_system=autonomous_system,
        value=data.get("ipAddress"),
        type=OSINTIndicatorType.IP_ADDRESS,
        location=location,
        vendor_name=OSINTVendor.ABUSE_IP_DB,
        whois=WhoisInfo(
            autonomous_system=autonomous_system,
            domain=data.get("domain"),
        ),
        src_url=f"https://www.abuseipdb.com/check/{data.get('ipAddress')}",
        # FIXME: where do we map hostnames[]
        # FIXME: Where do we map isWhitelisted and isPublic
    )


class AbuseIpdbCheckIpReputation(CheckIpReputation):
    def execute(self, args: CheckIpReputationArgs) -> CheckIpReputationResult:
        """Given an IPV4 or IPv6 address, return the OSINT data for that address"""
        api = self.integration.get_api()
        response = api.check_ip(ip_address=args.ip_address.value)
        osint = normalize_osint(response)
        return CheckIpReputationResult(result=osint)

    def get_permission_checks(self):
        return []
