import urllib.parse

from apps.connectors.integrations import ApiBase


class AbuseIpdbV1Api(ApiBase):
    def __init__(self, token=None):
        self.url = "https://api.abuseipdb.com/api/v2"
        self.token = token
        static_headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Key": self.token,
        }
        super().__init__(base_url=self.url, static_headers=static_headers)

        # FIXME: should this be configurable? Maybe config or action args?
        self.max_age_in_days = 90

    def check_ip(self, ip_address, max_age_in_days=None, verbose=None):
        url = f"{self.base_url}/check"
        max_age_in_days = max_age_in_days or self.max_age_in_days
        params = {
            "ipAddress": ip_address,
            "maxAgeInDays": max_age_in_days,
        }
        if verbose is not None:
            # `verbose` is a GET parameter without a value
            params["verbose"] = ""

        # urlencode the IP, since it can be IPv6
        response = self.session.get(url=url, params=urllib.parse.urlencode(params))
        return response.json()
