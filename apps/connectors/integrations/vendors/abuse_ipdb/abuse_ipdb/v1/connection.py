from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class AbuseIpdbV1Config(TemplateVersionConfig):
    token: EncryptedStr = Field(
        title="Key",
        description="API Key used to authenticate with AbuseIPDB",
    )


class AbuseIpdbV1Connection(ConnectionTemplate):
    id = "abuse_ipdb"
    name = "AbuseIPDB"
    config_model = AbuseIpdbV1Config
