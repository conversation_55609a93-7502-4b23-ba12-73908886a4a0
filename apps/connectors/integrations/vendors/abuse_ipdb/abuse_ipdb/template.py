from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import AbuseIpdbV1TemplateVersion


class AbuseIpdbTemplate(Template):
    id = "abuse_ipdb"
    name = "AbuseIPDB OSINT Provider"
    category = Template.Category.OSINT_SOURCE
    versions = {
        AbuseIpdbV1TemplateVersion.id: AbuseIpdbV1TemplateVersion(),
    }
    vendor = Vendors.ABUSE_IPDB
