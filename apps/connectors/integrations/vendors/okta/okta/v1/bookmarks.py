from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_latest_event_published_datetime():
    return (
        (datetime.now(timezone.utc) - timedelta(days=1))
        .isoformat()
        .replace("+00:00", "Z")
    )


class OktaV1EventSyncBookmark(TemplateVersionActionBookmark):
    latest_event_published_datetime: str = Field(
        title="Latest Event Published Datetime",
        description="The latest published datetime received during a fetch.",
        default_factory=default_latest_event_published_datetime,
    )


OktaV1Bookmarks = create_bookmarks_model(
    "OktaV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: OktaV1EventSyncBookmark,
    },
)
