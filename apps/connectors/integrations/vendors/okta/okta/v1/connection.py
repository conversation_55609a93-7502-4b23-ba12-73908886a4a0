from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class OktaV1Config(TemplateVersionConfig):
    # https://developer.okta.com/docs/api/openapi/okta-management/guides/overview/
    org_url: HttpUrl = Field(
        title="Okta Org URL",
        description="The base URL for the Okta API. Example: https://example.okta.com",
    )
    api_token: EncryptedStr = Field(
        title="API Token",
        description="API Token for the Okta API",
    )


class OktaV1Connection(ConnectionTemplate):
    id = "okta"
    name = "Okta"
    config_model = OktaV1Config
