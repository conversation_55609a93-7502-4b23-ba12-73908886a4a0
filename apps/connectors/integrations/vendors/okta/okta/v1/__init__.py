from apps.connectors.integrations import IntegrationActionType, TemplateVersion

from .bookmarks import OktaV1Bookmarks
from .connection import OktaV1Config, OktaV1Connection
from .integration import OktaV1Integration
from .settings import OktaV1Settings


class OktaV1TemplateVersion(TemplateVersion):
    integration = OktaV1Integration
    id = "v1"
    name = "v1"
    config_model = OktaV1Config
    connection_model = OktaV1Connection
    settings_model = OktaV1Settings
    bookmarks_model = OktaV1Bookmarks
    supported_actions = [IntegrationActionType.EVENT_SYNC]
