from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    SimplePermissionsCheck,
)


class OktaPermissionsCheck(SimplePermissionsCheck):
    def _has_role(self, role: str) -> bool:
        user = self.integration.get_api().get_my_user()
        user_id = user["id"]
        roles = self.integration.get_api().get_user_roles(user_id)
        for role_data in roles:
            if role_data["label"] == role:
                return True
        return False

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            if any(self._has_role(role) for role in self.allowed_values):
                return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            pass

        return IntegrationHealthCheckResult.FAILED


class OktaUsersManage(OktaPermissionsCheck):
    name = "Okta Users Manage"
    description = "Allows the app to manage users in Okta"
    value = "Okta Users Manage"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = ["Super Administrator", "Group Administrator"]


class OktaUsersRead(OktaPermissionsCheck):
    name = "Okta Users Read"
    description = "Allows the app to read users in Okta"
    value = "Okta Users Read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Super Administrator",
        "Group Administrator",
        "Read-Only Administrator",
    ]


class OktaLogsRead(OktaPermissionsCheck):
    name = "Okta Logs Read"
    description = "Allows the app to read logs in Okta"
    value = "Okta Logs Read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Super Administrator",
        "Group Administrator",
        "Read-Only Administrator",
    ]


class OktaOrgsRead(OktaPermissionsCheck):
    name = "Okta Orgs Read"
    description = "Allows the app to read org settings in Okta"
    value = "Okta Orgs Read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Super Administrator",
        "Group Administrator",
        "Read-Only Administrator",
    ]


class OktaSystemLogsRead(IntegrationHealthCheck):
    name = "Read System Logs"
    description = "Read system log events from Okta"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            api = self.integration.get_api()
            # Test with a minimal query to verify access
            params = {"limit": 1, "sortOrder": "DESCENDING"}
            api.get_system_log_events(params=params)
            return IntegrationHealthCheckResult.PASSED
        except HTTPError as e:
            if e.response.status_code == 403:
                return IntegrationHealthCheckResult.FAILED
            # For other HTTP errors, we'll consider it a temporary issue
            return IntegrationHealthCheckResult.FAILED
        except Exception:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            api = self.integration.get_api()
            api.get_org_settings()
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
