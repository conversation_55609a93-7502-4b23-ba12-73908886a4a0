from apps.connectors.integrations.actions.user import (
    EnableUser<PERSON>og<PERSON>,
    UserLoginResult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaUsersManage,
)


class OktaV1EnableUserLogin(EnableUserLogin):
    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        api.enable_user_login(args.user_id.value)

        return UserLoginResult(result=UserLoginStatus(enabled=True))

    def get_permission_checks(self, *args, **kwargs):
        return [OktaUsersManage]
