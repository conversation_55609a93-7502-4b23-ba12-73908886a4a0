from urllib.parse import urlparse, urlunparse

from apps.connectors.integrations.actions.external_user_profile_link import (
    ExternalUserProfileLink,
    ExternalUserProfileLinkResult,
)
from apps.connectors.integrations.schemas.identifiers import UserIdentifierArgs


class OktaV1GetExternalUserProfileLink(ExternalUserProfileLink):
    def execute(self, args: UserIdentifierArgs) -> ExternalUserProfileLinkResult:
        # Extract the base URL and user ID from the input arguments
        base_url = self.integration.get_api().base_url
        user_id = args.user_id.value

        # Parse the base URL to modify the hostname
        parsed_url = urlparse(base_url)
        hostname_parts = parsed_url.netloc.split(".")
        hostname_parts[0] += "-admin"  # Add '-admin' to the subdomain
        modified_netloc = ".".join(hostname_parts)

        # Reconstruct the base URL with the modified hostname
        modified_base_url = urlunparse(parsed_url._replace(netloc=modified_netloc))

        # Construct the external user profile link
        external_user_profile_link = (
            f"{modified_base_url}/admin/user/profile/view/{user_id}"
        )

        # Return the external user profile link
        return ExternalUserProfileLinkResult(template=external_user_profile_link)

    def get_permission_checks(self, *args, **kwargs):
        return []
