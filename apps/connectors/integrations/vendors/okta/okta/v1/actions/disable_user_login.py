from apps.connectors.integrations.actions.user import (
    Disable<PERSON><PERSON><PERSON><PERSON><PERSON>,
    UserLoginR<PERSON>ult,
    UserLoginStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaUsersManage,
)


class OktaV1DisableUserLogin(DisableUserLogin):
    def execute(self, args: UserIdentifierArgs) -> UserLoginResult:
        api = self.integration.get_api()
        api.disable_user_login(args.user_id.value)

        return UserLoginResult(result=UserLoginStatus(enabled=False))

    def get_permission_checks(self, *args, **kwargs):
        return [OktaUsersManage]
