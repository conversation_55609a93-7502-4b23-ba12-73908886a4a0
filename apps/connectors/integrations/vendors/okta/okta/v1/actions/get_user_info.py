from apps.connectors.integrations.actions.user import GetUserInfo, UserInfoResult
from apps.connectors.integrations.schemas import UserIdentifierArgs, ocsf
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import OktaUsersRead


class OktaV1GetUserInfo(GetUserInfo):
    def execute(self, args: UserIdentifierArgs) -> UserInfoResult:
        api = self.integration.get_api()

        user = api.get_user(user_id=args.user_id.value)

        return UserInfoResult(
            result=ocsf.User(
                account=ocsf.Account(type="Okta Account"),
                display_name=user["profile"]["displayName"],
                is_enabled=user["status"] == "ACTIVE",
                uid=user["id"],
            )
        )

    def get_permission_checks(self, *args, **kwargs):
        return [OktaUsersRead]
