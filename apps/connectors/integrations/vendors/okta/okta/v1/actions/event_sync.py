import logging
from datetime import datetime
from typing import Generator, Optional

import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.okta.okta.v1.api import OktaV1Api
from apps.connectors.integrations.vendors.okta.okta.v1.bookmarks import (
    OktaV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaSystemLogsRead,
)

logger = logging.getLogger(__name__)


def convert_to_ocsf(event: dict) -> ocsf.Authentication:
    """
    Normalize an Okta System Log event to OCSF Authentication Activity format.

    Handles all Okta Identity Threat Protection (ITP) event types and maps them
    according to the specified field mapping requirements.

    Args:
        event: Raw Okta System Log event

    Returns:
        Authentication: OCSF normalized event
    """

    event_type = event.get("eventType", "")
    published = event.get("published", "")
    display_message = event.get("displayMessage", "")
    outcome = event.get("outcome", {})
    debug_context = event.get("debugContext", {}).get("debugData", {})

    message = display_message
    if outcome.get("reason"):
        message = outcome["reason"]

    severity_mapping = {
        "DEBUG": ocsf.Severity.INFORMATIONAL,
        "INFO": ocsf.Severity.INFORMATIONAL,
        "WARN": ocsf.Severity.MEDIUM,
        "ERROR": ocsf.Severity.HIGH,
    }
    severity = severity_mapping.get(
        event.get("severity", "INFO"), ocsf.Severity.INFORMATIONAL
    )

    status_mapping = {
        "SUCCESS": ocsf.EventStatus.SUCCESS,
        "FAILURE": ocsf.EventStatus.FAILURE,
        "SKIPPED": ocsf.EventStatus.OTHER,
        "ALLOW": ocsf.EventStatus.SUCCESS,
        "DENY": ocsf.EventStatus.FAILURE,
        "CHALLENGE": ocsf.EventStatus.OTHER,
        "DEFERRED": ocsf.EventStatus.OTHER,
        "SCHEDULED": ocsf.EventStatus.OTHER,
        "ABANDONED": ocsf.EventStatus.OTHER,
        "UNANSWERED": ocsf.EventStatus.OTHER,
    }
    status_id = status_mapping.get(outcome.get("result", ""), ocsf.EventStatus.UNKNOWN)

    correlation_uid = event.get("uuid", "")

    metadata = ocsf.Metadata(
        correlation_uid=correlation_uid,
        event_code=event_type,
        uid=event.get("uuid"),
        product=ocsf.Product(
            name="Okta System Log",
            vendor_name="Okta",
            version=event.get("version"),
        ),
        profiles=[
            ocsf.Profile.DATETIME,
            ocsf.Profile.HOST,
        ],
    )

    actor = _extract_actor(event.get("actor", {}))

    user = _extract_target_user(event.get("target", []))

    src_endpoint = _extract_src_endpoint(event.get("client", {}))

    dst_endpoint = ocsf.NetworkEndpoint(
        svc_name=event.get("debugContext", {}).get("debugData", {}).get("url"),
    )

    session = _extract_session(event.get("authenticationContext", {}), debug_context)

    http_request = ocsf.HttpRequest(
        user_agent=event.get("client", {}).get("userAgent", {}).get("rawUserAgent"),
        uid=event.get("debugContext", {}).get("debugData", {}).get("requestId"),
    )

    device = _extract_device(event.get("device", {}))

    return ocsf.Authentication(
        activity=_map_event_type_to_activity(event_type),
        status=status_id,
        message=message,
        metadata=metadata,
        severity=severity,
        time_dt=published,
        actor=actor,
        user=user,
        src_endpoint=src_endpoint,
        dst_endpoint=dst_endpoint,
        session=session,
        http_request=http_request,
        device=device,
    )


def normalize_event(event: dict) -> Event:
    """
    Normalize an Okta System Log event to the Event structure.

    Args:
        event: Raw Okta System Log event

    Returns:
        Event: Normalized event with OCSF data
    """

    ocsf = convert_to_ocsf(event)

    event_timestamp = datetime.fromisoformat(
        event.get("published", "").replace("Z", "+00:00")
    )

    result = Event(
        event_timestamp=event_timestamp,
        raw_event=event,
        ocsf=ocsf,
        vendor_item_ref=VendorRef(
            id=event.get("uuid", ""),
            title=event.get("displayMessage", ""),
            url=None,
            created=event_timestamp,
        ),
        vendor_group_ref=None,
        ioc=EventIOCInfo(
            external_id=event.get("eventType", ""),
            external_name=event.get("displayMessage", ""),
            has_ioc_definition=False,
            mitre_techniques=None,
        ),
    )
    return result


def _extract_actor(actor_data: dict) -> Optional[ocsf.Actor]:
    """
    Extract actor information from Okta event with proper type filtering and mapping.

    Per specification, only map actors with types: User, AD_AGENT, SystemPrincipal, PublicClientApp
    """
    if not actor_data:
        return None

    actor_type = actor_data.get("type")

    if actor_type not in ["User", "AD_AGENT", "SystemPrincipal", "PublicClientApp"]:
        return None

    type_mapping = {
        "User": ocsf.UserType.USER,
        "AD_AGENT": ocsf.UserType.UNKNOWN,
        "SystemPrincipal": ocsf.UserType.SYSTEM,
        "PublicClientApp": ocsf.UserType.UNKNOWN,
    }

    mapped_type = type_mapping.get(actor_type, ocsf.UserType.UNKNOWN)

    email_addr = None
    if actor_type == "User":
        email_addr = actor_data.get("alternateId")

    user = ocsf.User(
        email_addr=email_addr,
        display_name=actor_data.get("displayName"),
        uid=actor_data.get("id"),
        type=mapped_type,
    )

    return ocsf.Actor(
        user=user,
    )


def _extract_target_user(target_data: list) -> Optional[ocsf.User]:
    """
    Extract target user information from Okta event target array.

    Maps target[] fields to user object per specification:
    - target[].alternateId → user.email_addr
    - target[].displayName → user.display_name
    - target[].id → user.uid
    - target[].type → user.type
    """
    if not target_data or not isinstance(target_data, list) or len(target_data) == 0:
        return None

    target = target_data[0]

    return ocsf.User(
        email_addr=target.get("alternateId"),
        display_name=target.get("displayName"),
        uid=target.get("id"),
        type=target.get("type"),
    )


def _map_event_type_to_activity(event_type: str) -> ocsf.AuthenticationActivity:
    """
    Map Okta ITP event types to OCSF Authentication Activity enum values.

    Args:
        event_type: Okta event type (e.g., "user.risk.change", "user.session.end")

    Returns:
        Appropriate AuthenticationActivity enum value
    """
    event_activity_mapping = {
        # Session-related events
        "user.session.end": ocsf.AuthenticationActivity.LOGOFF,
        "user.session.clear": ocsf.AuthenticationActivity.LOGOFF,
        "user.authentication.universal_logout": ocsf.AuthenticationActivity.LOGOFF,
        "user.authentication.universal_logout.scheduled": ocsf.AuthenticationActivity.LOGOFF,
        # Authentication and session context events
        "user.session.context.change": ocsf.AuthenticationActivity.AUTHENTICATION_TICKET,
        "policy.auth_reevaluate.action": ocsf.AuthenticationActivity.AUTHENTICATION_TICKET,
        "policy.auth_reevaluate.enforce": ocsf.AuthenticationActivity.AUTHENTICATION_TICKET,
        "policy.auth_reevaluate.fail": ocsf.AuthenticationActivity.AUTHENTICATION_TICKET,
        # Risk and threat detection events (treated as authentication monitoring)
        "user.risk.change": ocsf.AuthenticationActivity.OTHER,
        "user.risk.detect": ocsf.AuthenticationActivity.OTHER,
        "policy.entity_risk.action": ocsf.AuthenticationActivity.OTHER,
        "policy.entity_risk.evaluate": ocsf.AuthenticationActivity.OTHER,
        # Device and security monitoring events
        "device.signals.status.timeout": ocsf.AuthenticationActivity.OTHER,
        "security.events.provider.receive_event": ocsf.AuthenticationActivity.OTHER,
        # Administrative and workflow events
        "analytics.feedback.provide": ocsf.AuthenticationActivity.OTHER,
        "workflows.user.delegatedflow.run": ocsf.AuthenticationActivity.OTHER,
    }

    return event_activity_mapping.get(event_type, ocsf.AuthenticationActivity.UNKNOWN)


def _extract_device(device_data: dict) -> Optional[ocsf.Device]:
    """Extract device information from Okta event."""
    if not device_data:
        return None

    return ocsf.Device(
        uid=device_data.get("id"),
        name=device_data.get("name"),
        os=ocsf.OperatingSystem(
            name=device_data.get("os_platform"),
        ),
        is_managed=device_data.get("managed"),
    )


def _extract_src_endpoint(client_data: dict) -> Optional[ocsf.NetworkEndpoint]:
    """
    Extract source endpoint information from client data with device type mapping.

    Maps client.device per specification:
    - Computer → Desktop
    - Mobile → Mobile
    - Tablet → Tablet
    - Unknown → Unknown
    """
    if not client_data:
        return None

    device_type_mapping = {
        "Computer": ocsf.EndpointType.DESKTOP,
        "Mobile": ocsf.EndpointType.MOBILE,
        "Tablet": ocsf.EndpointType.TABLET,
        "Unknown": ocsf.EndpointType.UNKNOWN,
    }

    device_type = client_data.get("device")
    mapped_device_type = device_type_mapping.get(device_type, ocsf.EndpointType.UNKNOWN)

    return ocsf.NetworkEndpoint(
        type=mapped_device_type,
        location=ocsf.GeoLocation(
            city=client_data.get("geographicalContext", {}).get("city"),
            country=client_data.get("geographicalContext", {}).get("country"),
            lat=client_data.get("geographicalContext", {})
            .get("geolocation", {})
            .get("lat"),
            long=client_data.get("geographicalContext", {})
            .get("geolocation", {})
            .get("lon"),
            postal_code=client_data.get("geographicalContext", {}).get("postalCode"),
            region=client_data.get("geographicalContext", {}).get("state"),
            isp=client_data.get("securityContext", {}).get("isp"),
        ),
        ip=client_data.get("ipAddress"),
        os=ocsf.OperatingSystem(
            name=client_data.get("userAgent", {}).get("os"),
            type=ocsf.OSType.OTHER,
        ),
        autonomous_system=ocsf.AutonomousSystem(
            number=client_data.get("securityContext", {}).get("asNumber"),
            name=client_data.get("securityContext", {}).get("asOrg"),
        ),
        domain=client_data.get("securityContext", {}).get("domain"),
    )


def _extract_session(
    auth_context: dict, debug_context: dict = None
) -> Optional[ocsf.Session]:
    """
    Extract session information from authentication context with ITP enhancements.

    Args:
        auth_context: Authentication context from event
        debug_context: Debug context containing ITP-specific fields
    """
    if not auth_context and not debug_context:
        return None

    issuer = auth_context.get("issuer")

    session_uid = auth_context.get("externalSessionId")

    return ocsf.Session(uid=session_uid, issuer=issuer)


class OktaV1EventSync(EventSync):
    """
    Okta System Log event synchronization for security events.

    Fetches security-related events from Okta System Log API and normalizes
    them to OCSF Authentication Activity format.
    """

    PAGE_SIZE = 1000

    # Security event types based on Okta Threat Insight events
    SECURITY_EVENT_TYPES = [
        # Threat detection and attack monitoring
        "security.threat.detected",
        "security.attack.start",
        "security.attack.end",
        # Configuration and credential security
        "security.threat.configuration.update",
        "security.breached_credential.detected",
    ]

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: OktaV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        """
        Fetch security events from Okta System Log API.

        Args:
            args: Event sync arguments
            bookmark: Bookmark tracking last processed event
            **kwargs: Additional arguments

        Yields:
            Event: Normalized OCSF events
        """
        api: OktaV1Api = self.integration.get_api()

        event_filter = self._build_event_filter()

        params = {
            "since": bookmark.latest_event_published_datetime,
            "filter": event_filter,
            "limit": self.PAGE_SIZE,
            "sortOrder": "ASCENDING",
        }

        logger.info(
            f"Fetching Okta security events since {bookmark.latest_event_published_datetime}"
        )

        latest_published = bookmark.latest_event_published_datetime
        event_count = 0

        for event in api.enumerate_system_log_events(params):
            event_count += 1

            event_published = event.get("published", "")
            if event_published > latest_published:
                latest_published = event_published

            yield event

        bookmark.latest_event_published_datetime = latest_published

        logger.info(f"Processed {event_count} Okta security events")

    def _build_event_filter(self) -> str:
        """
        Build SCIM filter expression for security events.

        Returns:
            str: SCIM filter expression
        """

        event_type_filters = [
            f'eventType eq "{event_type}"' for event_type in self.SECURITY_EVENT_TYPES
        ]

        return " or ".join(event_type_filters)

    def get_permission_checks(self):  # pragma: no cover
        """Return required permission checks for this action."""
        return [OktaSystemLogsRead]
