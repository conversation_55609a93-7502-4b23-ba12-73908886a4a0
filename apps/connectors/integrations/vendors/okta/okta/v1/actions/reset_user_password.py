from apps.connectors.integrations.actions.user import (
    ResetUserPassword,
    ResetUserPasswordResult,
    ResetUserPasswordStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaUsersManage,
)


class OktaV1ResetUserPassword(ResetUserPassword):
    def execute(self, args: UserIdentifierArgs) -> ResetUserPasswordResult:
        api = self.integration.get_api()

        # Reset the user's password.  Send email to user with the OTT link to set a new password
        params = {
            "sendEmail": True,
            "revokeSessions": False,
        }
        api.reset_user_password(args.user_id.value, params=params)

        return ResetUserPasswordResult(result=ResetUserPasswordStatus(reset=True))

    def get_permission_checks(self, *args, **kwargs):
        return [OktaUsersManage]
