from apps.connectors.integrations.schemas.ocsf import (
    Actor,
    Authentication,
    AuthenticationActivity,
    AutonomousSystem,
    Device,
    EventStatus,
    GeoLocation,
    HttpRequest,
    Metadata,
    NetworkEndpoint,
    Product,
    Profile,
    Session,
    Severity,
    User,
)

status_map = {
    "Unknown": EventStatus.UNKNOWN,
    "SUCCESS": EventStatus.SUCCESS,
    "FAILURE": EventStatus.FAILURE,
    "SKIPPED": EventStatus.OTHER,
    "ALLOW": EventStatus.SUCCESS,
    "DENY": EventStatus.FAILURE,
    "CHALLENGE": EventStatus.OTHER,
    "DEFERRED": EventStatus.OTHER,
    "SCHEDULED": EventStatus.OTHER,
    "ABANDONED": EventStatus.OTHER,
    "UNANSWERED": EventStatus.OTHER,
}


def safe_get(d, keys, default=None):
    for key in keys:
        if not isinstance(d, dict):
            return default
        d = d.get(key)
    return d


def normalize_sign_in(sign_in: dict) -> Authentication:
    okta_actor = sign_in.get("actor", {})
    okta_device = sign_in.get("device", {})
    okta_authentication_context = sign_in.get("authenticationContext", {})
    okta_security_context = sign_in.get("securityContext", {})
    okta_outcome_result = sign_in.get("outcome", {}).get("result")
    okta_client = sign_in.get("client", {})
    okta_client_geographical_context = okta_client.get("geographicalContext", {})
    okta_debug_context = sign_in.get("debugContext", {})

    return Authentication(
        activity=AuthenticationActivity.LOGON,
        severity=Severity.INFORMATIONAL,
        time_dt=sign_in.get("published"),
        metadata=Metadata(
            event_code=sign_in.get("eventType"),
            product=Product(
                name="Okta System Log",
                vendor_name="Okta",
                version=sign_in.get("version"),
            ),
            profiles=[Profile.DATETIME, Profile.HOST],
            uid=sign_in.get("uuid"),
            correlation_uid=sign_in.get("uuid"),
        ),
        message=sign_in.get("displayMessage", "No message provided"),
        http_request=HttpRequest(
            user_agent=safe_get(okta_client, ["userAgent", "rawUserAgent"]),
            uid=safe_get(okta_debug_context, ["debugData", "requestId"]),
        ),
        actor=Actor(
            user=User(
                name=okta_actor.get("displayName", "Unknown User"),
                email_addr=okta_actor.get("alternateId"),
                uid=okta_actor.get("id"),
            ),
            session=Session(
                issuer=safe_get(okta_authentication_context, ["issuer", "id"]),
                uid=okta_authentication_context.get("externalSessionId"),
            ),
        ),
        device=Device(
            uid=okta_device.get("id"),
            name=okta_device.get("name", "Unknown Device"),
            is_managed=okta_device.get("managed", False),
        ),
        src_endpoint=NetworkEndpoint(
            type=okta_client.get("device"),
            autonomous_system=AutonomousSystem(
                number=okta_security_context.get("asNumber"),
                name=okta_security_context.get("asOrg"),
            ),
            domain=okta_security_context.get("domain"),
            location=GeoLocation(
                isp=okta_security_context.get("isp"),
                city=okta_client_geographical_context.get("city"),
                country=okta_client_geographical_context.get("country"),
                lat=safe_get(okta_client_geographical_context, ["geolocation", "lat"]),
                long=safe_get(okta_client_geographical_context, ["geolocation", "lon"]),
                postal_code=okta_client_geographical_context.get("postalCode"),
                region=okta_client_geographical_context.get("state"),
            ),
            ip=okta_client.get("ipAddress"),
        ),
        dst_endpoint=NetworkEndpoint(
            svc_name=safe_get(okta_debug_context, ["debugData", "url"]),
        ),
        status=status_map.get(okta_outcome_result, EventStatus.UNKNOWN),
        status_code=okta_outcome_result,
    )
