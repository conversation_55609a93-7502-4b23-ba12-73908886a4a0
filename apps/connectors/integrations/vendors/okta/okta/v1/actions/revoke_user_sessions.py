from apps.connectors.integrations.actions.user import (
    RevokeUserSessions,
    RevokeUserSessionsResult,
    RevokeUserSessionsStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import (
    OktaUsersManage,
)


class OktaV1RevokeUserSessions(RevokeUserSessions):
    def execute(self, args: UserIdentifierArgs) -> RevokeUserSessionsResult:
        api = self.integration.get_api()
        # TODO: When OAuth2 is implemented, we can set 'oauthTokens':True in query params
        # to revoke OpenID Connect and OAuth refresh and access tokens.
        # For now, we are only revoking the session cookies.
        api.revoke_user_sessions(args.user_id.value)

        return RevokeUserSessionsResult(result=RevokeUserSessionsStatus(revoked=True))

    def get_permission_checks(self, *args, **kwargs):
        return [OktaUsersManage]
