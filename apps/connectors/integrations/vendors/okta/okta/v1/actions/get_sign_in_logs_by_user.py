from apps.connectors.integrations.actions.user.get_sign_in_logs import (
    GetSignInLogsByUser,
    SignInLogsByUserIdArgs,
    SignInLogsResult,
)
from apps.connectors.integrations.vendors.okta.okta.v1.health_check import OktaLogsRead

from .authentication import normalize_sign_in


class OktaV1GetSignInLogsByUser(GetSignInLogsByUser):
    def execute(self, args: SignInLogsByUserIdArgs) -> SignInLogsResult:
        api = self.integration.get_api()
        params = {
            "since": args.start_time.isoformat(),
            "until": args.end_time.isoformat(),
            "filter": f'actor.id eq "{args.user_id.value}"',
        }
        sign_ins = api.get_sign_in_logs(params=params)

        # Normalize the sign-in logs if needed
        normalized_sign_ins = [normalize_sign_in(sign_in) for sign_in in sign_ins]

        return SignInLogsResult(result=normalized_sign_ins)

    def get_permission_checks(self, *args, **kwargs):
        return [OktaLogsRead]
