from apps.connectors.integrations import Integration

from .actions.disable_user_login import OktaV1DisableUserLogin
from .actions.enable_user_login import OktaV1EnableUserLogin
from .actions.event_sync import OktaV1EventSync
from .actions.get_external_user_profile_link import OktaV1GetExternalUserProfileLink
from .actions.get_sign_in_logs_by_ip import OktaV1GetSignInLogsByIp
from .actions.get_sign_in_logs_by_user import OktaV1GetSignInLogsByUser
from .actions.get_user_info import OktaV1GetUserInfo
from .actions.reset_user_password import OktaV1ResetUserPassword
from .actions.revoke_user_sessions import OktaV1RevokeUserSessions
from .api import OktaV1Api
from .health_check import ConnectionHealthCheck


class OktaV1Integration(Integration):
    api_class = OktaV1Api
    exception_types = ()
    actions = (
        OktaV1DisableUserLogin,
        OktaV1EnableUserLogin,
        OktaV1EventSync,
        OktaV1ResetUserPassword,
        OktaV1RevokeUserSessions,
        OktaV1GetExternalUserProfileLink,
        OktaV1GetSignInLogsByUser,
        OktaV1GetSignInLogsByIp,
        OktaV1GetUserInfo,
    )
    critical_health_checks = (ConnectionHealthCheck,)
