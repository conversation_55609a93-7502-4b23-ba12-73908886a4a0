from apps.connectors.integrations.api import ApiBase


class OktaV1Api(ApiBase):
    def __init__(self, org_url=None, api_token=None):
        static_headers = {
            "Authorization": f"SSWS {api_token}",
        }
        super().__init__(static_headers=static_headers, base_url=org_url)

    def get_org_settings(self):
        url = self.url(f"api/v1/org")
        return self.session.get(url).json()

    def enable_user_login(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/unsuspend")
        return self.session.post(url).json()

    def disable_user_login(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/suspend")
        return self.session.post(url).json()

    def reset_user_password(self, user_id: str, params=None):
        url = self.url(f"api/v1/users/{user_id}/lifecycle/reset_password")
        return self.session.post(url, params=params).json()

    def revoke_user_sessions(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/sessions")
        return self.session.delete(url)

    def get_sign_in_logs(self, params=None):
        url = self.url(f"api/v1/logs")
        return self.session.get(url, params=params).json()

    def get_user(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}")
        return self.session.get(url).json()

    def get_my_user(self):
        url = self.url(f"api/v1/users/me?expand=blocks")
        return self.session.get(url).json()

    def get_user_roles(self, user_id: str):
        url = self.url(f"api/v1/users/{user_id}/roles?expand=targets/groups")
        return self.session.get(url).json()

    def get_system_log_events(self, params=None):
        """
        Fetch system log events from Okta System Log API.

        Args:
            params (dict): Query parameters including:
                - since: ISO 8601 timestamp for lower bound
                - until: ISO 8601 timestamp for upper bound
                - filter: SCIM filter expression
                - limit: Number of results (max 1000)
                - after: Pagination token
                - sortOrder: ASCENDING or DESCENDING

        Returns:
            list: List of system log events
        """
        url = self.url("api/v1/logs")
        response = self.session.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def enumerate_system_log_events(self, params=None):
        """
        Generator that yields all system log events with automatic pagination.

        Args:
            params (dict): Query parameters for filtering events

        Yields:
            dict: Individual system log event
        """
        current_params = params.copy() if params else {}
        current_params.setdefault("limit", 1000)  # Max limit for System Log API

        while True:
            response = self.session.get(self.url("api/v1/logs"), params=current_params)
            response.raise_for_status()
            events = response.json()

            if not events:
                break

            for event in events:
                yield event

            # Check for pagination link in response headers
            link_header = response.headers.get("Link", "")
            next_link = None

            # Parse Link header for next page
            if link_header:
                links = link_header.split(",")
                for link in links:
                    if 'rel="next"' in link:
                        # Extract URL from <URL>
                        next_url = link.split(";")[0].strip().strip("<>")
                        # Extract after parameter from URL
                        if "after=" in next_url:
                            after_param = next_url.split("after=")[1].split("&")[0]
                            current_params["after"] = after_param
                            next_link = True
                            break

            if not next_link:
                break
