from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.jamf.jamf_pro.v1.api import paginate
from apps.connectors.integrations.vendors.jamf.jamf_pro.v1.health_check import ReadHosts


def normalize_host(host_data: dict):
    mac_address = host_data.get("macAddress")
    ip_address = host_data.get("ipAddress")
    os_version = host_data.get("operatingSystemVersion")
    os_build = host_data.get("operatingSystemBuild")
    os_name = f"macOS {os_version} ({os_build})"
    os = OsAttributes(host_type=HostType.WORKSTATION, family=OsFamily.MAC, name=os_name)
    hostname = host_data.get("name", "")
    fqdn = ""
    groups = []

    return Host(
        source_id=host_data.get("id"),
        group_names=groups,
        hostname=hostname,
        fqdns=fqdn,
        ip_addresses=to_list(ip_address),
        mac_addresses=to_list(mac_address),
        os=os,
        last_seen=normalize_last_seen(host_data.get("lastContactDate")),
        source_data=host_data,
    )


class JamfProV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.preview_computers, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadHosts]
