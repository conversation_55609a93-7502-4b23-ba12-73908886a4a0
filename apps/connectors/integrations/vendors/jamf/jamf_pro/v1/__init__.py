from apps.connectors.integrations import TemplateVersion

from .connection import JamfProV1Config, JamfProV1Connection
from .integration import JamfProV1Integration
from .settings import JamfProV1Settings


class JamfProV1TemplateVersion(TemplateVersion):
    integration = JamfProV1Integration
    id = "v1"
    name = "v1"
    config_model = JamfProV1Config
    connection_model = JamfProV1Connection
    settings_model = JamfProV1Settings
