import time

from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["results"]
    items_count = len(response["results"])
    total_count = response["totalCount"]
    page_count = 1
    while total_count > items_count:
        response = bound_method(page_no=page_count, **kwargs)
        yield response["results"]
        page_count = page_count + 1
        items_count = items_count + len(response["results"])


class JamfProV1Api(ApiBase):
    def __init__(self, url=None, client_id=None, client_secret=None):
        self.base_url = url
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.token_expiration_epoch = 0
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )

    @property
    def get_access_token(self):
        if self.access_token and self.token_expiration_epoch > int(time.time()):
            return self.session
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
        }
        data = {
            "client_id": self.client_id,
            "grant_type": "client_credentials",
            "client_secret": self.client_secret,
        }

        response = self.session.post(
            self.url("api/oauth/token"), headers=headers, data=data
        )
        access_token = response.json()["access_token"]
        token_expires_in = response.json()["expires_in"]
        self.access_token = access_token
        self.token_expiration_epoch = int(time.time()) + token_expires_in - 1
        headers["Authorization"] = f"Bearer {self.access_token}"
        self.session.headers.update(headers)
        return self.session

    def health_check(self):
        # https://developer.jamf.com/jamf-pro/reference/get_v1-health-check
        # This api does not return content. The status code is either 204 or 400.
        # When the status code is 400, an HttpError will be raise by the base class.
        self.get_access_token.get(self.url("/api/v1/health-check"))

    def preview_computers(self, page_no=0, page_size=100):
        # https://developer.jamf.com/jamf-pro/reference/get_preview-computers
        query_params = {"page": page_no, "page-size": page_size}
        response = self.get_access_token.get(
            self.url("/api/preview/computers"), params=query_params
        )
        return response.json()
