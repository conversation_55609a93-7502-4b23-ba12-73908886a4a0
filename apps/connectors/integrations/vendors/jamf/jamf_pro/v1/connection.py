from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class JamfProV1Config(TemplateVersionConfig):
    # https://developer.jamf.com/jamf-pro/docs/client-credentials
    url: HttpUrl = Field(
        title="API URL",
        description="API URL for the Jamf Pro server",
    )
    client_id: str = Field(
        title="Client ID",
        description="API Client ID",
        max_length=1024,
    )
    client_secret: EncryptedStr = Field(
        title="Client Secret",
        description="API Client Secret",
        max_length=1024,
    )


class JamfProV1Connection(ConnectionTemplate):
    id = "jamf_pro"
    name = "Jamf Pro"
    config_model = JamfProV1Config
