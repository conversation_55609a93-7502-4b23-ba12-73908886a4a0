from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CylanceV1TemplateVersion


class CylanceTemplate(Template):
    id = "cylance"
    name = "Cylance"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        CylanceV1TemplateVersion.id: CylanceV1TemplateVersion(),
    }
    endpoint_coverage_available = True
    vendor = Vendors.CYLANCE
