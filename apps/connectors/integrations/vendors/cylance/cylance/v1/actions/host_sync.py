from typing import Generator

from apps.connectors.integrations.actions import HostSync, normalize, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.actions.utils import (
    normalize_last_seen,
)
from apps.connectors.integrations.vendors.cylance.cylance.v1.api import paginate
from apps.connectors.integrations.vendors.cylance.cylance.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    hostname = host_data.get("host_name")
    os_name = host_data.get("os_version")
    mac_addresses = host_data.get("mac_addresses")

    return Host(
        source_id=host_data["id"],
        hostname=hostname,
        ip_addresses=to_list(host_data.get("ip_addresses")),
        mac_addresses=mac_addresses,
        _os_name=os_name,
        last_seen=normalize_last_seen(host_data.get("date_offline")),
        source_data=host_data,
    )


class CylanceV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_devices_extended, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
