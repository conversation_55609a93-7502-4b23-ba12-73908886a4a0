from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


# enum class with url choices
class CylanceV1Url(StrEnum):
    NA = "https://protectapi.cylance.com/"
    APNE1 = "https://protectapi-apne1.cylance.com/"
    AU = "https://protectapi-au.cylance.com/"
    EUC1 = "https://protectapi-euc1.cylance.com/"
    SAE1 = "https://protectapi-sae1.cylance.com/"
    US = "https://protectapi.us.cylance.com/"


class CylanceV1Config(TemplateVersionConfig):
    url: CylanceV1Url = Field(
        title="Cylance  URL",
        description="The service endpoint used to connect with Cylance. See https://docs.blackberry.com/en/unified-endpoint-security/blackberry-ues/Cylance-API-user-guide/RESTful_API/Authentication/Service-endpoint to select the correct URL for your region.",
    )
    tenant_id: str = Field(
        title="Tenant ID",
        description="Tenant ID",
    )
    app_id: str = Field(
        title="Application ID",
        description="Application ID",
    )
    app_secret: str = Field(
        title="Application Secret",
        description="Application Secret",
    )


class CylanceV1Connection(ConnectionTemplate):
    id = "cylance"
    name = "Cylance"
    config_model = CylanceV1Config
