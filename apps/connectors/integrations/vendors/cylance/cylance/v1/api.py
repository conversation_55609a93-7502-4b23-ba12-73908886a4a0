import uuid
from datetime import datetime, timedelta

import jwt  # PyJWT version 1.7.1 as of the time of authoring.

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["page_items"]
    page_no = 1
    while True:
        page_no = page_no + 1
        response = bound_method(**kwargs, page_no=page_no)
        if ("page_items" in response) and len(response["page_items"]) > 0:
            yield response["page_items"]
        else:
            break


class CylanceV1Api(ApiBase):
    def __init__(
        self, url=None, tenant_id=None, app_id=None, app_secret=None, **kwargs
    ):
        self.tenant_id = tenant_id
        self.app_id = app_id
        self.app_secret = app_secret

        static_headers = {"Accept": "application/json"}
        super().__init__(base_url=url, static_headers=static_headers)

    def generate_token(self):
        # 30 minutes from now
        timeout = 1800
        now = datetime.now()
        timeout_datetime = now + timedelta(seconds=timeout)
        epoch_time = int((now - datetime(1970, 1, 1)).total_seconds())
        epoch_timeout = int((timeout_datetime - datetime(1970, 1, 1)).total_seconds())

        jti_val = str(uuid.uuid4())
        tid_val = self.tenant_id
        app_id = self.app_id
        app_secret = self.app_secret

        auth_url = self.url("auth/v2/token")

        claims = {
            "exp": epoch_timeout,
            "iat": epoch_time,
            "iss": "http://cylance.com",
            "sub": app_id,
            "tid": tid_val,
            "jti": jti_val,
        }
        encoded = jwt.encode(claims, app_secret, algorithm="HS256")

        payload = {"auth_token": encoded}
        resp = self.session.post(auth_url, json=payload)

        return resp.json()["access_token"]

    def ensure_token(self):
        if not hasattr(self, "_token"):
            self._token = self.generate_token()
            self.session.headers.update({"Authorization": f"Bearer {self._token}"})
        return self._token

    def get_devices_extended(self, page_no=1):
        """
        Get a list of devices for a tenant
        """
        query_params = {
            "page": page_no,
            "page_size": 100,
        }

        self.ensure_token()
        url_path = self.url("devices/v2/extended")
        response = self.session.get(url_path, params=query_params)

        return response.json()
