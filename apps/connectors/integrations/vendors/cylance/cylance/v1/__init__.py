from apps.connectors.integrations import TemplateVersion

from .connection import CylanceV1Config, CylanceV1Connection
from .integration import CylanceV1Integration
from .settings import CylanceV1Settings


class CylanceV1TemplateVersion(TemplateVersion):
    integration = CylanceV1Integration
    id = "v1"
    name = "v1"
    config_model = CylanceV1Config
    connection_model = CylanceV1Connection
    settings_model = CylanceV1Settings
