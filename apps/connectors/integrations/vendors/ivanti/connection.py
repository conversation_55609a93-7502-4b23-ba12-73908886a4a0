from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class IvantiConfig(TemplateVersionConfig):
    # https://help.ivanti.com/ht/help/en_US/CLOUD/api/Shared-Content/authenticate_api.htm
    ivanti_neurons_token: EncryptedStr = Field(
        title="Ivanti Neurons Token",
        description="Ivanti Neurons Token",
    )
    client_secret: EncryptedStr = Field(
        title="Client Secret",
        description="Client Secret",
    )
    tenant_id: str = Field(
        title="Tenant ID",
        description="Tenant ID",
    )
    client_id: str = Field(
        title="Client ID",
        description="Client ID",
    )
    rapid_api_key: str = Field(
        title="Rapid API Key",
        description="Rapid API Key",
    )
    rapid_api_host: str = Field(
        title="Rapid API Host",
        description="Rapid API Host",
    )


class IvantiConnection(ConnectionTemplate):
    id = "ivanti"
    name = "<PERSON><PERSON>"
    config_model = IvantiConfig
