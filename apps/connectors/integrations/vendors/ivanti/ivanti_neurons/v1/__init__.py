from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.ivanti.connection import IvantiConnection

from .integration import IvantiNeuronsV1Integration
from .settings import IvantiNeuronsV1Settings


class IvantiNeuronsV1TemplateVersion(TemplateVersion):
    integration = IvantiNeuronsV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = IvantiConnection
    settings_model = IvantiNeuronsV1Settings
