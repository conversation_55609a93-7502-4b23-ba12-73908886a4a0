from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.ivanti.ivanti_neurons.v1.actions.host_sync import (
    IvantiNeuronsV1HostSync,
)

from .api import IvantiNeuronsV1Api
from .health_check import ConnectionHealthCheck


class IvantiNeuronsV1Integration(Integration):
    api_class = IvantiNeuronsV1Api
    actions = (IvantiNeuronsV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
