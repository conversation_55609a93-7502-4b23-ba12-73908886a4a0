from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
)
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.vendors.ivanti.ivanti_neurons.v1.api import paginate
from apps.connectors.integrations.vendors.ivanti.ivanti_neurons.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    os_name = host_data.get("OS", {}).get("Name")
    mac_addresses = []
    hostname = host_data.get("DeviceName")
    ip_addresses = [host_data.get("Network", {}).get("TCPIP", {}).get("Address")]
    fqdns = []

    return Host(
        source_id=host_data.get("DiscoveryId"),
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        _os_name=os_name,
        last_seen=None,
        source_data=host_data,
    )


class IvantiNeuronsV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_devices, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
