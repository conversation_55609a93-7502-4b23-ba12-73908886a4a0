import time

from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["value"]
    next_page = response.get("@odata.nextLink", None)
    while len(response["value"]) > 0 and next_page:
        response = bound_method(**kwargs, next_page=next_page)
        if len(response["value"]) > 0:
            yield response["value"]
            next_page = response.get("@odata.nextLink", None)
        else:
            break


class IvantiNeuronsV1Api(ApiBase):
    def __init__(
        self,
        ivanti_neurons_token,
        client_secret,
        tenant_id,
        client_id,
        rapid_api_key,
        rapid_api_host,
    ):
        self.ivanti_api_url = "https://people-and-device-inventory.p.rapidapi.com"
        self.ivanti_neurons_token = ivanti_neurons_token
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.rapid_api_key = rapid_api_key
        self.rapid_api_host = rapid_api_host
        self.access_token = None
        self.expires_in = None
        super().__init__(
            base_url=self.ivanti_api_url, static_headers={"Accept": "application/json"}
        )

    @property
    def get_access_token(self):
        # https://help.ivanti.com/ht/help/en_US/CLOUD/api/Patch-Mgmt/patch-authenication.htm
        if self.access_token and self.expires_in > time.time():
            return self.session
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": "Bearer " + self.ivanti_neurons_token,
            "X-ClientSecret": self.client_secret,
            "X-TenantId": self.tenant_id,
            "X-ClientId": self.client_id,
            "X-RapidAPI-Key": self.rapid_api_key,
            "X-RapidAPI-Host": self.rapid_api_host,
        }
        self.session.headers.update(headers)
        response = self.session.get(
            self.url("/api/apigatewaydataservices/authentication"), headers=headers
        )
        self.access_token = response.json()["access_token"]
        self.expires_in = time.time() + response.json()["expires_in"]
        headers = {
            "Authorization": "Bearer " + self.access_token,
            "X-RapidAPI-Key": self.rapid_api_key,
            "X-RapidAPI-Host": self.rapid_api_host,
        }
        self.session.headers.update(headers)
        return self.session

    def get_devices(self, next_page=None):
        # https://help.ivanti.com/ht/help/en_US/CLOUD/api/PeopleDevices/neuronsplatformDevices.htm
        if next_page:
            return self.get_access_token.get(next_page).json()
        return self.get_access_token.get(
            self.url("/api/apigatewaydataservices/v1/devices")
        ).json()

    def test_connection(self):
        self.get_access_token
