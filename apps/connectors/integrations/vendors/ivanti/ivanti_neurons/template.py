from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import IvantiNeuronsV1TemplateVersion


class IvantiNeuronsTemplate(Template):
    id = "ivanti_neurons"
    name = "Ivanti Neurons"
    category = Template.Category.ASSET_SOURCE
    versions = {
        IvantiNeuronsV1TemplateVersion.id: IvantiNeuronsV1TemplateVersion(),
    }
    vendor = Vendors.IVANTI
