from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import IvantiPmV1TemplateVersion


class IvantiPmTemplate(Template):
    id = "ivanti_pm"
    name = "Ivanti Neurons for Patch Management"
    category = Template.Category.VULNERABILITY_MANAGEMENT
    versions = {
        IvantiPmV1TemplateVersion.id: IvantiPmV1TemplateVersion(),
    }
    vulnerability_coverage_available = True
    vendor = Vendors.IVANTI
