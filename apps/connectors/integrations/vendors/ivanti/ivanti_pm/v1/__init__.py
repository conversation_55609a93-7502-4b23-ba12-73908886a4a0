from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.template import EmptyConfig
from apps.connectors.integrations.vendors.ivanti.connection import IvantiConnection

from .integration import IvantiPmV1Integration
from .settings import IvantiPmV1Settings


class IvantiPmV1TemplateVersion(TemplateVersion):
    integration = IvantiPmV1Integration
    id = "v1"
    name = "v1"
    config_model = EmptyConfig
    connection_model = IvantiConnection
    settings_model = IvantiPmV1Settings
