from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CiscoDuoV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Host",
        description="API hostname obtained from the Duo Admin Panel",
    )
    ikey: EncryptedStr = Field(
        title="Integration Key",
        description="Duo Admin API application's integration key",
    )
    skey: EncryptedStr = Field(
        title="Secret Key",
        description="Duo Admin API application's secret key",
    )


class CiscoDuoV1Connection(ConnectionTemplate):
    id = "cisco_duo"
    name = "Cisco Duo"
    config_model = CiscoDuoV1Config
