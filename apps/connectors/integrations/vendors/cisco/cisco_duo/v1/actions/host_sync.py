from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.actions.utils import (
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import paginate
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    hostname = host_data.get("device_name")
    os_name = host_data.get("os_family") + " " + host_data.get("os_version")
    owners = (
        [
            OwnerAttributes(
                name=host_data.get("device_username"),
                email=host_data.get("email") or None,
            )
        ]
        if host_data.get("device_username")
        else []
    )
    return Host(
        source_id=host_data["computer_sid"],
        group_names=[],
        hostname=hostname,
        fqdns=[],
        ip_addresses=[],
        mac_addresses=[],
        _os_name=os_name,
        owners=owners,
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("last_updated")),
        source_data=host_data,
    )


class CiscoDuoV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_endpoints, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
