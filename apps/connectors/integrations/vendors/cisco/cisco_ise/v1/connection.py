from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CiscoIseV1Config(TemplateVersionConfig):
    # https://developer.cisco.com/docs/identity-services-engine/latest/authentication/#authentication
    url: HttpUrl = Field(
        title="API URL",
        description="API URL for the Cisco ISE server",
    )
    username: str = Field(
        title="Username",
        description="API Username",
        max_length=1024,
    )
    password: EncryptedStr = Field(
        title="Password",
        description="API Password",
        max_length=1024,
    )


class CiscoIseV1Connection(ConnectionTemplate):
    id = "cisco_ise"
    name = "Cisco ISE"
    config_model = CiscoIseV1Config
