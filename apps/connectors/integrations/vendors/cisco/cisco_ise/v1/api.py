from urllib.parse import urlparse

from requests.auth import HTTPBasic<PERSON>uth

from apps.connectors.integrations import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response
    items_count = len(response)
    page_count = 1
    while items_count > 0:
        response = bound_method(page_no=page_count, **kwargs)
        items_count = len(response)
        if items_count > 0:
            yield response
        page_count = page_count + 1


class CiscoIseV1Api(ApiBase):
    def __init__(self, url=None, username=None, password=None):
        parsed_url = urlparse(url)
        self.baseurl = f"{parsed_url.scheme}://{parsed_url.netloc}:9060"
        self.username = username
        self.password = password

        super().__init__(
            base_url=self.baseurl, static_headers={"Accept": "application/json"}
        )
        self.session.auth = HTTPBasicAuth(self.username, self.password)

    def endpoints(self, page_no=0, page_size=100):
        # https://developer.cisco.com/docs/identity-services-engine/latest/get-all-endpoints/
        query_params = {"page": page_no, "size": page_size}
        response = self.session.get(self.url("api/v1/endpoint"), params=query_params)
        return response.json()
