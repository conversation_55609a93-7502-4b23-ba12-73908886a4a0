from apps.connectors.integrations import TemplateVersion

from .connection import CiscoIseV1Config, CiscoIseV1Connection
from .integration import CiscoIseV1Integration
from .settings import CiscoIseV1Settings


class CiscoIseV1TemplateVersion(TemplateVersion):
    integration = CiscoIseV1Integration
    id = "v1"
    name = "v1"
    config_model = CiscoIseV1Config
    connection_model = CiscoIseV1Connection
    settings_model = CiscoIseV1Settings
