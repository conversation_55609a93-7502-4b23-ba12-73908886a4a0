from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.vendors.cisco.cisco_ise.v1.api import paginate
from apps.connectors.integrations.vendors.cisco.cisco_ise.v1.health_check import (
    ReadHosts,
)


def normalize_host(host_data: dict):
    mac_address = host_data.get("mac")
    ip_address = host_data.get("ipAddress")
    # https://www.cisco.com/c/en/us/td/docs/security/ise/3-1/admin_guide/b_ise_admin_3_1/b_ISE_admin_31_secure_wired_access.html
    os_name = host_data.get("mdmAttributes").get("OsVersion")
    hostname = host_data.get("name", "")
    fqdn = []
    groups = [host_data.get("groupId")]

    return Host(
        source_id=host_data["id"],
        group_names=groups,
        hostname=hostname,
        fqdns=fqdn,
        ip_addresses=to_list(ip_address),
        mac_addresses=to_list(mac_address),
        _os_name=os_name,
        last_seen=normalize_last_seen(
            host_data.get("mdmAttributes").get("lastCheckinTimeStamp")
        ),
        source_data=host_data,
    )


class CiscoIseV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.endpoints, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadHosts]
