from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CiscoIseV1TemplateVersion


class CiscoIseTemplate(Template):
    id = "cisco_ise"
    name = "Cisco Identity Services Engine (ISE)"
    category = Template.Category.ASSET_SOURCE
    vendor = Vendors.CISCO
    versions = {
        CiscoIseV1TemplateVersion.id: CiscoIseV1TemplateVersion(),
    }
