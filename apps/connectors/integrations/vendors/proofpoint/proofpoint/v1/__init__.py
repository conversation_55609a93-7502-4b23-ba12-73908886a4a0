from apps.connectors.integrations import TemplateVersion

from .bookmarks import ProofpointV1Bookmarks
from .connection import ProofpointV1Config, ProofpointV1Connection
from .integration import ProofpointV1Integration
from .settings import ProofpointV1Settings


class ProofpointV1TemplateVersion(TemplateVersion):
    integration = ProofpointV1Integration
    id = "v1"
    name = "v1"
    config_model = ProofpointV1Config
    connection_model = ProofpointV1Connection
    settings_model = ProofpointV1Settings
    bookmarks_model = ProofpointV1Bookmarks
