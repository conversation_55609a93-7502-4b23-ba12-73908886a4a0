from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_query_end_time():
    return (
        (datetime.now(timezone.utc) - timedelta(days=1))
        .isoformat()
        .replace("+00:00", "Z")
    )


class ProofpointV1EventSyncBookmark(TemplateVersionActionBookmark):
    query_end_time: str = Field(
        title="Latest Event Query End Datetime",
        description="The Latest Event Query End Datetime fetched.",
        default_factory=default_query_end_time,
    )


ProofpointV1Bookmarks = create_bookmarks_model(
    "ProofpointV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: ProofpointV1EventSyncBookmark,
    },
)
