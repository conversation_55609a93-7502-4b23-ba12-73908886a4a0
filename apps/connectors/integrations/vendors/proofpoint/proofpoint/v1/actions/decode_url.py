from apps.connectors.integrations.actions.decode_url import (
    DecodeUrl,
    DecodeUrlArgs,
    DecodeUrlResult,
)
from apps.connectors.integrations.schemas.ocsf import Url
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.api import (
    ProofpointV1Api,
)


class ProofpointV1DecodeUrl(DecodeUrl):
    """
    Decode a URL using the Proofpoint API.
    This action sends a request to the Proofpoint API to decode the provided URL.
    """

    def execute(self, args: DecodeUrlArgs) -> DecodeUrlResult:
        api: ProofpointV1Api = self.integration.get_api()
        result = api.decode_url([args.url.value])
        return DecodeUrlResult(result=Url(url_string=result.get("decodedUrl")))

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return []
