from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class ProofpointV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with Proofpoint.",
    )
    principal: str = Field(
        title="Principal",
        description="Principal used to authenticate with the Proofpoint API.",
    )
    secret: EncryptedStr = Field(
        title="Secret",
        description="Secret used to authenticate with the Proofpoint API.",
    )


class ProofpointV1Connection(ConnectionTemplate):
    id = "proofpoint"
    name = "Proofpoint"
    config_model = ProofpointV1Config
