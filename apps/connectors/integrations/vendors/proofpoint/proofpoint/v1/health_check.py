from datetime import datetime, timedelta, timezone

from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        now = now = datetime.now(timezone.utc)
        from_dt = (now - timedelta(seconds=30)).strftime("%Y-%m-%dT%H:%M:%SZ")
        to_dt = now.strftime("%Y-%m-%dT%H:%M:%SZ")
        try:
            api.get_all_siem_events(from_datetime=from_dt, to_datetime=to_dt)
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
