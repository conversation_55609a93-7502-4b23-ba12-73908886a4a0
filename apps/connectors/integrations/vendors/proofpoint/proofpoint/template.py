from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ProofpointV1TemplateVersion


class ProofpointTemplate(Template):
    id = "proofpoint"
    name = "Proofpoint"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        ProofpointV1TemplateVersion.id: ProofpointV1TemplateVersion(),
    }
    vendor = Vendors.PROOFPOINT
