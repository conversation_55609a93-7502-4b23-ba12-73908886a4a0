from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class SentinelOneConfig(TemplateVersionConfig):
    api_key: EncryptedStr = Field(
        title="API Key",
        description="API Key used to authenticate with SentinelOne.",
    )
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate with SentinelOne.",
    )


class SentinelOneConnection(ConnectionTemplate):
    id = "sentinel_one"
    name = "SentinelOne"
    config_model = SentinelOneConfig
