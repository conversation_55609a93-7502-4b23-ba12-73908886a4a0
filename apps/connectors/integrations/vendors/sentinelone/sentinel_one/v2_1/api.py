import logging
from copy import deepcopy
from urllib.parse import urljoin

from apps.connectors.integrations import ApiBase

logger = logging.getLogger(__name__)


PAGE_LIMIT = 100


def paginate(bound_method, params=None):
    params = params or {}
    response = bound_method(params=params)
    yield response["data"]

    while response["pagination"]["nextCursor"]:
        cursor_prams = deepcopy(params)
        cursor_prams["cursor"] = response["pagination"]["nextCursor"]
        response = bound_method(params=cursor_prams)
        yield response["data"]


class S1BaseApi(ApiBase):
    """
    SentinelOne API wrapper
    API Docs are hosted for each subdomain, e.g.:
    https://{subdomain}.sentinelone.net/api-doc/overview
    """

    def __init__(self, url=None, api_key=None, **kwargs):
        if not all([url, api_key]):
            raise ValueError("URL and API Key are required.")

        self._api_key = api_key

        # Since our tokens supposedly don't expire, there's no need for any caching here
        static_headers = {
            "Accept": "application/json",
            "Authorization": "ApiToken %s" % api_key,
        }
        api_version = "v2.1"
        base_url = urljoin(url, f"web/api/{api_version}/")
        super().__init__(static_headers=static_headers, base_url=base_url)

    def api_token_details(self):
        url = self.url("users/api-token-details")
        data = {"data": {"apiToken": self._api_key}}
        response = self.session.post(url, json=data)
        return response.json()

    def generate_api_token(self):
        url = self.url("users/generate-api-token")
        response = self.session.post(url)
        return response.json()


class SentinelOneV21API(S1BaseApi):
    def get_agents(self, params=None):
        """
        Returns the list of all agents (endpoints/devices) in the tenant
        """
        url = self.url("agents")
        default_params = {"limit": PAGE_LIMIT, "countOnly": False, "skipCount": False}

        params = {**default_params, **params} if params else default_params

        response = self.session.get(url, params=params)
        return response.json()

    def disconnect_device(self, agent_id):
        url = self.url("agents/actions/disconnect")
        data = {
            "filter": {
                "ids": [
                    agent_id,
                ]
            }
        }

        response = self.session.post(url, json=data)
        return response.json()

    def connect_device(self, agent_id):
        url = self.url("agents/actions/connect")
        data = {
            "filter": {
                "ids": [
                    agent_id,
                ]
            }
        }

        response = self.session.post(url, json=data)
        return response.json()

    def initiate_scan_on_device(self, agent_id):
        url = self.url("agents/actions/initiate-scan")
        data = {
            "filter": {
                "ids": [
                    agent_id,
                ]
            }
        }

        response = self.session.post(url, json=data)
        return response.json()
