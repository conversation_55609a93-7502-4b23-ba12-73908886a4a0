from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.sentinelone.connection import (
    SentinelOneConfig,
    SentinelOneConnection,
)

from .integration import SentinelOneV21Integration
from .settings import SentinelOneV21Settings


class SentinelOneV21TemplateVersion(TemplateVersion):
    integration = SentinelOneV21Integration
    id = "v2_1"
    name = "v2.1"
    config_model = SentinelOneConfig
    connection_model = SentinelOneConnection
    settings_model = SentinelOneV21Settings
