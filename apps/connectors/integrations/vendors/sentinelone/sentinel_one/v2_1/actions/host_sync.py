from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import Host, HostSync, HostSyncArgs
from apps.connectors.integrations.actions.utils import (
    parse_fqdn,
    to_list,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.sentinelone.sentinel_one.v2_1.api import (
    paginate,
)

HOST_TYPE_MAP = {
    "desktop": HostType.WORKSTATION,
    "laptop": HostType.WORKSTATION,
    "server": HostType.SERVER,
    # Technically a server, but marked as a container for now to help filter out.
    "kubernetes node": HostType.CONTAINER,
}


def normalize_host(host_data: dict):
    group_name = to_list(host_data.get("groupName"))
    ip_addresses = []
    if ip := host_data.get("externalIp"):
        ip_addresses.append(ip)
    if ip := host_data.get("lastIpToMgmt"):
        ip_addresses.append(ip)
    mac_addresses = []
    for entry in host_data.get("networkInterfaces", []):
        for ip_key in ["inet", "inet6"]:
            for ip in entry.get(ip_key, []):
                if ip not in ip_addresses:
                    ip_addresses.append(ip)
        if mac := entry.get("physical"):
            mac_addresses.append(mac)

    host_type = HOST_TYPE_MAP.get(host_data.get("machineType"), HostType.UNKNOWN)
    os_name = " ".join(
        [
            host_data.get("osName", ""),
            host_data.get("osRevision", ""),
        ]
    ).strip()
    os_family, __ = OsFamily.from_string(os_name)
    hostname = host_data.get("computerName", "")
    domain = host_data.get("domain")

    # Some agents fail to parse the hostname from the fqdn
    if domain == "unknown":
        hostname, domain = parse_fqdn(hostname)

    return Host(
        source_id=host_data["id"],
        group_names=group_name,
        hostname=hostname,
        _domain=domain,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        last_seen=normalize_last_seen(host_data.get("lastActiveDate")),
        source_data=host_data,
    )


class SentinelOneV21HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_agents):
            yield from page

    def get_permission_checks(self):
        return []
