from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.api_token_details()
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
