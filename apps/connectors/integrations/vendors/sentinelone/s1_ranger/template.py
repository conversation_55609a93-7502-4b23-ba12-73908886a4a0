from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v2_1 import S1RangerV21TemplateVersion


class S1RangerTemplate(Template):
    id = "s1_ranger"
    name = "SentinelOne Singularity Ranger"
    category = Template.Category.ASSET_SOURCE
    versions = {
        S1RangerV21TemplateVersion.id: S1RangerV21TemplateVersion(),
    }
    vendor = Vendors.SENTINELONE
