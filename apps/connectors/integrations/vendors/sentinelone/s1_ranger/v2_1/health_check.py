from requests import HTTPError

from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)


class ReadDevices(IntegrationPermissionsHealthCheck):
    name = "Read Devices"
    description = "Read devices from Ranger Device Inventory"
    value = "ranger:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            result = self.integration.invoke(
                "get_table_view", params={"countOnly": True}
            )
            return (
                IntegrationHealthCheckResult.PASSED
                if result
                else IntegrationHealthCheckResult.FAILED
            )
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.api_token_details()
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
