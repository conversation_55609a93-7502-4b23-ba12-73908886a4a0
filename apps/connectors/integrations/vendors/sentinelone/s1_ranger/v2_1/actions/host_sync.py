from typing import Generator

from apps.connectors.integrations.actions import (
    HostSync,
    normalize,
    normalize_last_seen,
)
from apps.connectors.integrations.actions.host_sync import Host, HostSyncArgs
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.sentinelone.s1_ranger.v2_1.health_check import (
    ReadDevices,
)
from apps.connectors.integrations.vendors.sentinelone.sentinel_one.v2_1.api import (
    paginate,
)

HOST_TYPE_MAP = {
    "Audio Visual": HostType.OTHER,
    "Embedded": HostType.OTHER,
    "Gaming Console": HostType.OTHER,
    "IP Phone": HostType.MOBILE,
    "IoT": HostType.OTHER,
    "Mobile": HostType.MOBILE,
    "Network": HostType.OTHER,
    "Printer": HostType.OTHER,
    "Security": HostType.OTHER,
    "Server": HostType.SERVER,
    "Smart Office": HostType.OTHER,
    "Storage": HostType.OTHER,
    "Unknown": HostType.UNKNOWN,
    "Workstation": HostType.WORKSTATION,
}

OS_FAMILY_MAP = {
    "Android": OsFamily.ANDROID,
    "Apple": OsFamily.MAC,
    "Cisco": OsFamily.UNKNOWN,
    "Linux": OsFamily.LINUX,
    "Unix": OsFamily.LINUX,
    "Windows": OsFamily.WINDOWS,
    "Windows Legacy": OsFamily.WINDOWS,
    "Wyse": OsFamily.UNKNOWN,
}


def normalize_host(host_data: dict):
    hostnames = host_data.get("hostnames") or []
    hostname = hostnames[0] if hostnames else ""
    domain = host_data.get("domain")

    public_ip = host_data.get("externalIp")
    public_ips = [public_ip] if public_ip else []
    private_ips = host_data.get("ipAddresses")
    ips = private_ips + public_ips
    mac_addresses = host_data.get("macAddress")
    os_family = OS_FAMILY_MAP.get(host_data.get("osType"), OsFamily.UNKNOWN)
    os_name = host_data.get("osName")
    host_type = HOST_TYPE_MAP.get(host_data.get("deviceType"), HostType.UNKNOWN)

    os = OsAttributes(host_type=host_type, family=os_family, name=os_name)

    return Host(
        source_id=host_data.get("id"),
        hostname=hostname,
        _domain=domain,
        ip_addresses=ips,
        mac_addresses=mac_addresses,
        os=os,
        last_seen=normalize_last_seen(host_data.get("lastSeen")),
        source_data=host_data,
    )


class S1RangerV21HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_table_view):
            yield from page

    def get_permission_checks(self):
        return [ReadDevices]
