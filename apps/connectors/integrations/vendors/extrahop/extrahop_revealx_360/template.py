from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ExtrahopRevealx360V1TemplateVersion


class ExtrahopRevealx360Template(Template):
    id = "extrahop_revealx_360"
    name = "ExtraHop RevealX 360"
    category = Template.Category.ASSET_SOURCE
    versions = {
        ExtrahopRevealx360V1TemplateVersion.id: ExtrahopRevealx360V1TemplateVersion(),
    }
    vendor = Vendors.EXTRAHOP
