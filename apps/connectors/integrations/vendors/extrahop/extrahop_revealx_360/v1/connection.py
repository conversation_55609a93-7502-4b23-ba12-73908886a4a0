from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class ExtrahopRevealx360V1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="ExtraHop RevealX 360  URL",
        description="The service endpoint used to connect with Extrahop RevealX 360.",
    )
    id: str = Field(
        title="ID",
        description="REST API Credentials - ID",
    )
    secret: EncryptedStr = Field(
        title="Secret",
        description="REST API Credentials - Secret",
    )


class ExtrahopRevealx360V1Connection(ConnectionTemplate):
    id = "extrahop_revealx_360"
    name = "Extrahop RevealX 360"
    config_model = ExtrahopRevealx360V1Config
