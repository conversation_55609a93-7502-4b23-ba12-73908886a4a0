from datetime import datetime
from typing import Generator

from django.utils.timezone import make_aware

from apps.connectors.integrations.actions import HostSync, normalize
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSyncArgs,
)
from apps.connectors.integrations.actions.utils import (
    normalize_last_seen,
)
from apps.connectors.integrations.vendors.extrahop.extrahop_revealx_360.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.extrahop.extrahop_revealx_360.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    hostname = host_data.get("display_name")
    os_name = None
    ip_addresses = []
    ip_addresses.append(host_data.get("ipaddr4"))
    ip_addresses.append(host_data.get("ipaddr6"))
    unix_timestamp = host_data.get("mod_time") / 1000
    last_seen = str(
        make_aware(datetime.utcfromtimestamp(unix_timestamp))
        if unix_timestamp
        else None
    )
    return Host(
        source_id=host_data["id"],
        hostname=hostname,
        ip_addresses=ip_addresses,
        mac_addresses=host_data.get("macaddr"),
        _os_name=os_name,
        last_seen=normalize_last_seen(last_seen),
        source_data=host_data,
    )


class ExtrahopRevealx360V1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_devices, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
