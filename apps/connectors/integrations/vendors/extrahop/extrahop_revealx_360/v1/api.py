import base64

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["result_fields"]
    offset = 0
    while True:
        offset = offset + 1
        response = bound_method(**kwargs, offset=offset)
        if ("result_fields" in response) and len(response["result_fields"]) > 0:
            yield response["result_fields"]
        else:
            break


class ExtrahopRevealx360V1Api(ApiBase):
    def __init__(self, url=None, id=None, secret=None, **kwargs):
        self.id = id
        self.secret = secret
        static_headers = {
            "Accept": "application/json",
        }
        super().__init__(base_url=url, static_headers=static_headers)

    def get_token(self):
        """
        Method that generates and retrieves a temporary API access token for RevealX 360 authentication.
            Returns:
                str: A temporary API access token
        """
        auth = base64.b64encode(bytes(f"{self.id}:{self.secret}", "utf-8")).decode(
            "utf-8"
        )
        headers = {
            "Authorization": "Basic " + auth,
            "Content-Type": "application/x-www-form-urlencoded",
        }
        url_path = self.url("/oauth2/token")
        response = self.session.post(
            url_path,
            headers=headers,
            data="grant_type=client_credentials",
        )
        return response.json()["access_token"]

    def get_devices(self, offset=0, limit=100):
        assert 1 <= limit <= 10000
        url_path = self.url("/api/v1/devices")
        params = {
            "offset": offset,
            "limit": limit,
        }
        self.session.headers.update(
            {
                "Authorization": f"Bearer {self.get_token}",
                "Content-Type": "application/json",
            }
        )
        response = self.session.get(url_path, params=params)
        return response.json()
