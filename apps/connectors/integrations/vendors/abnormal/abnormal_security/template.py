from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import AbnormalSecurityV1TemplateVersion


class AbnormalSecurityTemplate(Template):
    id = "abnormal_security"
    name = "Abnormal Security"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        AbnormalSecurityV1TemplateVersion.id: AbnormalSecurityV1TemplateVersion(),
    }
    vendor = Vendors.ABNORMAL
