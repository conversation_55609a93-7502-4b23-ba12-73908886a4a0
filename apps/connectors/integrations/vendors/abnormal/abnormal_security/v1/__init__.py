from apps.connectors.integrations import TemplateVersion

from .bookmarks import AbnormalSecurityV1Bookmarks
from .connection import AbnormalSecurityV1Config, AbnormalSecurityV1Connection
from .integration import AbnormalSecurityV1Integration
from .settings import AbnormalSecurityV1Settings


class AbnormalSecurityV1TemplateVersion(TemplateVersion):
    integration = AbnormalSecurityV1Integration
    id = "v1"
    name = "v1"
    config_model = AbnormalSecurityV1Config
    connection_model = AbnormalSecurityV1Connection
    settings_model = AbnormalSecurityV1Settings
    bookmarks_model = AbnormalSecurityV1Bookmarks
