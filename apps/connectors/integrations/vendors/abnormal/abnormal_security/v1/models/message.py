# generated by datamodel-codegen:
#   filename:  message.json
#   timestamp: 2025-05-08T01:41:19+00:00

from __future__ import annotations

from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field


class Message(BaseModel):
    model_config = ConfigDict(
        extra="allow",
    )
    threat_id: Optional[str] = Field(None, alias="threatId")
    abx_message_id_str: Optional[str] = Field(None, alias="abxMessageIdStr")
    abx_message_id: Optional[int] = Field(None, alias="abxMessageId")
    abx_portal_url: Optional[str] = Field(None, alias="abxPortalUrl")
    subject: Optional[str] = None
    from_address: Optional[str] = Field(None, alias="fromAddress")
    from_name: Optional[str] = Field(None, alias="fromName")
    sender_domain: Optional[str] = Field(None, alias="senderDomain")
    to_addresses: Optional[List[str]] = Field(None, alias="toAddresses")
    recipient_address: Optional[str] = Field(None, alias="recipientAddress")
    received_time: Optional[str] = Field(None, alias="receivedTime")
    sent_time: Optional[str] = Field(None, alias="sentTime")
    internet_message_id: Optional[str] = Field(None, alias="internetMessageId")
    remediation_status: Optional[str] = Field(None, alias="remediationStatus")
    attack_type: Optional[str] = Field(None, alias="attackType")
    attack_party: Optional[str] = Field(None, alias="attackParty")
    attack_strategy: Optional[str] = Field(None, alias="attackStrategy")
    return_path: Optional[str] = Field(None, alias="returnPath")
    reply_to_emails: Optional[List[str]] = Field(None, alias="replyToEmails")
    cc_emails: Optional[List[str]] = Field(None, alias="ccEmails")
    sender_ip_address: Optional[str] = Field(None, alias="senderIpAddress")
    impersonated_party: Optional[str] = Field(None, alias="impersonatedParty")
    attack_vector: Optional[str] = Field(None, alias="attackVector")
    attachment_names: Optional[List[str]] = Field(None, alias="attachmentNames")
    attachment_count: Optional[int] = Field(None, alias="attachmentCount")
    urls: Optional[List[str]] = None
    url_count: Optional[int] = Field(None, alias="urlCount")
    summary_insights: Optional[List[str]] = Field(None, alias="summaryInsights")
    remediation_timestamp: Optional[str] = Field(None, alias="remediationTimestamp")
    is_read: Optional[bool] = Field(None, alias="isRead")
    attacked_party: Optional[str] = Field(None, alias="attackedParty")
    auto_remediated: Optional[bool] = Field(None, alias="autoRemediated")
    post_remediated: Optional[bool] = Field(None, alias="postRemediated")
