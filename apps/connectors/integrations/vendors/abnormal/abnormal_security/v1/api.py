from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, *args, params=None):
    response = bound_method(*args, params=params)
    yield response
    while next_page := response.get("nextPageNumber"):
        params["pageNumber"] = next_page
        response = bound_method(*args, params=params)
        yield response


class AbnormalSecurityV1Api(ApiBase):
    """
    Abnormal Security API
    swagger: https://app.swaggerhub.com/apis/abnormal-security/abx/1.4.2#/
    url: https://api.abnormalsecurity.com/v1/
    """

    def __init__(self, url=None, token=None, **kwargs):
        static_headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
        }
        super().__init__(static_headers=static_headers, base_url=url)

    def get_threats(self, params=None):
        url = self.url(f"threats")
        return self.session.get(url, params=params).json()

    def get_threat(self, threat_id, params=None):
        url = self.url(f"threats/{threat_id}")
        return self.session.get(url, params=params).json()

    def remediate_threat(self, threat_id, data, params=None):
        url = self.url(f"threats/{threat_id}")
        return self.session.post(url, data=data, params=params).json()

    def get_threat_attachments(self, threat_id, params=None):
        url = self.url(f"threats/{threat_id}/attachments")
        return self.session.get(url, params=params).json()

    def get_message_attachment_details(self, message_id, attachment_name, params=None):
        url = self.url(f"messages/{message_id}/attachment/{attachment_name}")
        data = self.session.get(url, params=params).json()["data"]
        # The API docs are not clear on the response structure
        # According to the API docs, the response should a single MessageAttachmentDetails
        # But the API returns a list of MessageAttachmentDetails under the key "data"
        # So, we are returning the first element of the list, because there should
        # be only one element in the list.
        return data[0]

    def get_urls_by_threat(self, threat_id, params=None):
        url = self.url(f"threats/{threat_id}/links")
        return self.session.get(url, params=params).json()
