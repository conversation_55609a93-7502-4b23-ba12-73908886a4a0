from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_latest_time_remediated():
    dt = datetime.now(timezone.utc) - timedelta(days=1)
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


class AbnormalSecurityV1EventSyncBookmark(TemplateVersionActionBookmark):
    latest_time_remediated: str = Field(
        title="Latest Event Update Datetime",
        description="The latest TimeRemediated received during a fetch.",
        default_factory=default_latest_time_remediated,
    )


AbnormalSecurityV1Bookmarks = create_bookmarks_model(
    "AbnormalSecurityV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: AbnormalSecurityV1EventSyncBookmark,
    },
)
