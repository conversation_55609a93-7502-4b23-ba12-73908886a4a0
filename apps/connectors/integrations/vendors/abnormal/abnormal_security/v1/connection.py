from enum import StrEnum

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class AbnormalSecurityV1Url(StrEnum):
    STANDARD = "https://api.abnormalsecurity.com/v1/"
    TESTING = "https://api.abnormalplatform.com/v1/"


class AbnormalSecurityV1Config(TemplateVersionConfig):
    url: AbnormalSecurityV1Url = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate Abnormal Security.",
    )

    token: EncryptedStr = Field(
        title="API Access Key",
        description="API Key used to authenticate with Abnormal Security.",
    )


class AbnormalSecurityV1Connection(ConnectionTemplate):
    id = "abnormal_security"
    name = "Abnormal Security"
    config_model = AbnormalSecurityV1Config
