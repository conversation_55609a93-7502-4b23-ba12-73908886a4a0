from apps.connectors.integrations import Integration
from apps.connectors.integrations.actions.email_threat import (
    EmailThreatInfo,
    EmailThreatRemediationStatus,
    MailMessage,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
)
from apps.connectors.integrations.vendors.abnormal.abnormal_security.v1.actions.event_sync import (
    AbnormalSecurityV1EventSync,
)

from .actions.get_attachment_details_by_email_threat import (
    AbnormalSecurityV1GetAttachmentDetailsByEmailThreat,
)
from .actions.get_email_threat import AbnormalSecurityV1GetEmailThreat
from .actions.get_urls_by_email_threat import AbnormalSecurityV1GetUrlsByEmailThreat
from .actions.remediate_email_threat import AbnormalSecurityV1RemediateEmailThreat
from .actions.unremediate_email_threat import AbnormalSecurityV1UnRemediateEmailThreat
from .api import AbnormalSecurityV1Api
from .health_check import ConnectionHealthCheck


class AbnormalSecurityV1Integration(Integration):
    api_class = AbnormalSecurityV1Api
    actions = (
        AbnormalSecurityV1EventSync,
        AbnormalSecurityV1GetEmailThreat,
        AbnormalSecurityV1RemediateEmailThreat,
        AbnormalSecurityV1UnRemediateEmailThreat,
        AbnormalSecurityV1GetAttachmentDetailsByEmailThreat,
        AbnormalSecurityV1GetUrlsByEmailThreat,
    )
    critical_health_checks = (ConnectionHealthCheck,)

    def _change_remediation_to(
        self,
        args: EmailThreatIdentifierArgs,
        status: EmailThreatRemediationStatus,
    ):
        api = self.api_class(**self.config)

        threat = api.get_threat(args.email_threat_id.value)
        threat_info = self._to_email_threat_info(threat)

        # If the threat is already unremediated or in progress,
        # return remediation result.
        if threat_info.remediation_status in [
            status,
            EmailThreatRemediationStatus.IN_PROGRESS,
        ]:
            return threat_info

        if status == EmailThreatRemediationStatus.UNREMEDIATED:
            data = {"action": "unremediate"}
        else:
            data = {"action": "remediate"}

        response = api.remediate_threat(  # noqa: F841
            args.email_threat_id.value, data=data
        )
        # TODO: Handle polling with action_id = response["action_id"],
        threat_info.remediation_status = EmailThreatRemediationStatus.IN_PROGRESS
        return threat_info

    def _to_email_threat_info(self, response) -> EmailThreatInfo:
        """
        Convert the response from the Abnormal Security API to an EmailThreatInfo object.
        """
        mail_messages = [
            MailMessage(
                source_id=message["abxMessageIdStr"],
                technology_id="abnormal_security",
                internet_message_id=message["internetMessageId"],
                subject=message["subject"],
                sender_address=message["fromAddress"],
                recipient_addresses=message["toAddresses"],
                source_remediation_status=message["remediationStatus"],
            )
            for message in response["messages"]
        ]

        # Abnormal supports many remediation statuses, but we only care about a few.
        remediation_status_mapping = {
            "Auto-Remediated": EmailThreatRemediationStatus.REMEDIATED,
            "Post-Remediated": EmailThreatRemediationStatus.REMEDIATED,
            "Remediated": EmailThreatRemediationStatus.REMEDIATED,
            "Remediation Triggered": EmailThreatRemediationStatus.IN_PROGRESS,
            "Would Remediate": EmailThreatRemediationStatus.UNREMEDIATED,
            "Marked Safe": EmailThreatRemediationStatus.UNREMEDIATED,
        }

        # Resolve remediation status of the threat based on the remediation status each of
        # the messages related to this threat. Consider threat as remediated only if all the
        # messages are remediated.
        remediation_status = EmailThreatRemediationStatus.NONE
        for message in mail_messages:
            # Abnormal supports other remediation status such as Email Moved, Qurantined, etc.
            # Instead of making an assumption whether the threat is remediated, use None as default.
            # Consider message as remediated only if the remediation status clearly indicates so.
            # It doesn't make sense to map all the statuses to the EmailThreatRemediationStatus enum.
            # Instead, provide source remediation status in the source_remediation_status field and let
            # the user decide how to handle it.
            remediation_status = remediation_status_mapping.get(
                message.source_remediation_status,
                EmailThreatRemediationStatus.NONE,
            )
            if remediation_status != EmailThreatRemediationStatus.REMEDIATED:
                break

        return EmailThreatInfo(
            technology_id="abnormal_security",
            source_id=response["threatId"],
            messages=mail_messages,
            remediation_status=remediation_status,
        )
