from apps.connectors.integrations.actions.email_threat import (
    GetUrlsByEmailThreat,
    GetUrlsByEmailThreatResult,
    ThreatLink,
)
from apps.connectors.integrations.schemas import EmailThreatIdentifierArgs, ocsf


def normalize_url(data: dict) -> ocsf.Url:
    return ocsf.Url(
        domain=data.get("domainLink"),
        resource_type=data.get("linkType"),
        url_string=data.get("linkUrl"),
    )


class AbnormalSecurityV1GetUrlsByEmailThreat(GetUrlsByEmailThreat):
    def execute(self, args: EmailThreatIdentifierArgs) -> GetUrlsByEmailThreatResult:
        api = self.integration.get_api()

        urls_response = api.get_urls_by_threat(args.email_threat_id.value)

        links = []
        urls = urls_response["links"]
        for url in urls:
            links.append(
                ThreatLink(source_id=url["abxMessageIdStr"], url=normalize_url(url))
            )

        return GetUrlsByEmailThreatResult(result=links)

    def get_permission_checks(self, *args, **kwargs):
        return []
