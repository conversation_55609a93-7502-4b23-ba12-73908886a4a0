from apps.connectors.integrations.actions.email_threat import (
    EmailThreatResult,
    GetEmailThreat,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
)


class AbnormalSecurityV1GetEmailThreat(GetEmailThreat):
    def execute(self, args: EmailThreatIdentifierArgs) -> EmailThreatResult:
        api = self.integration.get_api()
        response = api.get_threat(threat_id=args.email_threat_id.value)

        return EmailThreatResult(
            result=self.integration._to_email_threat_info(response)
        )

    def get_permission_checks(self):
        return []
