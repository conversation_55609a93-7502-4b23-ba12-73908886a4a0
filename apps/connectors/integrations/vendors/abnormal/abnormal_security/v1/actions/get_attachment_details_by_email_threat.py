from apps.connectors.integrations.actions.email_threat import (
    GetAttachmentDetailsByEmailThreat,
    GetAttachmentDetailsByEmailThreatResult,
)
from apps.connectors.integrations.schemas.identifiers import EmailThreatIdentifierArgs
from apps.connectors.integrations.vendors.abnormal.abnormal_security.v1.actions.file import (
    normalize_file,
)


class AbnormalSecurityV1GetAttachmentDetailsByEmailThreat(
    GetAttachmentDetailsByEmailThreat
):
    def execute(
        self, args: EmailThreatIdentifierArgs
    ) -> GetAttachmentDetailsByEmailThreatResult:
        api = self.integration.get_api()

        attachments_response = api.get_threat_attachments(args.email_threat_id.value)

        files = []
        attachments = attachments_response["attachments"]
        for attachment in attachments:
            details = api.get_message_attachment_details(
                attachment["abxMessageIdStr"], attachment["attachmentName"]
            )
            files.append(normalize_file(details))

        return GetAttachmentDetailsByEmailThreatResult(result=files)

    def get_permission_checks(self, *args, **kwargs):
        return []
