from apps.connectors.integrations.actions.email_threat import (
    EmailThreatRemediationResult,
    EmailThreatRemediationStatus,
    RemediateEmailThreat,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
    IntegrationActionPollingContext,
)


class AbnormalSecurityV1RemediateEmailThreat(RemediateEmailThreat):
    def execute(self, args: EmailThreatIdentifierArgs) -> EmailThreatRemediationResult:
        return EmailThreatRemediationResult(
            result=self.integration._change_remediation_to(
                args, EmailThreatRemediationStatus.REMEDIATED
            )
        )

    def poll(self, poll_context: IntegrationActionPollingContext):  # pragma: no cover
        # TODO: Implement polling logic
        return IntegrationActionPollingContext(context={})

    def get_permission_checks(self):
        return []
