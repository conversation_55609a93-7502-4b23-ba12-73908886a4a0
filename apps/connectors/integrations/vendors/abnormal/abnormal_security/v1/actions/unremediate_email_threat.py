from apps.connectors.integrations.actions.email_threat import (
    EmailThreatRemediationResult,
    EmailThreatRemediationStatus,
    UnRemediateEmailThreat,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
    IntegrationActionPollingContext,
)


class AbnormalSecurityV1UnRemediateEmailThreat(UnRemediateEmailThreat):
    def execute(self, args: EmailThreatIdentifierArgs) -> EmailThreatRemediationResult:
        return EmailThreatRemediationResult(
            result=self.integration._change_remediation_to(
                args, EmailThreatRemediationStatus.UNREMEDIATED
            )
        )

    def poll(self, poll_context: IntegrationActionPollingContext):  # pragma: no cover
        raise NotImplementedError(f"poll not implemented for {self.__class__.__name__}")

    def get_permission_checks(self):
        return []
