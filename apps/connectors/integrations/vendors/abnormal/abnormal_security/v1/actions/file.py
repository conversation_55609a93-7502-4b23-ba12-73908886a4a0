import re

from apps.connectors.integrations.schemas.ocsf import (
    File,
    FileType,
    Fingerprint,
    HashAlgorithm,
)


def convert_to_bytes(size_str: str) -> int | None:
    if not size_str:
        return None

    if size_str.isdigit():
        return int(size_str)

    units = {"B": 1, "KB": 1024, "MB": 1024**2, "GB": 1024**3, "TB": 1024**4}
    match = re.match(
        r"([\d.]+)\s*([KMGT]?B)", size_str.upper()
    )  # Extract number and unit
    if not match:
        return None

    size, unit = match.groups()
    return int(float(size) * units[unit])


def normalize_file(attachment_data: dict) -> File:
    """
    Normalize the MessageAttachmentDetails to OCSFFile
    :param attachment_data: MessageAttachmentDetails
    :return: OCSFFile
    """
    name = attachment_data.get("attachmentName", "")
    hashes = []
    if md5_value := attachment_data.get("md5", ""):
        md5 = Fingerprint(value=md5_value, algorithm=HashAlgorithm.MD5)
        hashes.append(md5)
    if sha1_value := attachment_data.get("sha1", ""):
        sha1 = Fingerprint(value=sha1_value, algorithm=HashAlgorithm.SHA1)
        hashes.append(sha1)
    if sha256_value := attachment_data.get("sha256", ""):
        sha256 = Fingerprint(value=sha256_value, algorithm=HashAlgorithm.SHA256)
        hashes.append(sha256)
    size = convert_to_bytes(attachment_data.get("size", ""))
    created_time_str = attachment_data.get("createdOn")
    modified_time_str = attachment_data.get("lastUpdated")
    return File(
        name=name,
        hashes=hashes,
        size=size,
        created_time_dt=created_time_str,
        modified_time_dt=modified_time_str,
        # OCSF File requires FileType enum, so we set it to REGULAR_FILE
        type=FileType.REGULAR_FILE,
    )
