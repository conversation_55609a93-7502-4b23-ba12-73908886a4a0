from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.vendors.sevco.sevco_io.v1.api import paginate
from apps.connectors.integrations.vendors.sevco.sevco_io.v1.health_check import (
    ReadAllHosts,
)


def normalize_host(host_data: dict):
    # Logics goes here
    users = host_data.get("attributes", {}).get("recent_users", [])
    owners = []
    for user in users:
        owners.append(
            OwnerAttributes(
                name=user.get("attributes", {}).get("full_name", ""),
                email=user.get("attributes", {}).get("emails", [])[0],
            )
        )

    return Host(
        source_id=host_data.get("id", ""),
        group_names=host_data.get("attributes", {}).get("groups", []),
        hostname=host_data.get("attributes", {}).get("hostnames", [""])[0],
        fqdns=[],
        ip_addresses=host_data.get("attributes", {}).get("ips", []),
        mac_addresses=host_data.get("attributes", {}).get("mac_addresses", []),
        _os_name=host_data.get("attributes", {}).get("os", ""),  # OsAttributes
        owners=owners,  # List[OwnerAttributes]
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(
            host_data.get("last_observed_timestamp")
        ),  # only works for iso date string or list of iso date strings
        source_data=host_data,
    )


class SevcoIoV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_unified_devices, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadAllHosts]
