from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.sevco.sevco_io.v1.actions.host_sync import (
    SevcoIoV1HostSync,
)

from .api import SevcoIoV1Api
from .health_check import ConnectionHealthCheck


class SevcoIoV1Integration(Integration):
    api_class = SevcoIoV1Api
    actions = (SevcoIoV1HostSync,)
    critical_health_checks = (ConnectionHealthCheck,)
