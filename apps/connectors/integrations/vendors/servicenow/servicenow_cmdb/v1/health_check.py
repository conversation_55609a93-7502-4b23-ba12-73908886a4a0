from requests.exceptions import HTTPError as HttpError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            api.get_table(sysparm_limit=1)
            return IntegrationHealthCheckResult.PASSED
        except HttpError:
            return IntegrationHealthCheckResult.FAILED


class ReadDeviceInventory(IntegrationPermissionsHealthCheck):
    name = "Read devices inventory"
    description = "Read devices inventory from Service Now CMDB."
    value = "devices_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke("get_table", sysparm_limit=1)
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
