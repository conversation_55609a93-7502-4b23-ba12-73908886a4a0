from typing import Generator

from apps.connectors.integrations.actions import (
    normalize,
    normalize_last_seen,
    parse_fqdn,
)
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    OwnerAttributes,
)
from apps.connectors.integrations.schemas import HostType, OsAttributes, OsFamily
from apps.connectors.integrations.vendors.servicenow.servicenow_cmdb.v1.api import (
    paginate,
)
from apps.connectors.integrations.vendors.servicenow.servicenow_cmdb.v1.health_check import (
    ReadDeviceInventory,
)


def normalize_host(host_data: dict):
    os_name = host_data.get("os")
    host_type = HostType.from_os_name(os_name)
    os_family, _ = OsFamily.from_string(os_name)
    hostname, _ = parse_fqdn(host_data.get("fqdn") or "")
    fqdns = [host_data.get("fqdn")]
    owners = [OwnerAttributes(name=host_data.get("owned_by"), email=None)]
    ip_addresses = [host_data.get("ip_address")]
    mac_addresses = [host_data.get("mac_address")]

    return Host(
        source_id=host_data.get("sys_class_name") + "/" + host_data.get("sys_id"),
        group_names=[],
        hostname=hostname,
        fqdns=fqdns,
        ip_addresses=ip_addresses,
        mac_addresses=mac_addresses,
        os=OsAttributes(host_type=host_type, family=os_family, name=os_name),
        owners=owners,
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("sys_updated_on")),
        source_data=host_data,
    )


class ServicenowCmdbV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        for page in paginate(api.get_table, **kwargs):
            yield from page

    def get_permission_checks(self):
        return [ReadDeviceInventory]
