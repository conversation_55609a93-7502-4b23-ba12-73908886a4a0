from apps.connectors.integrations import ApiBase

"""
tried

GraphQL
postman
B<PERSON><PERSON> Console Logs

Searched Documentation
Did some course on GraphQL in ServiceNow
Did Trails on GraphQL Explorer in ServiceNow

But Could not find anyway to get the data in bulk ,which we are seeing in the browser

Through API the Only way to get data is sending request to each serviceId to get details of that service instance

"""
list_of_instances = [
    "cmdb_ci_apache_web_server",
    "cmdb_ci_application_cluster",
    "cmdb_ci_app_server",
    "cmdb_ci_app_server_java",
    "cmdb_ci_app_server_jboss",
    "cmdb_ci_app_server_jb_module",
    "cmdb_ci_app_server_jrun",
    "cmdb_ci_app_server_jrun_war",
    "cmdb_ci_app_server_ora_ess",
    "cmdb_ci_app_server_ora_ias",
    "cmdb_ci_app_server_ora_ias_m",
    "cmdb_ci_app_server_remedy",
    "cmdb_ci_app_server_tomcat",
    "cmdb_ci_app_server_tomcat_war",
    "cmdb_ci_app_server_vendavo",
    "cmdb_ci_app_server_weblogic",
    "cmdb_ci_app_server_webseal",
    "cmdb_ci_app_server_websphere",
    "cmdb_ci_app_server_wl_module",
    "cmdb_ci_app_server_ws_ear",
    "cmdb_ci_app_server_ws_odr",
    "cmdb_ci_cim_server",
    "cmdb_ci_config_automation_server",
    "cmdb_ci_datapower_server",
    "cmdb_ci_db_mssql_server",
    "cmdb_ci_dir_policy_server",
    "cmdb_ci_dir_site_minder_server",
    "cmdb_ci_directory_server",
    "cmdb_ci_email_server",
    "cmdb_ci_email_server_jes",
    "cmdb_ci_esx_server",
    "cmdb_ci_exchange_edge_transport_server",
    "cmdb_ci_exchange_hub_transport_server",
    "cmdb_ci_exchange_mailbox_server",
    "cmdb_ci_ftp_server",
    "cmdb_ci_hpux_server",
    "cmdb_ci_hyper_v_server",
    "cmdb_ci_iplanet_web_server",
    "cmdb_ci_isam_server",
    "cmdb_ci_lb_backend_server",
    "cmdb_ci_linux_server",
    "cmdb_ci_microsoft_iis_web_server",
    "cmdb_ci_net_app_server",
    "cmdb_ci_netware_server",
    "cmdb_ci_nginx_web_server",
    "cmdb_ci_osx_server",
    "cmdb_ci_server_snapshot",
    "cmdb_ci_solaris_server",
    "cmdb_ci_sun_dir_proxy_server",
    "cmdb_ci_sun_ldap_dir_server",
    "cmdb_ci_unix_server",
    "cmdb_ci_web_server",
    "cmdb_ci_win_server",
]


def paginate(bound_method, **kwargs):
    for instance_name in list_of_instances:
        response = bound_method(**kwargs, instance_name=instance_name)
        yield response["result"]
        offset = len(response["result"])
        while True:
            response = bound_method(
                **kwargs, sysparm_offset=offset, instance_name=instance_name
            )
            items_count = len(response["result"])
            offset = offset + items_count
            if items_count > 0:
                yield response["result"]
            else:
                break


class ServicenowCmdbV1Api(ApiBase):
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        self.username = username
        self.password = password
        super().__init__(
            base_url=self.base_url, static_headers={"Accept": "application/json"}
        )
        self.session.auth = (self.username, self.password)

    def get_table(
        self, sysparm_limit=10000, sysparm_offset=0, instance_name="cmdb_ci_win_server"
    ):
        result = self.session.get(
            self.url("api/now/table/" + instance_name),
            params={"sysparm_limit": sysparm_limit, "sysparm_offset": sysparm_offset},
        )
        return result.json()
