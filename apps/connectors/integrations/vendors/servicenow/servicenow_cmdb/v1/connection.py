from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class ServicenowCmdbV1Config(TemplateVersionConfig):
    base_url: HttpUrl = Field(
        title="ServiceNow CMDB Base URL",
        description="The base URL of the ServiceNow CMDB API.",
    )
    username: str = Field(
        title="Username",
        description="The username to authenticate with the ServiceNow CMDB API.",
    )
    password: EncryptedStr = Field(
        title="Password",
        description="The password to authenticate with the ServiceNow CMDB API.",
    )


class ServicenowCmdbV1Connection(ConnectionTemplate):
    id = "servicenow_cmdb"
    name = "ServiceNow CMDB"
    config_model = ServicenowCmdbV1Config
