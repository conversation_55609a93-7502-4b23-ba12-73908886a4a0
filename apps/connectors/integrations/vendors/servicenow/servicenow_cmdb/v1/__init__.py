from apps.connectors.integrations import TemplateVersion

from .connection import ServicenowCmdbV1Config, ServicenowCmdbV1Connection
from .integration import ServicenowCmdbV1Integration
from .settings import ServicenowCmdbV1Settings


class ServicenowCmdbV1TemplateVersion(TemplateVersion):
    integration = ServicenowCmdbV1Integration
    id = "v1"
    name = "v1"
    config_model = ServicenowCmdbV1Config
    connection_model = ServicenowCmdbV1Connection
    settings_model = ServicenowCmdbV1Settings
