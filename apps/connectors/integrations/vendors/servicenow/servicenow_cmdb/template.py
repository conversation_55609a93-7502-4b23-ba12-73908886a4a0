from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ServicenowCmdbV1TemplateVersion


class ServicenowCmdbTemplate(Template):
    id = "servicenow_cmdb"
    name = "ServiceNow CMDB"
    category = Template.Category.ASSET_SOURCE
    versions = {
        ServicenowCmdbV1TemplateVersion.id: ServicenowCmdbV1TemplateVersion(),
    }
    vendor = Vendors.SERVICENOW
