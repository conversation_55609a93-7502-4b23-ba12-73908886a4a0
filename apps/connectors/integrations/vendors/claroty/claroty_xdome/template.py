from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ClarotyXdomeV1TemplateVersion


class ClarotyXdomeTemplate(Template):
    id = "claroty_xdome"
    name = "Claroty xDome"
    category = Template.Category.OT_SECURITY
    vendor = Vendors.CLAROTY
    versions = {
        ClarotyXdomeV1TemplateVersion.id: ClarotyXdomeV1TemplateVersion(),
    }
