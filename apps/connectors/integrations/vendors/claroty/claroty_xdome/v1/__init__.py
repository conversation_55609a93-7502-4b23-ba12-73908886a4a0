from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.bookmarks import (
    ClarotyXdomeSyncBookmarks,
)

from .bookmarks import ClarotyXdomeSyncBookmarks
from .connection import ClarotyXdomeV1Config, ClarotyXdomeV1Connection
from .integration import ClarotyXdomeV1Integration
from .settings import ClarotyXdomeV1Settings


class ClarotyXdomeV1TemplateVersion(TemplateVersion):
    integration = ClarotyXdomeV1Integration
    id = "v1"
    name = "v1"
    config_model = ClarotyXdomeV1Config
    connection_model = ClarotyXdomeV1Connection
    settings_model = ClarotyXdomeV1Settings
    bookmarks_model = ClarotyXdomeSyncBookmarks
