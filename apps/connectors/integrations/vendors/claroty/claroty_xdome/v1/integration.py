from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.actions.event_sync import (
    ClarotyXdomeV1EventSync,
)
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.actions.update_lifecycle_status import (
    ClarotyXdomeV1UpdateLifecycleStatus,
)

from .actions.event_sync import ClarotyXdomeV1EventSync
from .actions.update_lifecycle_status import ClarotyXdomeV1UpdateLifecycleStatus
from .api import ClarotyXdomeV1Api


class ClarotyXdomeV1Integration(Integration):
    api_class = ClarotyXdomeV1Api
    actions = (ClarotyXdomeV1EventSync, ClarotyXdomeV1UpdateLifecycleStatus)
    critical_health_checks = ()
