from datetime import datetime, timezone

from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadAllAlerts(IntegrationPermissionsHealthCheck):
    name = "Read Alerts"
    description = "Read Alerts from Claroty xDome"
    value = "read:alerts"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke(
                "get_alerts",
                limit=1,
                updated_time=datetime.now(timezone.utc)
                .isoformat()
                .replace("+00:00", "Z"),
            )
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
