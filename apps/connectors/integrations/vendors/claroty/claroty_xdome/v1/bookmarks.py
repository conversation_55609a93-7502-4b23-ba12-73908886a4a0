from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_event_datetime():
    return (
        (datetime.now(timezone.utc) - timedelta(days=1))
        .isoformat()
        .replace("+00:00", "Z")
    )


class ClarotyXdomeSyncBookmark(TemplateVersionActionBookmark):
    updated_time: str = Field(
        title="Updated Time Datetime",
        description="The Updated Time Datetime fetched.",
        default_factory=default_event_datetime,
    )


ClarotyXdomeSyncBookmarks = create_bookmarks_model(
    "ClarotyXdomeSyncBookmarks",
    {
        IntegrationActionType.EVENT_SYNC: ClarotyXdomeSyncBookmark,
    },
)
