from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class ClarotyXdomeV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="URL",
        description="The URL of the Claroty xDome API endpoint.",
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="The API key for authenticating with the Claroty xDome API.",
    )


class ClarotyXdomeV1Connection(ConnectionTemplate):
    id = "claroty_xdome"
    name = "Claroty xDome"
    config_model = ClarotyXdomeV1Config
