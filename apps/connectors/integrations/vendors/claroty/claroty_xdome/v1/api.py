from apps.connectors.integrations.api import ApiBase

alert_columns = [
    "id",
    "updated_time",
    "alert_name",
    "category",
    "description",
    "alert_type_name",
    "detected_time",
    "status",
]

device_columns = [
    "uid",
    "model",
    "os_category",
    "serial_number",
    "hw_version",
    "manufacturer",
    "vlan_list",
    "endpoint_security_names",
    "ad_description",
    "mdm_ownership",
    "domains",
    "machine_type",
    "last_seen_list",
    "first_seen_list",
    "device_name",
    "risk_score",
    "risk_score_points",
    "device_category",
    "device_subcategory",
    "ip_list",
    "mac_list",
    "windows_last_seen_hostname",
    "snmp_last_seen_hostname",
    "http_last_seen_hostname",
    "dhcp_last_seen_hostname",
    "windows_hostnames",
    "dhcp_hostnames",
    "snmp_hostnames",
    "http_hostnames",
    "other_hostnames",
    "ad_description",
]


def paginate(bound_method, response_key="alerts", **kwargs):
    response = bound_method(**kwargs)
    results = response[response_key]
    items_count = len(results)
    offset = 0
    while items_count > 0:
        yield from results
        offset += items_count
        response = bound_method(offset=offset, **kwargs)
        results = response[response_key]
        items_count = len(results)


class ClarotyXdomeV1Api(ApiBase):
    def __init__(self, url=None, api_key=None, **kwargs):
        self.base_url = url
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        super().__init__(base_url=self.base_url, static_headers=self.headers, **kwargs)

    def get_alerts(self, updated_time: str, offset=0, limit=5000):
        """
        Get alerts from the Claroty Xdome API.
        """
        url = f"{self.base_url}/api/v1/alerts"
        params = {
            "filter_by": {
                "field": "updated_time",
                "operation": "greater_or_equal",
                "value": updated_time,
            },
            "offset": offset,
            "limit": limit,
            "fields": alert_columns,
        }
        url = self.url(f"/api/v1/alerts")
        return self.session.post(url, json=params).json()

    def get_affected_devices(self, alert_id: str, offset=0, limit=5000):
        """
        Get affected devices for a specific alert.
        """
        url = self.url(f"/api/v1/alerts/{alert_id}/devices")
        params = {"offset": offset, "limit": limit, "fields": device_columns}
        return self.session.post(url, json=params).json()

    def set_alert_status(self, alerts: list[str], devices: list[str], status: str):
        """
        Set the status of an alert.
        """
        url = self.url("/api/v1/device-alert-status/set")
        params = {
            "status": status,
            "alerts": alerts,
            "devices": devices,
        }
        return self.session.post(url, json=params).json()
