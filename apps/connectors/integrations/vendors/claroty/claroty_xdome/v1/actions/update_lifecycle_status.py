from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.api import (
    ClarotyXdomeV1Api,
)
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.health_check import (
    ReadAllAlerts,
)
from apps.connectors.utils import compound_id


def map_corr_incident_status(status: CorrIncidentStatus) -> str:
    return {
        CorrIncidentStatus.NEW: "unresolved",
        CorrIncidentStatus.ASSIGNED: "resolved",
        CorrIncidentStatus.REVIEWING: "resolved",
        CorrIncidentStatus.MITIGATED: "resolved",
        CorrIncidentStatus.CLOSED: "resolved",
    }[status]


class ClarotyXdomeV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: ClarotyXdomeV1Api = self.integration.get_api()
        alert_id, device_id = compound_id.split(args.vendor_sync_id)
        api.set_alert_status(
            [alert_id], [device_id], map_corr_incident_status(args.status)
        )
        return UpdateLifecycleStatusResult()

    def get_permission_checks(self, *args, **kwargs):
        return [ReadAllAlerts]
