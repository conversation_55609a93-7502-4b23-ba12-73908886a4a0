from datetime import datetime
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRef,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.api import (
    ClarotyXdomeV1Api,
    paginate,
)
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.bookmarks import (
    ClarotyXdomeSyncBookmark,
)
from apps.connectors.integrations.vendors.claroty.claroty_xdome.v1.health_check import (
    ReadAllAlerts,
)
from apps.connectors.utils import compound_id


def parse_datetime(dt_str: str) -> datetime | None:
    if not dt_str:
        return None
    return parser.parse(dt_str)


def get_iso_date(dt_str: str) -> datetime:
    return parser.parse(dt_str).isoformat()


def get_status(status: str) -> str:
    return (
        ocsf.DetectionStatus.NEW
        if status == "Unresolved"
        else ocsf.DetectionStatus.RESOLVED
    )


def get_device_type(device: dict) -> ocsf.EndpointType:
    if device.get("device_category") == "IoT":
        return ocsf.EndpointType.IOT
    elif device.get("device_category") == "IT":
        if device.get("device_subcategory") == "Computers":
            return ocsf.EndpointType.DESKTOP
        elif device.get("device_subcategory") == "Servers":
            return ocsf.EndpointType.SERVER
    return ocsf.EndpointType.UNKNOWN


def get_risk_level(risk_level: str) -> ocsf.RiskLevel:
    if risk_level == "Very Low":
        return ocsf.RiskLevel.INFO
    elif risk_level == "Low":
        return ocsf.RiskLevel.LOW
    elif risk_level == "Medium":
        return ocsf.RiskLevel.MEDIUM
    elif risk_level == "High":
        return ocsf.RiskLevel.HIGH
    else:
        return ocsf.RiskLevel.CRITICAL


def convert_to_ocsf(event: dict, uid: str, title: str) -> ocsf.DetectionFinding:
    alert = event["alert"]
    device = event["device"]
    network_interfaces = []
    for mac in device.get("mac_list", []):
        network_interfaces.append(ocsf.NetworkInterface(mac=mac))
    for ip in device.get("ip_list", []):
        network_interfaces.append(ocsf.NetworkInterface(ip=ip))
    agent_list = [
        ocsf.Agent(name=agent_name, type=ocsf.AgentType.ENDPOINT_DETECTION_AND_RESPONSE)
        for agent_name in device.get("endpoint_security_names", [])
    ]
    if device.get("mdm_ownership"):  # pragma: no cover
        agent_list.append(ocsf.Agent(type=ocsf.AgentType.MOBILE_DEVICE_MANAGEMENT))

    hostname = (
        device.get("windows_last_seen_hostname")
        or device.get("snmp_last_seen_hostname")
        or device.get("http_last_seen_hostname")
        or device.get("dhcp_last_seen_hostname")
        or (device.get("windows_hostnames") or [None])[0]
        or (device.get("dhcp_hostnames") or [None])[0]
        or (device.get("snmp_hostnames") or [None])[0]
        or (device.get("http_hostnames") or [None])[0]
        or (device.get("other_hostnames") or [None])[0]
    )

    return ocsf.DetectionFinding(
        activity=ocsf.DetectionActivity.CREATE
        if alert.get("status", "Unresolved") == "Unresolved"
        else ocsf.DetectionActivity.CLOSE,
        metadata=ocsf.Metadata(
            correlation_uid=str(alert.get("id")),
            modified_time_dt=parse_datetime(alert.get("updated_time")),
            uid=uid,
            profiles=[ocsf.Profile.DATETIME, ocsf.Profile.HOST],
            event_code=alert.get("alert_type_name"),
        ),
        finding_info=ocsf.FindingInformation(
            uid=uid,
            title=title,
            modified_time_dt=parse_datetime(alert.get("updated_time")),
            desc=alert.get("description"),
        ),
        message=title,
        time_dt=parse_datetime(alert.get("detected_time")),
        status=get_status(alert.get("status")),
        device=ocsf.Device(
            type=get_device_type(device),
            uid=device.get("uid"),
            network_interfaces=network_interfaces,
            model=device.get("model"),
            os=ocsf.OperatingSystem(
                name=device.get("os_category"),
            ),
            hw_info=ocsf.DeviceHwInfo(
                serial_number=device.get("serial_number"),
                bios_ver=device.get("hw_version"),
                vendor_name=device.get("manufacturer"),
            ),
            vlan_uid=str(device.get("vlan_list")[0])
            if device.get("vlan_list")
            else None,
            agent_list=agent_list or None,
            desc=device.get("ad_description"),
            is_managed=True if device.get("mdm_ownership") else False,
            hostname=hostname,
            domain=device.get("domains")[0]
            if len(device.get("domains", [])) > 0
            else None,
            last_seen_time_dt=parse_datetime(
                (device.get("last_seen_list") or [None])[0]
            ),
            first_seen_time_dt=parse_datetime(
                (device.get("first_seen_list") or [None])[0]
            ),
            name=device.get("device_name"),
            risk_level=get_risk_level(device.get("risk_score")),
            risk_score=int(device.get("risk_score_points")),
        ),
    )


def normalize_event(event: dict) -> Event:
    alert = event["alert"]
    device = event["device"]
    uid = compound_id.combine(str(alert["id"]), str(device["uid"]))
    title = f"{alert.get('alert_name')} on {device.get('device_name')}"
    return Event(
        event_timestamp=get_iso_date(event.get("alert").get("updated_time")),
        raw_event=event,
        ocsf=convert_to_ocsf(event, uid, title),
        vendor_item_ref=VendorRef(
            id=uid,
            title=title,
        ),
        vendor_group_ref=VendorRef(
            id=str(alert["id"]),
            title=alert.get("alert_name"),
        ),
        ioc=EventIOCInfo(
            external_id=alert.get("alert_name", "Unknown"),
            external_name=alert.get("alert_name", "Unknown"),
            has_ioc_definition=False,
            mitre_techniques=alert.get("mitre_technique_enterprise_ids", [])
            + alert.get("mitre_technique_ics_ids", []),
        ),
    )


class ClarotyXdomeV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: ClarotyXdomeSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: ClarotyXdomeV1Api = self.integration.get_api()
        alerts = paginate(api.get_alerts, updated_time=bookmark.updated_time)
        for alert in alerts:
            devices = paginate(
                api.get_affected_devices,
                alert_id=alert.get("id"),
                response_key="devices",
            )
            for device in devices:
                yield {
                    "alert": alert,
                    "device": device,
                }
            log_time = alert.get("updated_time")
            if log_time > bookmark.updated_time:
                bookmark.updated_time = log_time

    def get_permission_checks(self):
        return [ReadAllAlerts]
