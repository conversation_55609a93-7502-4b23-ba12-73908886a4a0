from apps.connectors.integrations import TemplateVersion

from .connection import ImportHostsV1Config, ImportHostsV1Connection
from .integration import ImportHostsV1Integration
from .settings import ImportHostsV1Settings


class ImportHostsV1TemplateVersion(TemplateVersion):
    integration = ImportHostsV1Integration
    id = "v1"
    name = "v1"
    config_model = ImportHostsV1Config
    connection_model = ImportHostsV1Connection
    settings_model = ImportHostsV1Settings
