from apps.connectors.integrations import IntegrationError
from apps.connectors.integrations.actions import normalize_last_seen
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
)
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.utils import split_cs


def normalize_host(host_data: dict):
    owners = []
    if "owner_name" in host_data:
        owners.append(
            {
                "name": host_data.get("owner_name", ""),
                "email": host_data.get("owner_email", ""),
            }
        )
    return Host(
        source_id=host_data.get("source_id", ""),
        group_names=split_cs(host_data.get("group_names", "")),
        hostname=host_data.get("hostname", ""),
        fqdns=split_cs(host_data.get("fqdns", "")),
        ip_addresses=split_cs(host_data.get("ip_addresses", "")),
        mac_addresses=split_cs(host_data.get("mac_addresses", "")),
        os={
            "family": host_data.get("os_family", ""),
            "name": host_data.get("os_name", ""),
            "host_type": host_data.get("os_host_type", ""),
        },  # Set as dict to prevent short-circuiting of model validation
        owners=owners,  # List[OwnerAttributes]
        aad_id=host_data.get("aad_id", ""),
        criticality=host_data.get("criticality", AssetCriticality.UNKNOWN),
        last_seen=normalize_last_seen(
            host_data.get("last_seen", "")
        ),  # only works for iso date string or list of iso date strings
        source_data=host_data,
    )


class ReadAssets(IntegrationPermissionsHealthCheck):
    name = "Read assets"
    description = "Read assets from File"
    value = "assets:read"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            one_success = False
            api = self.integration.get_api()
            hosts = api.import_hosts()
            for ix, host_data in enumerate(hosts, 1):
                try:
                    normalize_host(host_data)
                    one_success = True
                except (TypeError, KeyError, AttributeError, ValueError) as ex:
                    self.integration.logger.log_normalization_error(ex, ix)

            if one_success:
                return IntegrationHealthCheckResult.PASSED
            else:
                return IntegrationHealthCheckResult.FAILED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
