from uuid import UUID

from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig


class ImportHostsV1Config(TemplateVersionConfig):
    artifact_id: UUID = Field(
        title="Artifact ID",
        description="Artifact ID of the import hosts connector.",
    )


class ImportHostsV1Connection(ConnectionTemplate):
    id = "import_hosts"
    name = "Import Hosts"
    config_model = ImportHostsV1Config
