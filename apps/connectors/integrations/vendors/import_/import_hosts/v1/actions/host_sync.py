from typing import Generator

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
)
from apps.connectors.integrations.vendors.import_.import_hosts.v1.health_check import (
    ReadAssets,
    normalize_host,
)


class ImportHostsV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        api = self.integration.get_api()
        hosts = api.import_hosts()
        yield from hosts

    def get_permission_checks(self):
        return [ReadAssets]
