from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import ImportHostsV1TemplateVersion


class ImportHostsTemplate(Template):
    id = "import_hosts"
    name = "Import Hosts by CSV/Excel"
    category = Template.Category.ASSET_SOURCE
    versions = {
        ImportHostsV1TemplateVersion.id: ImportHostsV1TemplateVersion(),
    }
    vendor = Vendors.IMPORT
