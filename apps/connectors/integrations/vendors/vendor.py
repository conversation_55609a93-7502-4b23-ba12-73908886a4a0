from dataclasses import dataclass


class Vendors:
    @dataclass(frozen=True)
    class Vendor:
        id: str
        name: str

    ABNORMAL = Vendor("abnormal", "Abnormal")
    AMAZON = Vendor("amazon", "Amazon")
    ABSOLUTE = Vendor("absolute", "Absolute")
    ABUSE_IPDB = Vendor("abuse_ipdb", "AbuseIPDB")
    CISCO = Vendor("cisco", "Cisco")
    CLAROTY = Vendor("claroty", "Claroty")
    COMMVAULT = Vendor("commvault", "Commvault")
    CROWDSTRIKE = Vendor("crowdstrike", "CrowdStrike")
    CYBERARK = Vendor("cyberark", "CyberArk")
    CYLANCE = Vendor("cylance", "Cylance")
    EXTRAHOP = Vendor("extrahop", "ExtraHop")
    FORTINET = Vendor("fortinet", "Fortinet")
    FRESHWORKS = Vendor("freshworks", "Freshworks")
    IMPORT = Vendor("import", "Import")
    INFOBLOX = Vendor("infoblox", "Infoblox")
    IVANTI = Vendor("ivanti", "Ivanti")
    JAMF = Vendor("jamf", "Jamf")
    LANSWEEPER = Vendor("lansweeper", "Lansweeper")
    MICROSOFT = Vendor("microsoft", "Microsoft")
    MIMECAST = Vendor("mimecast", "Mimecast")
    NETAPP = Vendor("netapp", "NetApp")
    OKTA = Vendor("okta", "Okta")
    PALO_ALTO = Vendor("palo_alto", "Palo Alto Networks")
    PROOFPOINT = Vendor("proofpoint", "Proofpoint")
    QUALYS = Vendor("qualys", "Qualys")
    RAPID7 = Vendor("rapid7", "Rapid7")
    SENTINELONE = Vendor("sentinel_one", "SentinelOne")
    SERVICENOW = Vendor("service_now", "ServiceNow")
    SEVCO = Vendor("sevco", "Sevco Security")
    SOLARWINDS = Vendor("solarwinds", "SolarWinds")
    TANIUM = Vendor("tanium", "Tanium")
    TENABLE = Vendor("tenable", "Tenable")
    TREND_MICRO = Vendor("trend_micro", "Trend Micro")
    UBIQUITI = Vendor("ubiquiti", "Ubiquiti")
    VEEAM = Vendor("veeam", "Veeam")
    VERITAS = Vendor("veritas", "Veritas")
    VMWARE = Vendor("vmware", "VMware")
    VULNCHECK = Vendor("vulncheck", "VulnCheck")
    ZSCALER = Vendor("zscaler", "Zscaler")

    @classmethod
    def get_all_vendors(cls):
        return [
            value
            for value in Vendors.__dict__.values()
            if isinstance(value, Vendors.Vendor)
        ]
