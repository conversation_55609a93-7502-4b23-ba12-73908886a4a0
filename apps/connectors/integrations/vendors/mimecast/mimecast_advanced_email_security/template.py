from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import MimecastAdvancedEmailSecurityV1TemplateVersion


class MimecastAdvancedEmailSecurityTemplate(Template):
    id = "mimecast_advanced_email_security"
    name = "Mimecast Advanced Email Security"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {
        MimecastAdvancedEmailSecurityV1TemplateVersion.id: MimecastAdvancedEmailSecurityV1TemplateVersion(),
    }
    vendor = Vendors.MIMECAST
