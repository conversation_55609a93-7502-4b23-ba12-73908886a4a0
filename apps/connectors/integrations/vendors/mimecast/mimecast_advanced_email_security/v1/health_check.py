from datetime import datetime

from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadLogs(IntegrationPermissionsHealthCheck):
    name = "Read logs"
    description = "Read logs from Mimecast Advanced Email Security"
    value = "logs_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            self.integration.invoke(
                "get_ttp_url_logs",
                from_date=datetime.now().isoformat(),
                to_date=datetime.now().isoformat(),
                page_size=1,
            )

            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
