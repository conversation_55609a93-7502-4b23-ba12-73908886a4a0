from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1.bookmarks import (
    MimecastAdvancedEmailSecurityEventSyncBookmark,
    MimecastAdvancedEmailSecurityEventSyncBookmarks,
)

from .connection import (
    MimecastAdvancedEmailSecurityV1Config,
    MimecastAdvancedEmailSecurityV1Connection,
)
from .integration import MimecastAdvancedEmailSecurityV1Integration
from .settings import MimecastAdvancedEmailSecurityV1Settings


class MimecastAdvancedEmailSecurityV1TemplateVersion(TemplateVersion):
    integration = MimecastAdvancedEmailSecurityV1Integration
    id = "v1"
    name = "v1"
    config_model = MimecastAdvancedEmailSecurityV1Config
    settings_model = MimecastAdvancedEmailSecurityV1Settings
    connection_model = MimecastAdvancedEmailSecurityV1Connection
    bookmarks_model = MimecastAdvancedEmailSecurityEventSyncBookmarks
