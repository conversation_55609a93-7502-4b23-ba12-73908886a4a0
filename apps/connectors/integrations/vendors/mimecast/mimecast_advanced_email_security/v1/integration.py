from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1.actions.event_sync import (
    MimecastAdvancedEmailSecurityV1EventSync,
)

from .api import MimecastAdvancedEmailSecurityV1Api
from .health_check import ReadLogs


class MimecastAdvancedEmailSecurityV1Integration(Integration):
    api_class = MimecastAdvancedEmailSecurityV1Api
    actions = (MimecastAdvancedEmailSecurityV1EventSync,)
    critical_health_checks = (ReadLogs,)
