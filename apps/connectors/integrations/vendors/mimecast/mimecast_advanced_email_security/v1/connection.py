from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class MimecastAdvancedEmailSecurityV1Config(TemplateVersionConfig):
    base_url: HttpUrl = Field(
        title="Mimecast API URL",
        description="The base URL of the Mimecast API.",
    )
    app_id: str = Field(
        title="App ID",
        description="Application ID value received when you registered your application.",
    )
    username: str = Field(
        default=None,
        title="Username",
        description="Optional. Username for basic authentication.",
    )
    password: EncryptedStr = Field(
        default=None,
        title="Password",
        description="Optional. Password for basic authentication.",
    )


class MimecastAdvancedEmailSecurityV1Connection(ConnectionTemplate):
    id = "mimecast_advanced_email_security"
    name = "Mimecast Advanced Email Security"
    config_model = MimecastAdvancedEmailSecurityV1Config
