from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import FortianalyzerV1TemplateVersion


class FortianalyzerTemplate(Template):
    id = "fortianalyzer"
    name = "FortiAnalyzer"
    category = Template.Category.OPERATIONAL_TECHNOLOGY
    versions = {
        FortianalyzerV1TemplateVersion.id: FortianalyzerV1TemplateVersion(),
    }
    vendor = Vendors.FORTINET
