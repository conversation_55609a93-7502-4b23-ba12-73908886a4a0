from apps.connectors.integrations import TemplateVersion
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.bookmarks import (
    FortianalyzerSyncBookmarks,
)

from .connection import FortianalyzerV1Config, FortianalyzerV1Connection
from .integration import FortianalyzerV1Integration
from .settings import FortianalyzerV1Settings


class FortianalyzerV1TemplateVersion(TemplateVersion):
    integration = FortianalyzerV1Integration
    id = "v1"
    name = "v1"
    config_model = FortianalyzerV1Config
    settings_model = FortianalyzerV1Settings
    connection_model = FortianalyzerV1Connection
    bookmarks_model = FortianalyzerSyncBookmarks
