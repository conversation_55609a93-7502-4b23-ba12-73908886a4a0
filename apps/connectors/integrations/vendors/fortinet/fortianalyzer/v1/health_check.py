from datetime import datetime

from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    IntegrationHealthCheckResult,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.integration import IntegrationError


class ReadEvents(IntegrationPermissionsHealthCheck):
    name = "Read events"
    description = "Read events from fortianalyzer"
    value = "events_inventory:view"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED

    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            response = self.integration.invoke(
                "add_task",
                log_type="firewall",
                adom="root",
                time_range_start=datetime.now().isoformat(),
                time_range_end=datetime.now().isoformat(),
            )
            tid = response.get("result", {}).get("tid")
            self.integration.invoke(
                "delete_task",
                adom="root",
                task_id=tid,
            )
            return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            return IntegrationHealthCheckResult.FAILED
