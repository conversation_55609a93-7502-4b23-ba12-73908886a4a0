from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


class FortianalyzerEventSyncBookmark(TemplateVersionActionBookmark):
    time_range_start: dict = Field(
        title="Time Range Start Datetime",
        description="The start of the time range for the event sync.",
        default_factory=dict,
    )


FortianalyzerSyncBookmarks = create_bookmarks_model(
    "FortianalyzerSyncBookmarks",
    {
        IntegrationActionType.EVENT_SYNC: FortianalyzerEventSyncBookmark,
    },
)
