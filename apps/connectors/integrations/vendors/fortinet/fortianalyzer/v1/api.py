from apps.connectors.integrations import ApiBase

log_types = [
    "anomaly",
    "app-ctrl",
    "casb",
    "dlp",
    "dns",
    "emailfilter",
    "file-filter",
    "forti-switch",
    "gtp",
    "icap",
    "ips",
    "ssh",
    "ssl",
    "virtual-patch",
    "virus",
    "voip",
    "waf",
    "webfilter",
]


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    response = response.get("result", response)
    results = response["data"]
    message = response.get("status", {}).get("message", "")
    items_count = len(results)
    offset = 0
    while items_count > 0 or message != "succeeded":
        yield {"data": results, "message": message}
        offset += items_count
        response = bound_method(offset=offset, **kwargs)
        response = response.get("result", response)
        results = response["data"]
        message = response.get("status", {}).get("message", "")
        items_count = len(results)
    yield {"message": "succeeded", "data": []}


class FortianalyzerV1Api(ApiBase):
    def __init__(
        self,
        host=None,
        api_key=None,
        verify_tls: bool = True,
        **kwargs,
    ):
        self.base_url = f"https://{host}/jsonrpc"
        self.host = host
        self.api_key = api_key
        self._token = None
        super().__init__(
            base_url=self.base_url,
            static_headers={
                "Accept": "application/json",
                "Authorization": f"Bearer {self.api_key}",
            },
            verify_tls=verify_tls,
        )

    def add_task(self, log_type, adom, time_range_start, time_range_end):
        payload = {
            "id": "1",
            "jsonrpc": "2.0",
            "method": "add",
            "params": [
                {
                    "apiver": 3,
                    "case-sensitive": False,
                    "device": [{"devid": "All_FortiGate"}],
                    "logtype": f"{log_type}",
                    "time-order": "asc",
                    "time-range": {"start": time_range_start, "end": time_range_end},
                    "url": f"/logview/adom/{adom}/logsearch/",
                }
            ],
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def search_by_taskid(self, task_id, adom, limit=1000, offset=0):
        payload = {
            "id": "2",
            "jsonrpc": "2.0",
            "method": "get",
            "params": [
                {
                    "apiver": 3,
                    "url": f"/logview/adom/{adom}/logsearch/{task_id}",
                    "limit": limit,
                    "offset": offset,
                }
            ],
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def delete_task(self, task_id, adom):
        payload = {
            "id": "3",
            "jsonrpc": "2.0",
            "method": "delete",
            "params": [
                {"apiver": 3, "url": f"/logview/adom/{adom}/logsearch/{task_id}"}
            ],
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()
