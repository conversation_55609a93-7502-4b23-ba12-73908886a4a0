from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class FortianalyzerV1EventSyncSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Fortianalyzer Event Sync Settings")

    adom: str = Field(
        default="root",
        title="ADOM",
        description="The ADOM to use for the API calls.",
    )


FortianalyzerV1Settings = create_settings_model(
    "FortianalyzerV1Settings",
    {IntegrationActionType.EVENT_SYNC: FortianalyzerV1EventSyncSettings},
)
