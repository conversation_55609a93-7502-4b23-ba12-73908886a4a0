from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class FortianalyzerV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Host",
        description="The FortiAnalyzer host.",
        max_length=1024,
    )
    api_key: EncryptedStr = Field(
        title="API Key",
        description="The API key for FortiAnalyzer.",
        max_length=1024,
    )
    verify_tls: bool = Field(
        default=True,
        title="Verify TLS Certificates",
        description="Defaults to True. Setting this to False could pose a security risk if used improperly.",
    )


class FortianalyzerV1Connection(ConnectionTemplate):
    id = "fortianalyzer"
    name = "FortiAnalyzer"
    config_model = FortianalyzerV1Config
