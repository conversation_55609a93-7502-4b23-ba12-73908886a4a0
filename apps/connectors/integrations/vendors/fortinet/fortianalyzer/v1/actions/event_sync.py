from datetime import datetime, timedelta, timezone
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
)
from apps.connectors.integrations.actions.utils import normalize, to_list
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.api import (
    FortianalyzerV1Api,
    log_types,
    paginate,
)
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.bookmarks import (
    FortianalyzerEventSyncBookmark,
)
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.settings import (
    FortianalyzerV1EventSyncSettings,
)


def parse_datetime(dt_str: str) -> datetime | None:
    if not dt_str:
        return None
    return parser.parse(dt_str).replace(tzinfo=timezone.utc)


def get_iso_date(dt_str: str) -> datetime:
    return parser.parse(dt_str).isoformat()


def get_reputation_score(
    crlevel,
) -> ocsf.reputation.ReputationScore:
    if crlevel == "critical":
        return ocsf.reputation.ReputationScore.POSSIBLY_MALICIOUS
    elif crlevel == "high":
        return ocsf.reputation.ReputationScore.SUSPICIOUS_RISKY
    elif crlevel == "medium":
        return ocsf.reputation.ReputationScore.MAY_NOT_BE_SAFE
    elif crlevel == "low":
        return ocsf.reputation.ReputationScore.PROBABLY_SAFE
    else:
        return ocsf.reputation.ReputationScore.UNKNOWN


def get_severity(crlevel) -> ocsf.Severity:
    if crlevel == "critical":
        return ocsf.Severity.CRITICAL
    elif crlevel == "high":
        return ocsf.Severity.HIGH
    elif crlevel == "elevated":
        return ocsf.Severity.OTHER
    elif crlevel == "medium":
        return ocsf.Severity.MEDIUM
    elif crlevel == "low":
        return ocsf.Severity.LOW
    else:
        return ocsf.Severity.UNKNOWN


def get_direction(direction: str) -> ocsf.NetworkDirection:
    if direction == "incoming":
        return ocsf.NetworkDirection.INBOUND
    elif direction == "outbound":
        return ocsf.NetworkDirection.OUTBOUND
    else:
        return ocsf.NetworkDirection.UNKNOWN


def get_action_disposition(action: str, type: str):
    if type == "anomaly":
        if action == "detected":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.DETECTED
        elif action == "dropped":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED
        elif action == "reset":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
        elif action == "clear_session":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER
    elif type == "app-ctrl":
        if action == "pass":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "block":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "reject":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.QUARANTINED
        elif action == "reset":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
    elif type == "casb":
        if action == "monitor":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED
        elif action == "block":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "bypass":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
    elif type == "dlp":
        if action == "log-only":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED
        elif action == "block":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "exempt":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "ban":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "ban-sender":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "quarantine-ip":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "quarantine-intreface":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
    elif type == "dns":
        if action == "allow":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "monitor":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.LOGGED
        elif action == "block":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
    elif type == "emailfilter":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "detected":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED
        elif action == "exempted":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
    elif type == "event":
        if action == "add":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "edit":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "delete":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "login":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "logout":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED
        elif action == "system-reboot":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED
        elif action == "config-change":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "admin-login":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "admin-logout":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED
    elif type == "file-filter":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "passthrough":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
    elif type == "icap":
        if action == "forward":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER
        elif action == "bypass":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.ERROR
        elif action == "error":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.ERROR
        elif action == "detected":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED
    elif type == "ips":
        if action == "dropped":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED
        elif action == "reset":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
        elif action == "reset_client":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
        elif action == "reset_server":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
        elif action == "drop_session":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED
        elif action == "pass_session":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "clear_session":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER
    elif type == "ssh":
        if action == "passthrough":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
    elif type == "ssl":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "exempt":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER
    elif type == "traffic":
        if action == "deny":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "accept":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "start":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER
        elif action == "dns":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ERROR
        elif action == "ip-conn":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ERROR
        elif action == "close":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "timeout":
            return ocsf.ControlAction.OBSERVED, ocsf.Disposition.RESET
        elif action == "client-rst":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
        elif action == "server-rst":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.RESET
    elif type == "virtual-patch":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "patched":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.CORRECTED
    elif type == "virus":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "passthrough":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER
        elif action == "monitored":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.LOGGED
        elif action == "analytics":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.UNKNOWN
    elif type == "voip":
        if action == "permit":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "dropped":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED
    elif type == "waf":
        if action == "pass":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
        elif action == "block":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "monitor":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED
        elif action == "erase":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.CORRECTED
    elif type == "webfilter":
        if action == "blocked":
            return ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED
        elif action == "passthrough":
            return ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED
    return ocsf.ControlAction.UNKNOWN, ocsf.Disposition.UNKNOWN


def get_osint(event: dict) -> ocsf.OSINT:
    osint = []
    reputation = ocsf.reputation.Reputation(
        provider=event.get("craction"),
        score=get_reputation_score(event.get("crlevel")),
        base_score=event.get("crscore"),
    )
    if event.get("attack"):
        osint.append(
            ocsf.OSINT(
                value=event.get("attack"),
                uid=event.get("attackId"),
                src_url=event.get("ref"),
                type=ocsf.OSINTIndicatorType.OTHER,
                reputation=reputation,
            )
        )
    if event.get("to") and event.get("from"):
        osint.append(
            ocsf.OSINT(
                value=event.get("from") or event.get("sender"),
                type=ocsf.OSINTIndicatorType.EMAIL,
                reputation=reputation,
                email=ocsf.Email(
                    cc=event.get("cc"),
                    from_mailbox=event.get("from"),
                    to=to_list(event.get("recipient")),
                    to_mailboxes=event.get("to"),
                    subject=event.get("subject"),
                    from_=event.get("sender"),
                ),
            )
        )

    if event.get("filename"):
        osint.append(
            ocsf.OSINT(
                value=event.get("filename"),
                type=ocsf.OSINTIndicatorType.FILE,
                file=ocsf.File(
                    path=event.get("file"),
                    name=event.get("filename"),
                    size=event.get("filesize"),
                    mime_type=event.get("filetype"),
                    hashes=[
                        ocsf.Fingerprint(
                            value=event.get("filehash") or event.get("hash"),
                            algorithm=ocsf.HashAlgorithm.UNKNOWN,
                        )
                        if event.get("filehash") or event.get("hash")
                        else None
                    ],
                ),
            )
        )
    if event.get("infectedfilename"):
        osint.append(
            ocsf.OSINT(
                file=ocsf.File(
                    name=event.get("infectedfilename"),
                    size=event.get("infectedfilesize"),
                    mime_type=event.get("infectedfiletype"),
                    url=event.get("infectedfileurl"),
                ),
                value=event.get("url"),
            )
        )
    return osint


def convert_to_ocsf(event: dict, log_type: str) -> ocsf.NetworkActivity:
    osint = get_osint(event)
    action, disposition = get_action_disposition(
        action=event.get("action"),
        type=log_type,
    )

    return ocsf.NetworkActivity(
        action=action,
        disposition=disposition,
        activity=ocsf.DetectionActivity.UNKNOWN,
        dst_endpoint=ocsf.NetworkEndpoint(
            ip=event.get("dstip") or event.get("addr") or event.get("end-usr-address"),
            hostname=event.get("dst_host"),
            interface_name=event.get("dst_int"),
            port=event.get("dst_port") or event.get("dstport"),
            location=ocsf.GeoLocation(
                country=event.get("dstcountry"),
                city=event.get("dstcity"),
                region=event.get("dstregion"),
            ),
            type=event.get("dstdevtype"),
            isp=event.get("dstinetsvc"),
            mac=event.get("dstmac"),
            name=event.get("dstname"),
            os=ocsf.OperatingSystem(
                name=event.get("dstosname"),
            )
            if event.get("dstosname")
            else None,
            owner=ocsf.User(
                name=event.get("dstuser"),
            ),
            uid=event.get("dstuuid"),
        ),
        app_name=event.get("app"),
        traffic=ocsf.NetworkTraffic(
            packets=event.get("count"),
        ),
        osint=osint,
        severity=get_severity(event.get("crlevel") or event.get("level")),
        time_dt=parse_datetime(event.get("date") + "T" + event.get("time")),
        src_endpoint=ocsf.NetworkEndpoint(
            name=event.get("hostname") or event.get("src_host") or event.get("srcname"),
            ip=event.get("ip")
            or event.get("srcip")
            or event.get("srcaddr")
            or event.get("src-usr-address"),
            traffic=ocsf.NetworkTraffic(
                bytes_in=event.get("lanin"),
                bytes_out=event.get("lanout"),
            ),
            mac=event.get("mac") or event.get("srcmac"),
            interface_name=event.get("src_int") or event.get("srcintf"),
            port=event.get("port") or event.get("src_port") or event.get("srcport"),
            location=ocsf.GeoLocation(
                country=event.get("srccountry"),
                city=event.get("srccity"),
                region=event.get("srcregion"),
                domain=event.get("srcdomain"),
            ),
            isp=event.get("srcinetsvc"),
            uid=event.get("srcuuid"),
            owner=ocsf.User(
                name=event.get("srcuser"),
            ),
            type=event.get("srctype"),
            device_type=event.get("srcdevtype"),
            device_name=event.get("srcname"),
        ),
        actor=ocsf.Actor(
            process=ocsf.Process(
                user=ocsf.User(
                    name=event.get("login"),
                ),
                name=event.get("process"),
            ),
            session=ocsf.Session(
                uid=event.get("session_id") or event.get("sessionid"),
            ),
            user=ocsf.User(
                name=event.get("user"),
            ),
        ),
        metadata=ocsf.Metadata(
            product=ocsf.Product(
                version=str(event.get("version")),
                uid=str(event.get("serial")) if event.get("serial") else "",
                name=event.get("hostname")
                or event.get("src_host")
                or event.get("srcname")
                or "",
            ),
            uid=event.get("logid"),
            event_code=event.get("subtype"),
            correlation_uid=event.get("logid"),
            version=str(event.get("version")),
            profiles=[],
        ),
        status=event.get("status"),
        status_code=event.get("statuscode"),
        time=event.get("time"),
        timezone_offset=event.get("tz"),
        device=ocsf.Device(
            os=ocsf.OperatingSystem(
                name=event.get("osname"),
            )
            if event.get("osname")
            else None,
            uid=event.get("devid"),
            type=event.get("devtype"),
        ),
        policy=ocsf.Policy(
            uid=event.get("policyid") or event.get("poluuid"),
            name=event.get("policyname") or event.get("policy_id"),
        ),
        connection_info=ocsf.NetworkConnectionInfo(
            direction=get_direction(event.get("direction")),
            protocol=event.get("protocol"),
            protocol_num=event.get("proto"),
            port=event.get("port") or event.get("src_port") or event.get("srcport"),
            src_port=event.get("src_port") or event.get("srcport"),
        ),
        message=f"Type - {event.get('type')} SubType - {event.get('subtype')} Message ID - {event.get('logid')[-5:]}",
    )


def normalize_event(event: dict) -> Event:
    log_type = event.get("subtype")
    return Event(
        raw_event=event,
        event_timestamp=get_iso_date(event.get("date") + "T" + event.get("time")),
        ioc=EventIOCInfo(
            external_id=event.get("logid"),
            external_name=f"Type - {event.get('type')} SubType - {event.get('subtype')} Message ID - {event.get('logid')[-5:]}",
            has_ioc_definition=False,
            mitre_techniques=None,
        ),
        ocsf=convert_to_ocsf(event, log_type=log_type),
    )


class FortianalyzerV1EventSync(EventSync):
    settings: FortianalyzerV1EventSyncSettings

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: FortianalyzerEventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: FortianalyzerV1Api = self.integration.get_api()
        for log_type in log_types:
            log_type_start = bookmark.time_range_start.get(log_type)
            if not log_type_start:
                # Default to one day ago if no timestamp exists for this log type
                log_type_start = (
                    (datetime.now(timezone.utc) - timedelta(days=1))
                    .isoformat()
                    .replace("+00:00", "Z")
                )
            response = api.add_task(
                log_type=log_type,
                adom=self.settings.adom,
                time_range_start=parse_datetime(log_type_start).isoformat(),
                time_range_end=datetime.now().isoformat(),
            )

            tid = response.get("result", {}).get("tid")
            response = paginate(
                api.search_by_taskid,
                task_id=tid,
                adom=self.settings.adom,
            )
            time_stamp = parse_datetime(log_type_start)
            for event in response:
                for data in event.get("data", []):
                    yield data
                    if time_stamp > parse_datetime(
                        data.get("date") + "T" + data.get("time")
                    ):
                        time_stamp = parse_datetime(
                            data.get("date") + "T" + data.get("time")
                        )

            api.delete_task(
                task_id=tid,
                adom=self.settings.adom,
            )
            bookmark.time_range_start[log_type] = time_stamp.isoformat().replace(
                "+00:00", "Z"
            )

    def get_permission_checks(self):
        return []
