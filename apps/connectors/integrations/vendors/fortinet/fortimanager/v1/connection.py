from pydantic import Field

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class FortimanagerV1Config(TemplateVersionConfig):
    host: str = Field(
        title="Host",
        description="The FortiManager host.",
        max_length=1024,
    )
    api_key: EncryptedStr = Field(
        title="Api Key",
        description="API Key used to authenticate with FortiManager.",
    )
    verify_tls: bool = Field(
        default=True,
        title="Verify TLS Certificates",
        description="Defaults to True. Setting this to False could pose a security risk if used improperly.",
    )


class FortimanagerV1Connection(ConnectionTemplate):
    id = "fortimanager"
    name = "FortiManager"
    config_model = FortimanagerV1Config
