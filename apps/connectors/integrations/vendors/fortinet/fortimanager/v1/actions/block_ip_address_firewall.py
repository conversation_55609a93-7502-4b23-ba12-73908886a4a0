from apps.connectors.integrations.actions.ip_address import (
    BlockIpAddress,
    BlockIpAddressArgs,
    BlockIpAddressResult,
    Message,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.api import (
    FortimanagerV1Api,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.health_check import (
    FortimanagerReadPermissionsCheck,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.settings import (
    FortimanagerV1IPAdressSettings,
)


class FortimanagerV1BlockIpAddressFirewall(BlockIpAddress):
    settings: FortimanagerV1IPAdressSettings

    def execute(self, args: BlockIpAddressArgs) -> BlockIpAddressResult:
        api: FortimanagerV1Api = self.integration.get_api()
        response = api.create_firewall_address(
            address_ip=args.ip_address.value, adom=self.settings.adom
        )
        api.add_firewall_address_to_address_group(
            address_group_name=self.settings.address_group_name,
            member=response.get("result", {})[0].get("name"),
            adom=self.settings.adom,
        )
        api.install_object(
            address_group_name=self.settings.address_group_name, adom=self.settings.adom
        )
        return BlockIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was blocked.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return [FortimanagerReadPermissionsCheck]
