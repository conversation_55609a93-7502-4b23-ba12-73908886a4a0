from apps.connectors.integrations.actions.ip_address import (
    Message,
    UnblockIpAddress,
    UnblockIpAddressArgs,
    UnblockIpAddressResult,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.api import (
    FortimanagerV1Api,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.health_check import (
    FortimanagerReadPermissionsCheck,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.settings import (
    FortimanagerV1IPAdressSettings,
)


class FortimanagerV1UnblockIpAddressFirewall(UnblockIpAddress):
    settings: FortimanagerV1IPAdressSettings

    def execute(self, args: UnblockIpAddressArgs) -> UnblockIpAddressResult:
        api: FortimanagerV1Api = self.integration.get_api()
        api.create_firewall_address(
            address_ip=args.ip_address.value, adom=self.settings.adom
        )
        api.delete_firewall_address_from_address_group(
            address_group_name=self.settings.address_group_name,
            member=args.ip_address.value,
            adom=self.settings.adom,
        )
        api.install_object(
            address_group_name=self.settings.address_group_name, adom=self.settings.adom
        )
        return UnblockIpAddressResult(
            result=Message(message=f"{args.ip_address.value} was unblocked.")
        )

    def get_permission_checks(self, *args, **kwargs):  # pragma: no cover
        return [FortimanagerReadPermissionsCheck]
