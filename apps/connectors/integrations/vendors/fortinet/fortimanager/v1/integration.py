from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.actions.block_ip_address_firewall import (
    FortimanagerV1BlockIpAddressFirewall,
)
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1.actions.unblock_ip_address_firewall import (
    FortimanagerV1UnblockIpAddressFirewall,
)

from .api import FortimanagerV1Api
from .health_check import ConnectionHealthCheck


class FortimanagerV1Integration(Integration):
    api_class = FortimanagerV1Api
    actions = (
        FortimanagerV1BlockIpAddressFirewall,
        FortimanagerV1UnblockIpAddressFirewall,
    )
    critical_health_checks = (ConnectionHealthCheck,)
