from apps.connectors.integrations import ApiBase


class FortimanagerV1Api(ApiBase):
    def __init__(
        self,
        host=None,
        api_key=None,
        verify_tls: bool = True,
        **kwargs,
    ):
        self.base_url = f"https://{host}/jsonrpc"
        self.host = host
        static_headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {api_key}",
        }
        super().__init__(
            base_url=self.base_url,
            static_headers=static_headers,
            verify_tls=verify_tls,
        )

    def create_firewall_address(self, address_ip, adom):
        payload = {
            "method": "set",
            "params": [
                {
                    "data": [
                        {
                            "name": f"{address_ip}",
                            "type": "ipmask",
                            "subnet": f"{address_ip}/32",
                        }
                    ],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/address",
                }
            ],
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def add_firewall_address_to_address_group(self, address_group_name, member, adom):
        payload = {
            "method": "add",
            "params": [
                {
                    "data": [member],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/addrgrp/{address_group_name}/member",
                }
            ],
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def delete_firewall_address_from_address_group(
        self, address_group_name, member, adom
    ):
        payload = {
            "method": "delete",
            "params": [
                {
                    "data": [member],
                    "url": f"/pm/config/adom/{adom}/obj/firewall/addrgrp/{address_group_name}/member",
                }
            ],
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def get_system_status(self):
        payload = {
            "method": "get",
            "params": [
                {
                    "url": "/sys/status",
                }
            ],
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def install_object(self, address_group_name, adom):
        payload = {
            "method": "exec",
            "params": [
                {
                    "data": {
                        "adom": f"{adom}",
                        "objects": [
                            [
                                "update",
                                f"obj/firewall/address/{address_group_name}",
                                "",
                                "",
                            ]
                        ],
                        "flags": 0,
                    },
                    "url": "securityconsole/install/objects/v2",
                }
            ],
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()

    def list_firewall_address(self, adom="root"):
        payload = {
            "method": "get",
            "params": [
                {
                    "url": f"/pm/config/adom/{adom}/obj/firewall/address",
                }
            ],
            "id": 1,
        }
        response = self.session.post(self.base_url, json=payload)
        return response.json()
