from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.fortinet.fortimanager.v1 import (
    FortimanagerV1TemplateVersion,
)
from apps.connectors.integrations.vendors.vendor import Vendors


class FortimanagerTemplate(Template):
    id = "fortimanager"
    name = "FortiManager"
    category = Template.Category.NETWORK_SECURITY
    versions = {
        FortimanagerV1TemplateVersion.id: FortimanagerV1TemplateVersion(),
    }
    vendor = Vendors.FORTINET
