from enum import StrEnum
from typing import Optional

from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class UpdateLifecycleStatusResult(BaseModel):
    ...


class CorrIncidentStatus(StrEnum):
    NEW = "new"
    ASSIGNED = "assigned"
    REVIEWING = "reviewing"
    MITIGATED = "mitigated"
    CLOSED = "closed"


class CorrVerdict(StrEnum):
    BENIGN_TRUE_POSITIVE = "benign_true_positive"
    TRUE_POSITIVE = "true_positive"
    FALSE_POSITIVE = "false_positive"


class UpdateLifecycleStatusArgs(IntegrationActionArgs):
    vendor_sync_id: str
    status: CorrIncidentStatus
    verdict: Optional[CorrVerdict] = None
    assigned_to: Optional[str] = None
    comment: Optional[str] = None


class UpdateLifecycleStatus(IntegrationAction):
    name = "Update Lifecycle Status"
    action_type = IntegrationActionType.UPDATE_LIFECYCLE_STATUS
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UpdateLifecycleStatusArgs,
        result_type=UpdateLifecycleStatusResult,
    )
    is_visible = False
    is_required = True
