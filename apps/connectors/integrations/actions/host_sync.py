import logging
from datetime import datetime, timezone
from enum import StrEnum, auto
from typing import Annotated, Optional, TypeVar

import email_validator
from pydantic import (
    BaseModel,
    BeforeValidator,
    EmailStr,
    Field,
    StringConstraints,
    ValidationInfo,
    field_validator,
    model_validator,
)

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
    InvocationType,
)
from apps.connectors.integrations.actions.utils import (
    build_fqdn,
    normalize_ip_addresses,
    normalize_mac_addresses,
    parse_fqdn,
    to_list,
)
from apps.connectors.integrations.schemas import OsAttributes
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs
from apps.connectors.integrations.schemas.operating_system import HostType, OsFamily

logger = logging.getLogger(__name__)


def remove_falsy_items(v):
    return [s for s in v if s]


T = TypeVar("T")
LowerStr = Annotated[str, StringConstraints(to_lower=True)]
TruthyItemList = Annotated[list[T], BeforeValidator(remove_falsy_items)]


class OwnerAttributes(BaseModel):
    name: str
    email: Optional[EmailStr]

    @field_validator("email", mode="before")
    def validate(cls, value: any, info: ValidationInfo):  # noqa: N805
        if value:
            try:
                if not isinstance(value, (str, bytes)):
                    raise email_validator.EmailNotValidError
                email_validator.validate_email(value, check_deliverability=False)
            except email_validator.EmailNotValidError:
                logger.warning("Invalid email address", extra={"email": value})
                return None
        return value


class AssetCriticality(StrEnum):
    TIER_0 = "tier0"
    TIER_1 = "tier1"
    TIER_2 = "tier2"
    TIER_3 = "tier3"
    TIER_4 = "tier4"
    UNKNOWN = "unknown"


class InternetExposure(StrEnum):
    INTERNET_FACING = auto()
    NOT_INTERNET_FACING = auto()
    UNKNOWN = auto()


class Host(BaseModel):
    source_id: str
    group_names: list[str] = Field(default_factory=list)
    hostname: LowerStr
    fqdns: TruthyItemList[LowerStr] = Field(default_factory=list)
    ip_addresses: TruthyItemList[str] = Field(default_factory=list)
    mac_addresses: TruthyItemList[str] = Field(default_factory=list)
    # FIXME: is_internet_facing will be deprecated in favor of internet_exposure
    is_internet_facing: Optional[bool] = None
    internet_exposure: InternetExposure = InternetExposure.UNKNOWN
    os: OsAttributes
    _os_name: Optional[str] = None
    _domain: Optional[str] = None
    owners: TruthyItemList[OwnerAttributes] = Field(default_factory=list)
    aad_id: Optional[LowerStr] = None
    criticality: Optional[AssetCriticality] = AssetCriticality.UNKNOWN
    last_seen: Optional[datetime] = None
    source_data: Optional[dict] = None

    @model_validator(mode="before")
    @staticmethod
    def validate_os(data: dict):
        if "os" in data:
            return data

        os_name = data.get("_os_name")
        os_family, __ = OsFamily.from_string(os_name)
        data["os"] = OsAttributes(
            host_type=HostType.from_os_name(os_name),
            family=os_family,
            name=os_name,
        )
        return data

    @model_validator(mode="before")
    @staticmethod
    def validate_fqdns(data: dict):
        fqdns = data.get("fqdns")
        if fqdns:
            data["fqdns"] = to_list(fqdns)
            if not data.get("hostname"):
                data["hostname"], _ = parse_fqdn(data["fqdns"][0])
        else:
            data["fqdns"] = build_fqdn(data.get("hostname"), data.get("_domain"))

        return data

    @field_validator("hostname", mode="before")
    def validate_hostname(cls, hostname: any, info: ValidationInfo):  # noqa: N805
        return hostname or ""

    @field_validator("mac_addresses", mode="before")
    def validate_mac_addresses(
        cls, mac_addresses: any, info: ValidationInfo  # noqa: N805
    ):  # noqa: N805
        return normalize_mac_addresses(mac_addresses)

    @field_validator("ip_addresses", mode="before")
    def validate_ip_addresses(
        cls, ip_addresses: any, info: ValidationInfo  # noqa: N805
    ):  # noqa: N805
        return normalize_ip_addresses(ip_addresses)

    @field_validator("last_seen", mode="after")
    def validate_last_seen(cls, last_seen: datetime | None):  # noqa: N805
        if last_seen:
            if not last_seen.tzinfo:
                return last_seen.replace(tzinfo=timezone.utc)
        return last_seen


class HostSyncArgs(IntegrationActionArgs):
    pass


class HostSync(IntegrationAction):
    name = "Fetch hosts"
    action_type = IntegrationActionType.HOST_SYNC
    entitlement = Entitlement.assets
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=HostSyncArgs,
        result_type=Host,
    )


class VulnerabilityAssetSync(IntegrationAction):
    name = "Fetch all hosts"
    action_type = IntegrationActionType.VULNERABILITY_ASSET_SYNC
    entitlement = Entitlement.vulnerabilities
    metadata = IntegrationActionMetadata(
        args_type=HostSyncArgs,
        result_type=Host,
    )
    is_visible = False
