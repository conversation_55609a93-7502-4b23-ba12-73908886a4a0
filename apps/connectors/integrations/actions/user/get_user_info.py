from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    TAPResult,
    UPNIdentifierArgs,
    UserIdentifierArgs,
    ocsf,
)


class UserInfoResult(TAPResult[ocsf.User]):
    ...


class GetUserInfo(IntegrationAction):
    name = "Get user"
    action_type = IntegrationActionType.GET_USER_INFO
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=UserInfoResult,
    )
    is_required = True


class GetUserInfoByUPN(IntegrationAction):
    name = "Get user by UPN"
    action_type = IntegrationActionType.GET_USER_INFO_BY_UPN
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UPNIdentifierArgs,
        result_type=UserInfoResult,
    )
    is_required = True
