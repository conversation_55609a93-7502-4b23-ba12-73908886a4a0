from datetime import datetime
from typing import Optional

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    IntegrationIdentifierArgs,
    IpAddressIdentifier,
    TAPResult,
    UPNIdentifier,
    UserIdentifier,
    ocsf,
)


class SignInLogsByUserIdArgs(IntegrationIdentifierArgs):
    """
    Args for getting sign-in logs by user_id.
    This model defines the arguments required to filter sign-in logs based on user.
    """

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    user_id: Optional[UserIdentifier] = None


class SignInLogsByUPNArgs(IntegrationIdentifierArgs):
    """
    Args for getting sign-in logs by UPN.
    This model defines the arguments required to filter sign-in logs based on UPN.
    """

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    upn: Optional[UPNIdentifier] = None


class SignInLogsByIpArgs(IntegrationIdentifierArgs):
    """
    Args for getting sign-in logs by IP address.
    This model defines the arguments required to filter sign-in logs based on IP address.
    """

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    ip_address: IpAddressIdentifier = None


class SignInLogsResult(TAPResult[list[ocsf.Authentication]]):
    ...


class GetSignInLogsByUser(IntegrationAction):
    name = "Get sign-in logs by user_id"
    action_type = IntegrationActionType.GET_SIGN_IN_LOGS_BY_USER_ID
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=SignInLogsByUserIdArgs,
        result_type=SignInLogsResult,
    )
    is_required = True


class GetSignInLogsByUPN(IntegrationAction):
    name = "Get sign-in logs by UPN"
    action_type = IntegrationActionType.GET_SIGN_IN_LOGS_BY_UPN
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=SignInLogsByUPNArgs,
        result_type=SignInLogsResult,
    )
    is_required = True


class GetSignInLogsByIp(IntegrationAction):
    name = "Get sign-in logs by IP"
    action_type = IntegrationActionType.GET_SIGN_IN_LOGS_BY_IP
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=SignInLogsByIpArgs,
        result_type=SignInLogsResult,
    )
    is_required = True
