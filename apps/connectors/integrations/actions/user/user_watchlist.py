from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    Message,
    TAPResult,
    UserIdentifierArgs,
)


class AddUserToWatchlistResult(TAPResult[Message]):
    """Result of the AddUserToWatchlist action."""

    ...


class AddUserToWatchlist(IntegrationAction):
    name = "Add User to Watchlist"
    action_type = IntegrationActionType.ADD_USER_TO_WATCHLIST
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=AddUserToWatchlistResult,
    )
    is_required = True


class RemoveUserFromWatchlistResult(TAPResult[Message]):
    """Result of the RemoveUserFromWatchlist action."""

    ...


class RemoveUserFromWatchlist(IntegrationAction):
    name = "Remove User from Watchlist"
    action_type = IntegrationActionType.REMOVE_USER_FROM_WATCHLIST
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=RemoveUserFromWatchlistResult,
    )
    is_required = True
