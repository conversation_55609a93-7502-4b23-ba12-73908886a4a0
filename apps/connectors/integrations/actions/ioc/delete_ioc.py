from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class DeleteIocArgs(IntegrationActionArgs):
    source_id: str


class DeleteIocResult(BaseModel):
    ...


class DeleteIoc(IntegrationAction):
    name = "Delete IOC"
    action_type = IntegrationActionType.DELETE_IOC
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=DeleteIocArgs,
        result_type=DeleteIocResult,
    )
    is_visible = False
    is_required = True
