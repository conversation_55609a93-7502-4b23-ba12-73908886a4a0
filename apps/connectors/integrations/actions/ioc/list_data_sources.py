from enum import StrEnum
from typing import Optional

from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class ListDataSourcesArgs(IntegrationActionArgs):
    start_time: str  # ISO 8601
    end_time: str  # ISO 8601


class DataSourceType(StrEnum):
    TABLE = "table"
    INDEX = "index"
    SOURCE_TYPE = "source_type"
    EVENT_TYPE = "event_type"
    PRODUCT = "product"
    SOURCE_CATEGORY = "source_category"


class DataSource(BaseModel):
    source_type: DataSourceType
    source_name: str
    log_count: int

    # NOTE: Must be ISO 8601 string format
    last_log_time: Optional[str]


class ListDataSources(IntegrationAction):
    name = "List Data Sources"
    action_type = IntegrationActionType.LIST_DATA_SOURCES
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=ListDataSourcesArgs,
        result_type=DataSource,
    )
    is_visible = False
    is_required = True
