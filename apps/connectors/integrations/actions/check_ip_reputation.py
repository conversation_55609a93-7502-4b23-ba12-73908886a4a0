from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    IntegrationIdentifierArgs,
    IpAddressIdentifier,
    TAPResult,
    ocsf,
)


class CheckIpReputationArgs(IntegrationIdentifierArgs):
    ip_address: IpAddressIdentifier


class CheckIpReputationResult(TAPResult[ocsf.OSINT]):
    ...


class CheckIpReputation(IntegrationAction):
    name = "Lookup IP Address"
    action_type = IntegrationActionType.CHECK_IP_REPUTATION
    entitlement = Entitlement.internal_system
    metadata = IntegrationActionMetadata(
        args_type=CheckIpReputationArgs,
        result_type=CheckIpReputationResult,
    )
    is_required = True
