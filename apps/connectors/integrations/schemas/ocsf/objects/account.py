from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AccountType

from .key_value_object import KeyValueObject


class Account(BaseModel):
    """
    The Account object contains details about the account that initiated or performed a
    specific activity within a system or application. Additionally, the Account object
    refers to logical Cloud and Software-as-a-Service (SaaS) based containers such as
    AWS Accounts, Azure Subscriptions, Oracle Cloud Compartments, Google Cloud Projects,
    and otherwise.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AccountType.set_values(values, "type_id", "type")

        return values

    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="The list of labels associated to the account.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the account (e.g. GCP Project name, Linux Account name, or AWS Account name).",
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="The list of tags; {key:value} pairs associated to the account.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description=(
            "The account type, normalized to the caption of 'account_type_id'. "
            "In the case of 'Other', it is defined by the event source."
        ),
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The normalized account type identifier.
            0   Unknown
                The account type is unknown.
            1   LDAP Account
            2   Windows Account
            3   AWS IAM User
            4   AWS IAM Role
            5   GCP Account
            6   Azure AD Account
            7   Mac OS Account
            8   Apple Account
            9   Linux Account
            10  AWS Account
            11  GCP Project
            12  OCI Compartment
            13  Azure Subscription
            14  Salesforce Account
            15  Google Workspace
            16  Servicenow Instance
            17  M365 Tenant
            18  Email Account
            99  Other
                The account type is not mapped.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description=(
            "The unique identifier of the account (e.g. AWS Account ID, OCID, GCP Project ID, "
            "Azure Subscription ID, Google Workspace Customer ID, or M365 Tenant UID)."
        ),
    )
