from typing import Any, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import RiskLevel

from .graph import Graph
from .group import Group
from .key_value_object import KeyValueObject
from .software_bill_of_materials import SoftwareBillOfMaterials
from .user import User


class Application(BaseModel):
    """
    An Application describes the details for an inventoried application as reported by an
    Application Security tool or other Developer-centric tooling. Applications can be
    defined as Kubernetes resources, Containerized resources, or application
    hosting-specific cloud sources such as AWS Elastic BeanStalk, AWS Lightsail, or Azure
    Logic Apps.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        RiskLevel.set_values(values, "risk_level_id", "risk_level")
        return values

    criticality: Optional[str] = Field(
        default=None,
        title="Business Criticality",
        description="""
        The criticality of the application as defined by the event source.
        """,
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""
        Additional data describing the application.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Application Description",
        description="""
        A description or commentary for an application, usually retrieved from an
        upstream system.
        """,
    )
    group: Optional[Group] = Field(
        default=None,
        title="Group",
        description="""
        The name of the related application or associated resource group.
        """,
    )
    hostname: Optional[str] = Field(
        default=None,
        title="Hostname",
        description="""
        The fully qualified name of the application.
        """,
    )
    labels: Optional[list[str]] = Field(
        default=None,
        title="Labels",
        description="""
        The list of labels associated to the application.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Application Name",
        description="""
        The name of the application.
        """,
    )
    owner: Optional[User] = Field(
        default=None,
        title="Owner",
        description="""
        The identity of the service or user account that owns the application.
        """,
    )
    region: Optional[str] = Field(
        default=None,
        title="Region",
        description="""
        The cloud region of the resource.
        """,
    )
    resource_relationship: Optional[Graph] = Field(
        default=None,
        title="Application Relationship",
        description="""
        A graph representation showing how this application relates to and interacts with
        other entities in the environment. This can include parent/child relationships,
        dependencies, or other connections.
        """,
    )
    risk_level: Optional[str] = Field(
        default=None,
        title="Risk Level",
        description="""
        The risk level, normalized to the caption of the risk_level_id value.
        """,
    )
    risk_level_id: Optional[int] = Field(
        default=None,
        title="Risk Level ID",
        description="""
        The normalized risk level id.

        0    Info
        1    Low
        2    Medium
        3    High
        4    Critical
        99   Other

        The risk level is not mapped. See the `risk_level` attribute, which contains a
        data source specific value.
        """,
    )
    risk_score: Optional[int] = Field(
        default=None,
        title="Risk Score",
        description="""
        The risk score as reported by the event source.
        """,
    )
    sbom: Optional[SoftwareBillOfMaterials] = Field(
        default=None,
        title="Related SBOM",
        description="""
        The Software Bill of Materials (SBOM) associated with the application.
        """,
    )
    tags: Optional[list[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="""
        The list of tags; `{key:value}` pairs associated to the application.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Application Type",
        description="""
        The type of application as defined by the event source, e.g., `GitHub`, `Azure
        Logic App`, or `Amazon Elastic BeanStalk`.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Application ID",
        description="""
        The unique identifier for the application.
        """,
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Application Alternative ID",
        description="""
        An alternative or contextual identifier for the application, such as a
        configuration, organization, or license UID.
        """,
    )
    url: Optional[str] = Field(
        default=None,
        title="URL",
        description="""
        The URL of the application.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Application Version",
        description="""
        The semantic version of the application, e.g., `1.7.4`.
        """,
    )
