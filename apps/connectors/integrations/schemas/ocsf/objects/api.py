from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.objects.response import Response
from apps.connectors.integrations.schemas.ocsf.objects.service import Service


class Api(BaseModel):
    operation: str | None = Field(
        default=None,
        description="""
        The operation being performed by the API call
        """,
    )
    response: Response | None = Field(
        default=None,
        description="""
        The response returned by the API call
        """,
    )
    service: Service | None = Field(
        default=None,
        description="""
        The service being called by the API
        """,
    )
