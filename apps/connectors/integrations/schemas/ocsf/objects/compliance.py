from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ComplianceStatus

from .assessment import Assessment
from .check import Check
from .key_value_object import KeyValueObject


class Compliance(BaseModel):
    """
    The Compliance object contains information about Industry and Regulatory Framework
    standards, controls and requirements or details about custom assessments utilized
    in a compliance evaluation. Standards define broad security frameworks, controls
    represent specific security requirements within those frameworks, and checks are
    the testable verification points used to determine if controls are properly implemented.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ComplianceStatus.set_values(values, "status_id", "status")
        return values

    assessments: Optional[List[Assessment]] = Field(
        default=None,
        title="Assessments",
        description="""
        A list of assessments related to the compliance evaluation.
        """,
    )
    category: Optional[str] = Field(
        default=None,
        title="Category",
        description="""
        The category of the compliance evaluation.
        """,
    )
    checks: Optional[List[Check]] = Field(
        default=None,
        title="Checks",
        description="""
        A list of checks performed during the compliance evaluation.
        """,
    )
    control: Optional[str] = Field(
        default=None,
        title="Control",
        description="""
        The specific control evaluated during the compliance check.
        """,
    )
    control_parameters: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Control Parameters",
        description="""
        Parameters associated with the control evaluated.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        A detailed description of the compliance evaluation.
        """,
    )
    requirements: Optional[List[str]] = Field(
        default=None,
        title="Requirements",
        description="""
        A list of requirements evaluated during the compliance check.
        """,
    )
    standards: Optional[List[str]] = Field(
        default=None,
        title="Standards",
        description="""
        A list of standards associated with the compliance evaluation.
        """,
    )
    status: Optional[str] = Field(
        default=None,
        title="Status",
        description="""
        The resultant status of the compliance evaluation normalized to the caption
        of the `status_id` field.
        """,
    )
    status_code: Optional[str] = Field(
        default=None,
        title="Status Code",
        description="""
        A code representing the status of the compliance evaluation.
        """,
    )
    status_details: Optional[str] = Field(
        default=None,
        title="Status Details",
        description="""
        Additional details about the status of the compliance evaluation.
        """,
    )
    status_id: Optional[int] = Field(
        default=None,
        title="Status ID",
        description="""
        0    Unknown
              The status is unknown.
        1    Pass
              The compliance check passed for all the evaluated resources.
        2    Warning
              The compliance check did not yield a result due to missing information.
        3    Fail
              The compliance check failed for at least one of the evaluated resources.
        99   Other
              The event status is not mapped. See the `status` attribute, which contains
              a data source specific value.
        """,
    )
