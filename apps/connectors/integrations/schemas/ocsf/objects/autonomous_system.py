from typing import Optional

from pydantic import BaseModel, Field


class AutonomousSystem(BaseModel):
    """https://schema.ocsf.io/1.4.0/objects/autonomous_system?extensions="""

    name: Optional[str] = Field(
        default=None,
        description="Organization name for the Autonomous System.",
    )
    number: Optional[int] = Field(
        default=None,
        description="Unique number that the AS is identified by.",
    )
