from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AgentType

from .policy import Policy


class Agent(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AgentType.set_values(values, "type_id", "type")

        return values

    name: Optional[str] = Field(
        default=None,
        title="Agent Name",
        description="The name of the agent or sensor. For example: 'AWS SSM Agent'.",
    )
    policies: Optional[List[Policy]] = Field(
        default=None,
        title="Agent Policies",
        description="Describes the various policies that may be applied or enforced by "
        "an agent or sensor, such as Conditional Access, auto-update, "
        "or tamper protection.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Agent Type",
        description="The normalized caption of the type_id value for the agent or sensor. "
        "In the case of 'Other' or 'Unknown', it is defined by the event source.",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The normalized representation of an agent or sensor. E.g., EDR,
        vulnerability management, APM, backup & recovery, etc.


        0	Unknown
            The type is unknown.
        1	Endpoint Detection and Response
            Any EDR sensor or agent. Or any tool that provides similar threat detection,
            anti-malware, anti-ransomware, or similar capabilities.
            E.g., Crowdstrike Falcon, Microsoft Defender for Endpoint, Wazuh.
        2	Data Loss Prevention
            Any DLP sensor or agent. Or any tool that provides similar data classification,
            data loss detection, and/or data loss prevention capabilities. E.g., Forcepoint DLP, Microsoft Purview, Symantec DLP.
        3	Backup & Recovery
            Any agent or sensor that provides backups, archival, or recovery capabilities.
            E.g., Azure Backup, AWS Backint Agent.
        4	Performance Monitoring & Observability
            Any agent or sensor that provides Application Performance Monitoring (APM),
            active tracing, profiling, or other observability use cases and optionally
            forwards the logs. E.g., New Relic Agent, Datadog Agent, Azure Monitor Agent.
        5	Vulnerability Management
            Any agent or sensor that provides vulnerability management or scanning
            capabilities. E.g., Qualys VMDR, Microsoft Defender for Endpoint,
            Crowdstrike Spotlight, Amazon Inspector Agent.
        6	Log Forwarding
            Any agent or sensor that forwards logs to a 3rd party storage system such as
            a data lake or SIEM. E.g., Splunk Universal Forwarder, Tenzir, FluentBit,
            Amazon CloudWatch Agent, Amazon Kinesis Agent.
        7	Mobile Device Management
            Any agent or sensor responsible for providing Mobile Device Management (MDM)
            or Mobile Enterprise Management (MEM) capabilities. E.g., JumpCloud Agent,
            Esper Agent, Jamf Pro binary.
        8	Configuration Management
            Any agent or sensor that provides configuration management of a device,
            such as scanning for software, license management, or applying configurations.
            E.g., AWS Systems Manager Agent, Flexera, ServiceNow MID Server.
        9	Remote Access
            Any agent or sensor that provides remote access capabilities to a device.
            E.g., BeyondTrust, Amazon Systems Manager Agent, Verkada Agent.
        99	Other
            The type is not mapped. See the type attribute, which contains a data source
            specific value.

        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Agent ID",
        description="The UID of the agent or sensor, sometimes known as a Sensor ID or `aid`.",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate Agent ID",
        description="An alternative or contextual identifier for the agent or sensor, "
        "such as a configuration, organization, or license UID.",
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Vendor Name",
        description="The company or author who created the agent or sensor. "
        "For example: 'Crowdstrike'.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Agent Version",
        description="The semantic version of the agent or sensor, e.g., '**********'.",
    )
