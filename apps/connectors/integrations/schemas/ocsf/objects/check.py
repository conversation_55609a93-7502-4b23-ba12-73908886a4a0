from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ComplianceStatus, Severity


class Check(BaseModel):
    """
    The check object defines a specific, testable compliance verification point that
    evaluates a target device against a standard, framework, or custom requirement.
    While checks are typically associated with formal standards (like CIS, NIST, or
    ISO), they can also represent custom or organizational requirements. When mapped
    to controls, checks can evaluate specific control_parameters to determine
    compliance status, but neither the control mapping nor control_parameters are
    required for a valid check.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Severity.set_values(values, "severity_id", "severity")
        ComplianceStatus.set_values(values, "status_id", "status")
        return values

    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The detailed description of the compliance check, explaining the security
        requirement, vulnerability, or configuration being assessed.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name or title of the compliance check.
        """,
    )
    severity: Optional[str] = Field(
        default=None,
        title="Severity",
        description="""
        The severity level as defined in the source document.
        """,
    )
    severity_id: Optional[int] = Field(
        default=None,
        title="Severity ID",
        description="""
        0    Unknown: The severity is unknown.
        1    Informational: Informational message. No action required.
        2    Low: The user decides if action is needed.
        3    Medium: Maps to CIS Benchmark 'Level 1' - Essential security settings
             recommended for all systems, or DISA STIG 'CAT III' - Action is required
             but the situation is not serious at this time.
        4    High: Maps to CIS Benchmark 'Level 2' - More restrictive and security-
             focused settings for sensitive environments, or DISA STIG 'CAT II' -
             Action is required immediately.
        5    Critical: Maps to DISA STIG 'CAT I' - Action is required immediately and
             the scope is broad.
        6    Fatal: An error occurred but it is too late to take remedial action.
        99   Other: The severity is not mapped. See the `severity` attribute, which
             contains a data source specific value.
        """,
    )
    standards: Optional[list[str]] = Field(
        default=None,
        title="Compliance Standards",
        description="""
        The regulatory or industry standard this check is associated with.
        """,
    )
    status: Optional[str] = Field(
        default=None,
        title="Status",
        description="""
        The resultant status of the compliance check normalized to the caption of
        the `status_id` field.
        """,
    )
    status_id: Optional[int] = Field(
        default=None,
        title="Status ID",
        description="""
            0    Unknown
                The status is unknown.
            1    Pass
                The compliance check passed for all the evaluated resources.
            2    Warning
                The compliance check did not yield a result due to missing information.
            3    Fail
                The compliance check failed for at least one of the evaluated resources.
            99   Other
                The event status is not mapped. See the status attribute, which contains
                a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="UID",
        description="""
        A unique identifier for the check object instance.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The version of the check definition.
        """,
    )
