from typing import List, Optional

from pydantic import BaseModel, Field

from .observation import Observation


class Baseline(BaseModel):
    """
    Describes the baseline or expected behavior of a system, service, or component based
    on historical observations and measurements. It establishes reference points for
    comparison to detect anomalies, trends, and deviations from typical patterns.
    """

    observation_parameter: Optional[str] = Field(
        default=None,
        title="Observation Parameter",
        description="""The specific parameter or property being monitored. Examples include: CPU usage
        percentage, API response time in milliseconds, HTTP error rate, memory utilization,
        network latency, transaction volume, etc.""",
    )
    observation_type: Optional[str] = Field(
        default=None,
        title="Observation Type",
        description="""The type of analysis being performed to establish baseline behavior. Common
        types include: Frequency Analysis, Time Pattern Analysis, Volume Analysis,
        Sequence Analysis, Distribution Analysis, etc.""",
    )
    observations: Optional[List[Observation]] = Field(
        default=None,
        title="Observations",
        description="""Collection of actual measured values, data points and observations recorded for
        this baseline.""",
    )
    observed_pattern: Optional[str] = Field(
        default=None,
        title="Observed Pattern",
        description="""The specific pattern identified within the observation type. For Frequency
        Analysis, this could be 'FREQUENT', 'INFREQUENT', 'RARE', or 'UNSEEN'. For Time Pattern
        Analysis, this could be 'BUSINESS_HOURS', 'OFF_HOURS', or 'UNUSUAL_TIME'. For Volume
        Analysis, this could be 'NORMAL_VOLUME', 'HIGH_VOLUME', or 'SURGE'. The pattern values
        are specific to each observation type and indicate the baseline behavior.""",
    )
