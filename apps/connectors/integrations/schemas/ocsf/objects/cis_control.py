from typing import Optional

from pydantic import BaseModel, Field


class CISControl(BaseModel):
    """
    The CIS Control (aka Critical Security Control) object describes a prioritized
    set of actions to protect your organization and data from cyber-attack vectors.
    The CIS Controls are defined by the Center for Internet Security.
    """

    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The CIS Control description.
        For example: Uninstall or disable unnecessary services on enterprise assets and software,
        such as an unused file sharing service, web application module, or service function.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The CIS Control name.
        For example: 4.8 Uninstall or Disable Unnecessary Services on Enterprise Assets and Software.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The CIS Control version. For example: v8.",
    )
