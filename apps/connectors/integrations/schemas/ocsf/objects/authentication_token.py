from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AuthenticationTokenType

from .encryption_details import EncryptionDetails


class AuthenticationToken(BaseModel):
    """
    The Authentication Token object contains the attributes pertaining to an authentication
    token, ticket, or assertion (e.g., Kerberos, OIDC, SAML).
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AuthenticationTokenType.set_values(values, "type_id", "type")
        return values

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time that the authentication token was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time that the authentication token was created.
        """,
    )
    encryption_details: Optional[EncryptionDetails] = Field(
        default=None,
        title="Encryption Details",
        description="""
        The encryption details of the authentication token.
        """,
    )
    expiration_time: Optional[int] = Field(
        default=None,
        title="Expiration Time",
        description="""
        The expiration time of the authentication token.
        """,
    )
    expiration_time_dt: Optional[datetime] = Field(
        default=None,
        title="Expiration Time",
        description="""
        The expiration time of the authentication token.
        """,
    )
    is_renewable: Optional[bool] = Field(
        default=None,
        title="Renewable",
        description="""
        Indicates whether the authentication token is renewable.
        """,
    )
    kerberos_flags: Optional[str] = Field(
        default=None,
        title="Kerberos Flags",
        description="""
        A bitmask, either in hexadecimal or decimal form, which encodes various attributes
        or permissions associated with a Kerberos ticket. These flags delineate specific
        characteristics of the ticket, such as its renewability or forwardability.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of the authentication token, normalized to the caption of the `type_id`
        value. In the case of 'Other', it is defined by the source.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The normalized authentication token type identifier.

        0    Unknown: The Authentication token type is unknown.
        1    Ticket Granting Ticket: Ticket Granting Ticket (TGT) for Kerberos.
        2    Service Ticket: Service Ticket (ST) for Kerberos.
        3    Identity Token: Identity (ID) Token for OIDC.
        4    Refresh Token: Refresh Token for OIDC.
        5    SAML Assertion: Authentication Assertion for SAML.
        99   Other: The type is not mapped. See the `type` attribute, which contains a data
             source specific value.
        """,
    )
