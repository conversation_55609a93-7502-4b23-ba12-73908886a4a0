from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .file import Fingerprint
from .san import SubjectAlternativeName


class DigitalCertificate(BaseModel):
    """
    The Digital Certificate, also known as a Public Key Certificate, object contains
    information about the ownership and usage of a public key. It serves as a means
    to establish trust in the authenticity and integrity of the public key and the
    associated entity.
    """

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="The time when the certificate was created.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The time when the certificate was created.",
    )
    expiration_time: Optional[int] = Field(
        default=None,
        title="Expiration Time",
        description="The expiration time of the certificate.",
    )
    expiration_time_dt: Optional[datetime] = Field(
        default=None,
        title="Expiration Time",
        description="The expiration time of the certificate.",
    )
    fingerprints: Optional[List[Fingerprint]] = Field(
        default=None,
        title="Fingerprints",
        description="The fingerprint list of the certificate.",
    )
    is_self_signed: Optional[bool] = Field(
        default=None,
        title="Certificate Self-Signed",
        description="Denotes whether a digital certificate is self-signed or signed by a known certificate authority (CA).",
    )
    issuer: Optional[str] = Field(
        default=None,
        title="Issuer Distinguished Name",
        description="The certificate issuer distinguished name.",
    )
    sans: Optional[List[SubjectAlternativeName]] = Field(
        default=None,
        title="Subject Alternative Names",
        description="The list of subject alternative names that are secured by a specific certificate.",
    )
    serial_number: Optional[str] = Field(
        default=None,
        title="Certificate Serial Number",
        description="The serial number of the certificate used to create the digital signature.",
    )
    subject: Optional[str] = Field(
        default=None,
        title="Subject Distinguished Name",
        description="The certificate subject distinguished name.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the certificate.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The certificate version.",
    )
