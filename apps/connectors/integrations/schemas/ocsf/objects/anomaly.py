from typing import List, Optional

from pydantic import BaseModel, Field

from .observation import Observation


class Anomaly(BaseModel):
    """
    Describes an anomaly or deviation detected in a system. Anomalies are unexpected
    activity patterns that could indicate potential issues needing attention.
    """

    observation_parameter: Optional[str] = Field(
        default=None,
        title="Observation Parameter",
        description="""The specific parameter, metric or property where the anomaly was observed.
        Examples include: CPU usage percentage, API response time in milliseconds, HTTP error
        rate, memory utilization, network latency, transaction volume, etc. This helps
        identify the exact aspect of the system exhibiting anomalous behavior.""",
    )
    observation_type: Optional[str] = Field(
        default=None,
        title="Observation Type",
        description="""The type of analysis methodology used to detect the anomaly. This indicates
        how the anomaly was identified through different analytical approaches. Common types
        include: Frequency Analysis, Time Pattern Analysis, Volume Analysis, Sequence
        Analysis, Distribution Analysis, etc.""",
    )
    observations: Optional[List[Observation]] = Field(
        default=None,
        title="Observations",
        description="""Details about the observed anomaly or observations that were flagged as
        anomalous compared to expected baseline behavior.""",
    )
    observed_pattern: Optional[str] = Field(
        default=None,
        title="Observed Pattern",
        description="""The specific pattern identified within the observation type. For Frequency
        Analysis, this could be 'FREQUENT', 'INFREQUENT', 'RARE', or 'UNSEEN'. For Time Pattern
        Analysis, this could be 'BUSINESS_HOURS', 'OFF_HOURS', or 'UNUSUAL_TIME'. For Volume
        Analysis, this could be 'NORMAL_VOLUME', 'HIGH_VOLUME', or 'SURGE'. The pattern values
        are specific to each observation type and indicate how the observed behavior relates
        to the baseline.""",
    )
