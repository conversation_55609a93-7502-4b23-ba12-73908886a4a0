from typing import Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.objects.account import Account


class Cloud(BaseModel):
    account: Optional[Account] = Field(
        default=None,
        title="Account",
        description="The account information.",
    )
    region: Optional[str] = Field(
        default=None,
        title="Region",
        description="The region of the cloud service.",
    )
    provider: Optional[str] = Field(
        default=None,
        description="The cloud service provider.",
        title="Cloud Provider",
    )
