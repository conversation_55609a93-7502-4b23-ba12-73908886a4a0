from typing import List, Optional

from pydantic import BaseModel, Field

from .authorization_result import AuthorizationResult
from .session import Session
from .user import User


class Actor(BaseModel):
    app_name: Optional[str] = Field(
        default=None,
        title="Application Name",
        description="The client application or service that initiated the activity.  "
        "This can be in conjunction with the user if present. Note that app_name is distinct "
        "from the process if present.",
    )
    app_uid: Optional[str] = Field(
        default=None,
        title="Application ID",
        description="The unique identifier for the application or service that initiated the activity.",
    )
    authorizations: Optional[List[AuthorizationResult]] = Field(
        default=None,
        title="Authorization Information",
        description="Provides details about an authorization, such as authorization outcome, and any associated policies related to the activity/event.",
    )
    session: Optional[Session] = Field(
        default=None,
        title="Session",
        description="The user session from which the activity was initiated.",
    )
    user: Optional[User] = Field(
        default=None,
        title="User",
        description="The user that initiated the activity or the user context from which the activity was initiated.",
    )
