from typing import Optional

from pydantic import BaseModel, Field


class ClassifierDetails(BaseModel):
    """
    The Classifier Details object describes details about the classifier used for data classification.
    """

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The name of the classifier.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The type of the classifier.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the classifier.",
    )
