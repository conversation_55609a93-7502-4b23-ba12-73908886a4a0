from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AnalyticType


class Analytic(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AnalyticType.set_values(values, "type_id", "type")

        return values

    category: Optional[str] = Field(
        default=None,
        description="The analytic category.",
    )
    desc: Optional[str] = Field(
        default=None,
        description="The description of the analytic that generated the finding.",
    )
    name: str = Field(
        default_factory=str,
        description="The name of the analytic that generated the finding.",
    )
    related_analytics: Optional[List["Analytic"]] = Field(
        default=None,
        description="Other analytics related to this analytic.",
    )
    type: Optional[str] = Field(
        default=None,
        description="The analytic type.",
    )
    type_id: Optional[int] = Field(
        default=None,
        description="The analytic type ID.",
    )
    uid: str = Field(
        default_factory=str,
        description="The unique identifier of the analytic.",
    )
    version: Optional[str] = Field(
        default=None,
        description="The analytic version. For example: 1.1.",
    )


Analytic.model_rebuild()
