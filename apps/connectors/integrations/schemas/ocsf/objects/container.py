from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .file import Fingerprint
from .image import Image
from .key_value_object import KeyValueObject


class Container(BaseModel):
    """
    The Container object describes an instance of a specific container.
    A container is a prepackaged, portable system image that runs isolated
    on an existing system using a container runtime like containerd.
    """

    hash: Optional[Fingerprint] = Field(
        default=None,
        title="Hash",
        description="""Commit hash of image created for docker or the SHA256 hash of the container.
        For example: `13550340a8681c84c861aac2e5b440161c2b33a3e4f302ac680ca5b686de48de`.
        """,
    )
    image: Optional[Image] = Field(
        default=None,
        title="Image",
        description="The container image used as a template to run the container.",
    )
    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="The list of labels associated to the container.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The container name.",
    )
    network_driver: Optional[str] = Field(
        default=None,
        title="Network Driver",
        description="""The network driver used by the container.
        For example, bridge, overlay, host, none, etc.
        """,
    )
    orchestrator: Optional[str] = Field(
        default=None,
        title="Orchestrator",
        description="The orchestrator managing the container, such as ECS, EKS, K8s, or OpenShift.",
    )
    pod_uuid: Optional[UUID] = Field(
        default=None,
        title="Pod UUID",
        description="""The unique identifier of the pod (or equivalent) that the container
        is executing on.""",
    )
    runtime: Optional[str] = Field(
        default=None,
        title="Runtime",
        description="The backend running the container, such as containerd or cri-o.",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="The size of the container image.",
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="The list of tags; `{key:value}` pairs associated to the container.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""The full container unique identifier for this instantiation of the container.
        For example: `ac2ea168264a08f9aaca0dfc82ff3551418dfd22d02b713142a6843caa2f61bf`.""",
    )
