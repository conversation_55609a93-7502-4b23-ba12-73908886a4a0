from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AuthenticationFactorType

from .device import Device


class AuthenticationFactor(BaseModel):
    """
    Describes a category of methods used for identity verification in an authentication
    attempt.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AuthenticationFactorType.set_values(values, "factor_type_id", "factor_type")
        return values

    device: Optional[Device] = Field(
        default=None,
        title="Device",
        description="""
        Device used to complete an authentication request.
        """,
    )
    email_addr: Optional[str] = Field(
        default=None,
        title="Email Address",
        description="""
        The email address used in an email-based authentication factor.
        """,
    )
    factor_type: Optional[str] = Field(
        default=None,
        title="Factor Type",
        description="""
        The type of authentication factor used in an authentication attempt, normalized to
        the caption of the `factor_type_id` value. In the case of 'Other', it is defined by
        the source.
        """,
    )
    factor_type_id: Optional[int] = Field(
        default=None,
        title="Factor Type ID",
        description="""
        The normalized identifier for the authentication factor.

        0    Unknown: The authentication factor is unknown.
        1    SMS: User receives and inputs a code sent to their mobile device via SMS text
             message.
        2    Security Question: The user responds to a security question as part of a
             question-based authentication factor.
        3    Phone Call: System calls the user's registered phone number and requires the
             user to answer and provide a response.
        4    Biometric: Devices that verify identity based on user's physical identifiers,
             such as fingerprint scanners or retina scanners.
        5    Push Notification: Push notification is sent to user's registered device and
             requires the user to acknowledge.
        6    Hardware Token: Physical device that generates a code to be used for
             authentication.
        7    OTP: Application generates a one-time password (OTP) for use in authentication.
        8    Email: A code or link is sent to a user's registered email address.
        9    U2F: Typically involves a hardware token, which the user physically interacts
             with to authenticate.
        10   WebAuthn: Web-based API that enables users to register devices as authentication
             factors.
        11   Password: The user enters a password that they have previously established.
        99   Other: The authentication factor is not mapped. See the `factor_type` attribute,
             which contains a data source specific value.
        """,
    )
    is_hotp: Optional[bool] = Field(
        default=None,
        title="HMAC-based One-time Password (HOTP)",
        description="""
        Whether the authentication factor is an HMAC-based One-time Password (HOTP).
        """,
    )
    is_totp: Optional[bool] = Field(
        default=None,
        title="Time-based One-time Password (TOTP)",
        description="""
        Whether the authentication factor is a Time-based One-time Password (TOTP).
        """,
    )
    phone_number: Optional[str] = Field(
        default=None,
        title="Phone Number",
        description="""
        The phone number used for a telephony-based authentication request.
        """,
    )
    provider: Optional[str] = Field(
        default=None,
        title="Provider",
        description="""
        The name of provider for an authentication factor.
        """,
    )
    security_questions: Optional[list[str]] = Field(
        default=None,
        title="Security Questions",
        description="""
        The question(s) provided to user for a question-based authentication factor.
        """,
    )
