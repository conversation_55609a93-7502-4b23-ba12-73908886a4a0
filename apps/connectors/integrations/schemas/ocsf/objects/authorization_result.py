from typing import Optional

from pydantic import BaseModel, Field

from .policy import Policy


class AuthorizationResult(BaseModel):
    decision: Optional[str] = Field(
        default=None,
        title="Authorization Decision/Outcome",
        description="Authorization Result/outcome, e.g. allowed, denied",
    )
    policy: Optional[Policy] = Field(
        default=None,
        title="Policy",
        description="Details about the identity/Access management policies that are applicable",
    )
