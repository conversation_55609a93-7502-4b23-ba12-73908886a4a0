from typing import Optional

from pydantic import BaseModel, Field

from .file import File
from .remediation import Remediation
from .rule import Rule
from .user import User


class AffectedCode(BaseModel):
    """
    The Affected Code object describes details about a code block identified as
    vulnerable.
    """

    end_column: Optional[int] = Field(
        default=None,
        title="End Column",
        description="The column number of the last part of the assessed code identified as vulnerable.",
    )
    end_line: Optional[int] = Field(
        default=None,
        title="End Line",
        description="The line number of the last line of code block identified as vulnerable.",
    )
    file: Optional[File] = Field(
        default=None,
        title="File",
        description="Details about the file that contains the affected code block.",
    )
    owner: Optional[User] = Field(
        default=None,
        title="Owner",
        description="Details about the user that owns the affected file.",
    )
    remediation: Optional[Remediation] = Field(
        default=None,
        title="Remediation Guidance",
        description="Describes the recommended remediation steps to address identified issue(s).",
    )
    rule: Optional[Rule] = Field(
        default=None,
        title="Related Rule",
        description="Details about the specific rule, e.g., those defined as part of a larger `policy`, that triggered the finding.",
    )
    start_column: Optional[int] = Field(
        default=None,
        title="Start Column",
        description="The column number of the first part of the assessed code identified as vulnerable.",
    )
    start_line: Optional[int] = Field(
        default=None,
        title="Start Line",
        description="The line number of the first line of code block identified as vulnerable.",
    )
