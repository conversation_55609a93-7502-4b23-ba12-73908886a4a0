from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import InstallState

from .cve import CVE
from .cwe import CWE
from .operating_system import OperatingSystem
from .product import Product
from .time_span import TimeSpan


class Advisory(BaseModel):
    """
    The Advisory object represents publicly disclosed cybersecurity vulnerabilities defined
    in a Security advisory.
    e.g. Microsoft KB Article, Apple Security Advisory, or a GitHub Security Advisory (GHSA)
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        InstallState.set_values(values, "install_state_id", "install_state")
        return values

    avg_timespan: Optional[TimeSpan] = Field(
        default=None,
        title="Average Timespan",
        description="The average time to patch.",
    )
    bulletin: Optional[str] = Field(
        default=None,
        title="Patch Bulletin",
        description="The Advisory bulletin identifier.",
    )
    classification: Optional[str] = Field(
        default=None,
        title="Classification",
        description="The vendors classification of the Advisory.",
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="The time when the Advisory record was created.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The time when the Advisory record was created.",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="A brief description of the Advisory Record.",
    )
    install_state: Optional[str] = Field(
        default=None,
        title="Install State",
        description="The install state of the Advisory.",
    )
    install_state_id: Optional[int] = Field(
        default=None,
        title="Install State ID",
        description="""
        The normalized install state ID of the Advisory.

            0    Unknown: The normalized install state is unknown.
            1    Installed: The item is installed.
            2    Not Installed: The item is not installed.
            3    Installed Pending Reboot: The item is installed pending reboot operation.
            99    Other: The install state is not mapped.
        """,
    )
    is_superseded: Optional[bool] = Field(
        default=None,
        title="The patch is superseded.",
        description="The Advisory has been replaced by another.",
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="The time when the Advisory record was last updated.",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="The time when the Advisory record was last updated.",
    )
    os: Optional[OperatingSystem] = Field(
        default=None,
        title="OS",
        description="The operating system the Advisory applies to.",
    )
    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="The product where the vulnerability was discovered.",
    )
    references: Optional[List[str]] = Field(
        default=None,
        title="References",
        description="""
        A list of reference URLs with additional information about the vulnerabilities
        disclosed in the Advisory.
        """,
    )
    related_cves: Optional[List[CVE]] = Field(
        default=None,
        title="Related CVEs",
        description="A list of Common Vulnerabilities and Exposures (CVE) identifiers related to the vulnerabilities disclosed in the Advisory.",
    )
    related_cwes: Optional[List[CWE]] = Field(
        default=None,
        title="Related CWEs",
        description="A list of Common Weakness Enumeration (CWE) identifiers related to the vulnerabilities disclosed in the Advisory.",
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="The size in bytes for the Advisory. Usually populated for a KB Article patch.",
    )
    src_url: Optional[str] = Field(
        default=None,
        title="Source URL",
        description="The Advisory link from the source vendor.",
    )
    title: Optional[str] = Field(
        default=None,
        title="Title",
        description="A title or a brief phrase summarizing the Advisory.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Advisory ID",
        description="The unique identifier assigned to the advisory or disclosed vulnerability, e.g, GHSA-5mrr-rgp6-x4gr.",
    )
