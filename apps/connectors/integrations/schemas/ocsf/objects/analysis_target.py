from typing import Optional

from pydantic import BaseModel, Field


class AnalysisTarget(BaseModel):
    """
    The analysis target defines the scope of monitored activities, specifying what
    entity, system or process is analyzed for activity patterns.
    """

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The specific name or identifier of the analysis target, such as the username
        of a User Account, the name of a Kubernetes Cluster, the identifier of a Network
        Namespace, or the name of an Application Component.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""The category of the analysis target, such as User Account, Kubernetes Cluster,
        Network Namespace, or Application Component.""",
    )
