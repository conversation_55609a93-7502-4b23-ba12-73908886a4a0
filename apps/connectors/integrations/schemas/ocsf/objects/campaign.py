from typing import Optional

from pydantic import BaseModel, Field


class Campaign(BaseModel):
    """
    Represents organized efforts by threat actors to achieve malicious objectives over a
    period, often characterized by shared tactics, techniques, and procedures (TTPs).
    """

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of a specific campaign associated with a cyber threat.
        """,
    )
