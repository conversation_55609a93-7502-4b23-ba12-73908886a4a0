from typing import Optional

from pydantic import BaseModel, Field

from .policy import Policy


class Assessment(BaseModel):
    """
    Describes a point-in-time assessment, check, or evaluation of a specific configuration
    or signal against an asset, entity, person, or otherwise. For example, this can
    encapsulate `os_signals` from CrowdStrike Falcon Zero Trust Assessments, or account
    for `Datastore` configurations from Cyera, or capture details of Microsoft Intune
    configuration policies.
    """

    category: Optional[str] = Field(
        default=None,
        title="Category",
        description="""
        The category that the assessment is part of. For example: `Prevention` or
        `Windows 10`.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The description of the assessment criteria, or a description of the specific
        configuration or signal the assessment is targeting.
        """,
    )
    meets_criteria: Optional[bool] = Field(
        default=None,
        title="Meets Criteria",
        description="""
        Determines whether the assessment against the specific configuration or signal
        meets the assessments criteria. For example, if the assessment checks if a
        `Datastore` is encrypted or not, having encryption would be evaluated as `true`.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the configuration or signal being assessed. For example: `Kernel
        Mode Code Integrity (KMCI)` or `publicAccessibilityState`.
        """,
    )
    policy: Optional[Policy] = Field(
        default=None,
        title="Assessment Policy",
        description="""
        The details of any policy associated with an assessment.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique identifier of the configuration or signal being assessed. For example:
        the `signal_id`.
        """,
    )
