from typing import Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects.data_classification import (
    DataClassification,
)


class DataClassificationProfile(BaseModel):
    NAME: str = Profile.DATA_CLASSIFICATION

    data_classifications: Optional[DataClassification] = Field(
        default=None,
        title="Data Classification",
        description="""
        A list of Data Classification objects, that include information about data classification
        levels and data category types, indentified by a classifier.
        """,
    )
