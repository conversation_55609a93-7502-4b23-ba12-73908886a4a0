from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects import (
    HttpRequest,
    HttpResponse,
    NetworkConnectionInfo,
    NetworkProxyEndpoint,
    NetworkTraffic,
    TransportLayerSecurity,
)


class NetworkProxyProfile(BaseModel):
    NAME: ClassVar[str] = Profile.NETWORK_PROXY

    proxy_connection_info: Optional[NetworkConnectionInfo] = Field(
        default=None,
        title="Proxy Connection Information",
        description="""
        The connection information from the proxy server to the remote server.
        """,
    )
    proxy_endpoint: Optional[NetworkProxyEndpoint] = Field(
        default=None,
        title="Proxy Endpoint",
        description="""
        The proxy (server) in a network connection.
        """,
    )
    proxy_http_request: Optional[HttpRequest] = Field(
        default=None,
        title="Proxy HTTP Request",
        description="""
        The HTTP Request from the proxy server to the remote server.
        """,
    )
    proxy_http_response: Optional[HttpResponse] = Field(
        default=None,
        title="Proxy HTTP Response",
        description="""
        The HTTP Response from the remote server to the proxy server.
        """,
    )
    proxy_tls: Optional[TransportLayerSecurity] = Field(
        default=None,
        title="Proxy TLS",
        description="""
        The TLS protocol negotiated between the proxy server and the remote server.
        """,
    )
    proxy_traffic: Optional[NetworkTraffic] = Field(
        default=None,
        title="Proxy Traffic",
        description="""
        The network traffic refers to the amount of data moving across a network,
        from proxy to remote server at a given point of time.
        """,
    )
