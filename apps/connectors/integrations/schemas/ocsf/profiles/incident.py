from typing import ClassVar, List, Optional

from pydantic import BaseModel, Field, HttpUrl, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    Impact,
    Priority,
    Profile,
    Verdict,
)
from apps.connectors.integrations.schemas.ocsf.objects import Group, Ticket, User


class IncidentProfile(BaseModel):
    NAME: ClassVar[str] = Profile.INCIDENT

    @model_validator(mode="before")
    def _set_incident_profile_fields(cls, values):
        Impact.set_values(values, "impact_id", "impact")
        Priority.set_values(values, "priority_id", "priority")
        Verdict.set_values(values, "verdict_id", "verdict")

        return values

    assignee: Optional[User] = Field(
        default=None,
        title="Assignee",
        description="The details of the user assigned to an Incident.",
    )
    assignee_group: Optional[Group] = Field(
        default=None,
        title="Assignee Group",
        description="The details of the group assigned to an Incident.",
    )
    impact: Optional[str] = Field(
        default=None,
        title="Impact",
        description="The impact , normalized to the caption of the impact_id value. "
        "In the case of 'Other', it is defined by the event source.",
    )
    impact_id: Optional[int] = Field(
        default=None,
        title="Impact ID",
        description="The normalized impact of the incident or finding. Per NIST, this is the "
        "magnitude of harm that can be expected to result from the consequences of "
        "unauthorized disclosure, modification, destruction, or loss of information "
        "or information system availability.",
    )
    impact_score: Optional[int] = Field(
        default=None,
        title="Impact Score",
        description="The impact as an integer value of the finding, valid range 0-100.",
    )
    is_suspected_breach: Optional[bool] = Field(
        default=None,
        title="Suspected Breach",
        description="A determination based on analytics as to whether a potential breach was found.",
    )
    priority: Optional[str] = Field(
        default=None,
        title="Priority",
        description="The priority, normalized to the caption of the priority_id value. "
        "In the case of 'Other', it is defined by the event source.",
    )
    priority_id: Optional[int] = Field(
        default=None,
        title="Priority ID",
        description="The normalized priority. Priority identifies the relative importance of the incident "
        "or finding. It is a measurement of urgency.",
    )
    src_url: Optional[HttpUrl] = Field(
        default=None,
        title="Source URL",
        description="A Url link used to access the original incident.",
    )
    tickets: Optional[List[Ticket]] = Field(
        default=None,
        title="Tickets",
        description="The linked ticket(s) in the ticketing system.",
    )
    verdict: Optional[str] = Field(
        default=None,
        title="Verdict",
        description="The verdict assigned to an Incident finding.",
    )
    verdict_id: Optional[int] = Field(
        default=None,
        title="Verdict ID",
        description="The normalized verdict of an Incident.",
    )
