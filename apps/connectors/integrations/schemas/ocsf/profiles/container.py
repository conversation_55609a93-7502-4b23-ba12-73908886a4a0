from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects.container import Container


class ContainerProfile(BaseModel):
    NAME: ClassVar[str] = Profile.CONTAINER

    container: Optional[Container] = Field(
        default=None,
        title="Container",
        description="""
        The information describing an instance of a container.
        A container is a prepackaged, portable system image that runs
        isolated on an existing system using a container runtime like containerd.
        """,
    )
    namespace_pid: Optional[int] = Field(
        default=None,
        title="Namespace PID",
        description="""
        If running under a process namespace (such as in a container),
        the process identifier within that process namespace.
        """,
    )
