from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects import OSINT


class OSINTProfile(BaseModel):
    NAME: ClassVar[str] = Profile.OSINT

    osint: Optional[list[OSINT]] = Field(
        default=None,
        description="""
        The OSINT (Open Source Intelligence) object contains details related to an
        indicator such as the indicator itself, related indicators, geolocation,
        registrar information, subdomains, analyst commentary, and other contextual
        information. This information can be used to further enrich a detection or
        finding by providing decisioning support to other analysts and engineers.
        """,
    )
