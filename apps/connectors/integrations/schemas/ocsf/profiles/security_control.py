from typing import ClassV<PERSON>, List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    ControlAction,
    Disposition,
    Profile,
)
from apps.connectors.integrations.schemas.ocsf.objects import (
    FirewallRule,
    MitreAttack,
    Policy,
)


class SecurityControlProfile(BaseModel):
    NAME: ClassVar[str] = Profile.SECURITY_CONTROL

    @model_validator(mode="before")
    def _set_security_control_profile_fields(cls, values):
        ControlAction.set_values(values, "action_id", "action")
        Disposition.set_values(values, "disposition_id", "disposition")
        return values

    action: Optional[str] = Field(
        default=None,
        title="Action",
        description="The normalized caption of action_id.",
    )
    action_id: Optional[int] = Field(
        default=None,
        title="Action ID",
        description="The action taken by a control or other policy-based system leading to an outcome or disposition.",
    )
    attacks: Optional[List[MitreAttack]] = Field(
        default=None,
        title="MITRE ATT&CK® Details",
        description="An array of MITRE ATT&CK® objects describing identified tactics, techniques & sub-techniques.",
    )
    # authorizations: Optional[List[Authorization]] = Field(
    #     default=None,
    #     title="Authorization Information",
    #     description="Details about an authorization, such as outcome and policies related to the activity/event.",
    # )
    disposition: Optional[str] = Field(
        default=None,
        title="Disposition",
        description="""
        The disposition name, normalized to the caption of the disposition_id value.
        In the case of 'Other', it is defined by the event source.
        """,
    )
    disposition_id: Optional[int] = Field(
        default=None,
        title="Disposition ID",
        description="""
        Describes the outcome or action taken by a security control, such as
        access control checks, malware detections or various types of policy violations.
        0	Unknown
            The disposition is unknown.
        1	Allowed
            Granted access or allowed the action to the protected resource.
        2	Blocked
            Denied access or blocked the action to the protected resource.
        3	Quarantined
            A suspicious file or other content was moved to a benign location.
        4	Isolated
            A session was isolated on the network or within a browser.
        5	Deleted
            A file or other content was deleted.
        6	Dropped
            The request was detected as a threat and resulted in the connection being
            dropped.
        7	Custom Action
            A custom action was executed such as running of a command script. Use the
            message attribute of the base class for details.
        8	Approved
            A request or submission was approved. For example, when a form was properly
            filled out and submitted. This is distinct from 1 'Allowed'.
        9	Restored
            A quarantined file or other content was restored to its original location.
        10	Exonerated
            A suspicious or risky entity was deemed to no longer be suspicious (re-scored).
        11	Corrected
            A corrupt file or configuration was corrected.
        12	Partially Corrected
            A corrupt file or configuration was partially corrected.
        13	Uncorrected
            A corrupt file or configuration was not corrected.
        14	Delayed
            An operation was delayed, for example if a restart was required to finish
            the operation.
        15	Detected
            Suspicious activity or a policy violation was detected without further action.
        16	No Action
            The outcome of an operation had no action taken.
        17	Logged
            The operation or action was logged without further action.
        18	Tagged
            A file or other entity was marked with extended attributes.
        19	Alert
            The request or activity was detected as a threat and resulted in a
            notification but request was not blocked.
        20	Count
            Counted the request or activity but did not determine whether to allow it
            or block it.
        21	Reset
            The request was detected as a threat and resulted in the connection being reset.
        22	Captcha
            Required the end user to solve a CAPTCHA puzzle to prove that a human being
            is sending the request.
        23	Challenge
            Ran a silent challenge that required the client session to verify that it's
            a browser, and not a bot.
        24	Access Revoked
            The requestor's access has been revoked due to security policy enforcements.
            Note: use the Host profile if the User or Actor requestor is not present in the event class.
        25	Rejected
            A request or submission was rejected. For example, when a form was improperly
            filled out and submitted. This is distinct from 2 'Blocked'.
        26	Unauthorized
            An attempt to access a resource was denied due to an authorization check
            that failed. This is a more specific disposition than 2 'Blocked' and can be complemented with the authorizations attribute for more detail.
        27	Error
            An error occurred during the processing of the activity or request. Use the
            message attribute of the base class for details.
        99	Other
            The disposition is not mapped. See the disposition attribute, which contains
            a data source specific value.
        """,
    )
    firewall_rule: Optional[FirewallRule] = Field(
        default=None,
        title="Firewall Rule",
        description="The firewall rule that pertains to the control that triggered the event.",
    )
    policy: Optional[Policy] = Field(
        default=None,
        title="Policy",
        description="""The policy that pertains to the control that triggered the event, if applicable. For example the name of an anti-malware policy or an access control policy.""",
    )
    is_alert: Optional[bool] = Field(
        default=None,
        title="Alert",
        description="""
        Indicates that the event is considered to be an alertable signal. For example,
        an activity_id of 'Create' could constitute an alertable signal and the value
        would be true, while 'Close' likely would not and either omit the attribute or
        set its value to false. Note that other events with the security_control profile
        may also be deemed alertable signals and may also carry is_alert = true attributes.
        """,
    )
    # malware: Optional[List[Malware]] = Field(
    #     default=None,
    #     title="Malware",
    #     description="A list of Malware objects, describing details about the identified malware.",
    # )
    # policy: Optional[Policy] = Field(
    #     None,
    #     title="Policy",
    #     description="The policy that pertains to the control that triggered the event.",
    # )
    risk_details: Optional[str] = Field(
        default=None,
        title="Risk Details",
        description="Describes the risk associated with the finding.",
    )
    risk_level: Optional[str] = Field(
        default=None,
        title="Risk Level",
        description="The risk level, normalized to the caption of the risk_level_id value.",
    )
    risk_level_id: Optional[int] = Field(
        default=None,
        title="Risk Level ID",
        description="The normalized risk level id.",
    )
    risk_score: Optional[int] = Field(
        default=None,
        title="Risk Score",
        description="The risk score as reported by the event source.",
    )
