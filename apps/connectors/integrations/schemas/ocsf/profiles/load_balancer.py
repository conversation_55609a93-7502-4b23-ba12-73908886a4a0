from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects.load_balancer import LoadBalancer


class LoadBalancerProfile(BaseModel):
    NAME: ClassVar[str] = Profile.LOAD_BALANCER

    load_balancer: Optional[LoadBalancer] = Field(
        default=None,
        title="Load Balancer",
        description="""
        The Load Balancer object contains information related to the device that is
        distributing incoming traffic to specified destinations.
        """,
    )
