from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects import Actor, Device


class HostProfile(BaseModel):
    NAME: ClassVar[str] = Profile.HOST

    actor: Optional[Actor] = Field(
        default=None,
        title="Actor",
        description="The actor object describes details about the user/role/process that was "
        "the source of the activity. Note that this is not the threat actor of a campaign but "
        "may be part of a campaign.",
    )
    device: Optional[Device] = Field(
        default=None,
        title="Device",
        description="An addressable device, computer system or host",
    )
