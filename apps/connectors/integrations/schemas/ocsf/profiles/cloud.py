from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.enums import Profile
from apps.connectors.integrations.schemas.ocsf.objects import (
    Api,
    Cloud,
)


class CloudProfile(BaseModel):
    NAME: ClassVar[str] = Profile.CLOUD

    api: Optional[Api] = Field(
        default=None,
        title="API Details",
        description="""
        Describes details about a typical API (Application Programming Interface) call.
        """,
    )
    cloud: Optional[Cloud] = Field(
        default=None,
        title="Cloud",
        description="""
        Describes details about the Cloud environment where the event was originally
        created or logged.
        """,
    )
