import re
from enum import StrEnum, auto
from typing import Optional

from pydantic import BaseModel, ConfigDict


class HostType(StrEnum):
    SERVER = auto()
    WORKSTATION = auto()
    MOBILE = auto()
    CONTAINER = auto()
    OTHER = auto()
    UNKNOWN = auto()

    @classmethod
    def from_string(cls, value: str):
        try:
            return cls[value.upper()]
        except KeyError:
            return cls.UNKNOWN

    @classmethod
    def from_os_name(cls, os: str):
        """
        Usually there is not enough information to determine the host type, so we
        default to an UNKNOWN host type. We can later inspect hosts with UNKNOWN host type and try to make this
        method better.
        """
        if not os:
            return cls.UNKNOWN

        os = os.lower()

        if "printer" in os or "camera" in os:
            return cls.OTHER

        if "windows" in os:
            if "server" in os:
                return cls.SERVER
            return cls.WORKSTATION
        elif "linux" in os:
            return cls.SERVER
        elif "mac" in os:
            return cls.WORKSTATION
        elif any(mobile_os in os for mobile_os in ["android", "ios", "iphone", "ipad"]):
            return cls.MOBILE
        else:
            return cls.OTHER


class OsFamily(StrEnum):
    WINDOWS = auto()
    LINUX = auto()
    MAC = auto()
    ANDROID = auto()
    IOS = auto()
    UNKNOWN = auto()

    @classmethod
    def from_string(
        cls,
        packed_os_string: str,
        os_family_map: dict[str | int, "OsFamily"] | None = None,
    ):
        """
        Extract OS family and name from the OS string.
        """
        if os_family_map is None:
            if not packed_os_string:
                return OsFamily.UNKNOWN, packed_os_string
            os_family_map = OS_FAMILY_MAP
        else:
            if not packed_os_string:
                return (
                    os_family_map.get(packed_os_string, OsFamily.UNKNOWN),
                    packed_os_string,
                )

        if packed_os_string in os_family_map:
            return os_family_map[packed_os_string], packed_os_string

        # First look for a known platform string in the packed OS string (w/ no spaces).
        packed_os_string = packed_os_string.strip()
        os_string_lower = packed_os_string.lower()
        if os_string_lower in os_family_map:
            return os_family_map[os_string_lower], packed_os_string

        # Unpack the OS string by adding spaces between title
        # case words and between words and numbers.
        os_string = re.sub("([A-Z]+)", r" \1", packed_os_string)
        os_string = re.sub("([A-Z][a-z]+)", r" \1", os_string)
        os_string = re.sub(r"(\d+)", r" \1 ", os_string)
        # Don't split a single letter followed by any number
        # for version and release numbers(e.g. v11, R2)
        os_string = re.sub(r"(?<=\b[A-Za-z]) (?=\d+\b)", "", os_string)
        # Don't split a numbers followed by dots for version numbers (e.g 18.04)
        os_string = re.sub(r"(\d+) \. (\d+)", r"\1.\2", os_string)

        os_parts = os_string.split()

        # Next look for a known platform string in the unpacked OS string.
        os_parts_lower = [p.lower() for p in os_parts]

        # if os contains "cisco", map it to UNKNOWN
        # this is so we don't accidentally map "cisco ios" to iOS
        if "cisco" in os_parts_lower:
            return OsFamily.UNKNOWN, " ".join(os_parts)

        os_family = next(
            (os_family_map[os] for os in os_family_map if os in os_parts_lower),
            OsFamily.UNKNOWN,
        )

        return os_family, " ".join(os_parts)


OS_FAMILY_MAP = {
    "windows": OsFamily.WINDOWS,
    "windows_legacy": OsFamily.WINDOWS,
    "linux": OsFamily.LINUX,
    "centos": OsFamily.LINUX,
    "debian": OsFamily.LINUX,
    "fedora": OsFamily.LINUX,
    "redhat": OsFamily.LINUX,
    "ubuntu": OsFamily.LINUX,
    "mac": OsFamily.MAC,
    "macos": OsFamily.MAC,
    "ios": OsFamily.IOS,
    "appleios": OsFamily.IOS,
    "android": OsFamily.ANDROID,
}

OS_FAMILY_NAME = {
    OsFamily.WINDOWS: "Windows",
    OsFamily.LINUX: "Linux",
    OsFamily.MAC: "MAC",
    OsFamily.ANDROID: "Android",
    OsFamily.IOS: "iOS",
}


class OsAttributes(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    family: OsFamily
    name: Optional[str]
    host_type: HostType
