from typing import Generic, List, TypeVar

from pydantic import BaseModel

from apps.connectors.integrations.schemas import TAPResult, ocsf

T = TypeVar("T", bound=BaseModel)


class BaseQueryResult(TAPResult[List[T]], Generic[T]):
    ...


class UntypedQueryResult(BaseQueryResult[dict]):
    """
    Before using this class, seriously consider whether you can use
    a typed result class instead.
    """

    ...


class NetworkActivityQueryResult(BaseQueryResult[ocsf.NetworkActivity]):
    ...


class EmailActivityQueryResult(BaseQueryResult[ocsf.EmailActivity]):
    ...
