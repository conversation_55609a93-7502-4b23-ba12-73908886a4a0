from pydantic import BaseModel, Field


class IntegrationActionPollingContext(BaseModel):
    context: dict = Field(
        ...,
        description="Opaque data required for polling the asynchronous action result. "
        "Clients should treat this data as a black box and must pass it unchanged "
        "in subsequent polling requests. The structure of this object depends entirely "
        "on the specific integration and action type.",
    )
