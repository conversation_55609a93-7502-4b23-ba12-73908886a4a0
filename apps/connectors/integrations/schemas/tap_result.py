from typing import Generic, Optional, TypeVar

from pydantic import BaseModel

ResultT = TypeVar("ResultT")


class ErrorDetail(BaseModel):
    message: Optional[str] = None


class TAPResult(BaseModel, Generic[ResultT]):
    """
    Represents a generic result structure for Portal TAPs (Threat Analysis Plugins).

    Attributes:
        result (Optional[ResultT]): The result of the action, if available.
        error (Optional[ErrorDetail]): The error details, if the action failed.
    """

    result: Optional[ResultT] = None
    error: Optional[ErrorDetail] = None
