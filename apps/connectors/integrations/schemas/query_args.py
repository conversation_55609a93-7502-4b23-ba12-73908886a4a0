from typing import Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas import (
    IntegrationIdentifierArgs,
    MailMessageIdentifier,
    UserIdentifier,
)


class TimeRangeArgs(BaseModel):
    start_time: str = Field(
        title="Start Time",
        description="The earliest record to fetch.",
    )

    end_time: Optional[str] = Field(
        title="End Time",
        description="The latest record to fetch.",
        default=None,
    )


class QueryArgs(IntegrationIdentifierArgs):
    time_range: Optional[TimeRangeArgs] = Field(
        title="Time Range",
        description="The time range to fetch activity for.",
        default=None,
    )


class UserQueryArgs(QueryArgs):
    identifier: UserIdentifier = Field(
        title="User Identifier",
        description="The user identifier to fetch activity for.",
    )


class UrlQueryArgs(QueryArgs):
    url: str = Field(
        title="URL",
        description="The URL to fetch activity for.",
    )


class MailMessageQueryArgs(QueryArgs):
    identifier: MailMessageIdentifier = Field(
        title="Message ID",
        description="The identifier of the email message to fetch activity for.",
    )
