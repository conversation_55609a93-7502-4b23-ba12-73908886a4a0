import json
from unittest import mock

import responses

# from apps.connectors.health_checks.component import (
#     Health<PERSON><PERSON>ckComponent,
#     HealthCheckRequirement,
#     RequirementStatus,
#     ValidationStatus,
# )
from apps.connectors.health_checks.components.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
)
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.schemas.ocsf import ReputationScore
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.actions import (
    event_sync,
)
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.api import (
    FortianalyzerV1Api,
)
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.bookmarks import (
    FortianalyzerSyncBookmarks,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


def load_test_xml_data(file_name):
    path = "apps/connectors/tests/integrations/data/fortianalyzer"
    with open(f"{path}/{file_name}", "r") as file:
        json_string = file.read()
        return json.loads(json_string)


def setup_basic_responses(fail=False):
    if fail:
        responses.add(
            responses.POST,
            "https://test_host/jsonrpc",
            json={},
            status=400,
        )
    else:
        responses.add(
            responses.POST,
            "https://test_host/jsonrpc",
            json={
                "id": "1",
                "jsonrpc": "2.0",
                "method": "add",
                "result": {"tid": 123456789},
            },
            status=200,
        )


def setup_responses():
    responses.add(
        responses.POST,
        "https://test_host/jsonrpc",
        match=[
            responses.matchers.json_params_matcher(
                {
                    "id": "1",
                    "jsonrpc": "2.0",
                    "method": "add",
                    "params": mock.ANY,
                }
            )
        ],
        json={
            "id": "1",
            "jsonrpc": "2.0",
            "method": "add",
            "result": {"tid": 123456789},
        },
        status=200,
    )

    responses.add(
        responses.POST,
        "https://test_host/jsonrpc",
        match=[
            responses.matchers.json_params_matcher(
                {
                    "id": "2",
                    "jsonrpc": "2.0",
                    "method": "get",
                    "params": [
                        {
                            "apiver": 3,
                            "url": mock.ANY,
                            "limit": mock.ANY,
                            "offset": 0,
                        }
                    ],
                }
            )
        ],
        json=load_test_xml_data("virus_logs.json"),
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_host/jsonrpc",
        match=[
            responses.matchers.json_params_matcher(
                {
                    "id": "2",
                    "jsonrpc": "2.0",
                    "method": "get",
                    "params": [
                        {
                            "apiver": 3,
                            "url": mock.ANY,
                            "limit": mock.ANY,
                            "offset": 4,
                        }
                    ],
                }
            )
        ],
        json={"result": {"data": [], "status": {"message": "succeeded"}}},
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_host/jsonrpc",
        match=[
            responses.matchers.json_params_matcher(
                {
                    "id": "3",
                    "jsonrpc": "2.0",
                    "method": "delete",
                    "params": mock.ANY,
                }
            )
        ],
        json={
            "id": "3",
            "jsonrpc": "2.0",
            "method": "delete",
            "result": {"tid": 123456789},
        },
        status=200,
    )


class FortianalyzerV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.integration = ConnectorFactory.get_integration(
            technology_id="fortianalyzer",
            version_id="v1",
        )

    @responses.activate
    def test_add_task(self):
        setup_responses()
        api: FortianalyzerV1Api = self.integration.get_api()
        test_date = "2023-01-01T00:00:00Z"
        response = api.add_task("virus", "root", test_date, test_date)
        self.assertEqual(
            response,
            {
                "id": "1",
                "jsonrpc": "2.0",
                "method": "add",
                "result": {"tid": 123456789},
            },
        )

    @responses.activate
    def test_search_by_taskid(self):
        setup_responses()
        api: FortianalyzerV1Api = self.integration.get_api()
        response = api.search_by_taskid(task_id=123456789, adom="root")
        self.assertEqual(response, load_test_xml_data("virus_logs.json"))

    @responses.activate
    def test_delete_task(self):
        setup_responses()
        api: FortianalyzerV1Api = self.integration.get_api()
        response = api.delete_task(task_id=123456789, adom="root")
        self.assertEqual(
            response,
            {
                "id": "3",
                "jsonrpc": "2.0",
                "method": "delete",
                "result": {"tid": 123456789},
            },
        )


class FortianalyzerV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        self.integration = ConnectorFactory.get_integration(
            technology_id="fortianalyzer",
            version_id="v1",
        )

    def test_bookmarks(self):
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = FortianalyzerSyncBookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("time_range_start", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        setup_responses()
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 18)
        self.assertEqual(
            result[0],
            {
                "raw_event": mock.ANY,
                "event_timestamp": "2025-05-22T15:23:36",
                "ioc": {
                    "external_id": "0212008448",
                    "external_name": "Type - utm SubType - virus Message ID - 08448",
                    "has_ioc_definition": False,
                    "mitre_techniques": None,
                },
                "ocsf": {
                    "action": "Allowed",
                    "action_id": 1,
                    "activity_id": 99,
                    "activity_name": "(0, 'Unknown')",
                    "actor": {
                        "session": {"uid": "1131176239"},
                        "user": {"name": "user2"},
                    },
                    "app_name": None,
                    "category_name": "Network Activity",
                    "category_uid": 4,
                    "class_name": "Network Activity",
                    "class_uid": 4001,
                    "connection_info": {
                        "direction": "Inbound",
                        "direction_id": 1,
                        "protocol_num": 6,
                    },
                    "device": {"os": None, "type": None, "uid": "FGTAWSSCQIDSTP7C"},
                    "disposition": "Other",
                    "disposition_id": 99,
                    "dst_endpoint": {
                        "hostname": None,
                        "interface_name": None,
                        "ip": "*************",
                        "isp": None,
                        "location": {
                            "city": None,
                            "country": "Reserved",
                            "region": None,
                        },
                        "mac": None,
                        "name": None,
                        "os": None,
                        "owner": {"name": None},
                        "port": 80,
                        "type": None,
                        "uid": None,
                    },
                    "message": "Type - utm SubType - virus Message ID - 08448",
                    "metadata": {
                        "correlation_uid": "0212008448",
                        "event_code": "virus",
                        "product": {"name": "", "uid": "", "version": "None"},
                        "profiles": [],
                        "uid": "0212008448",
                        "version": "1.5.0-dev",
                    },
                    "osint": [],
                    "policy": {"name": None, "uid": "1"},
                    "severity": "Low",
                    "severity_id": 2,
                    "src_endpoint": {
                        "interface_name": "port1",
                        "ip": "**************",
                        "isp": None,
                        "location": {
                            "city": None,
                            "country": "United%20States",
                            "region": None,
                        },
                        "mac": None,
                        "name": None,
                        "owner": {"name": None},
                        "port": 41752,
                        "type": None,
                        "uid": None,
                    },
                    "status": None,
                    "status_code": None,
                    "time": 1747927416000,
                    "time_dt": "2025-05-22T15:23:36Z",
                    "traffic": {"packets": None},
                    "type_name": "Network Activity: (0, 'Unknown')",
                    "type_uid": 400199,
                },
            },
        )


class FortianalyzerV1HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    def setUp(self) -> None:
        self._patch_encryption()

        self.connector = ConnectorFactory(
            technology_id="fortianalyzer",
            enabled_actions=["event_sync"],
        )

        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_connection(self):
        setup_basic_responses()
        components = HealthCheckComponent.get_components(connector=self.connector)

        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read events",
                description="Read events from fortianalyzer",
                value="events_inventory:view",
                required=IntegrationHealthCheckRequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        self.assert_components(
            components,
            [
                permissions_checks_expected,
            ],
        )

    @responses.activate
    def test_components_failed(self):
        setup_basic_responses(fail=True)
        components = HealthCheckComponent.get_components(connector=self.connector)

        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read events",
                description="Read events from fortianalyzer",
                value="events_inventory:view",
                required=IntegrationHealthCheckRequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        self.assert_components(
            components,
            [
                permissions_checks_expected,
            ],
        )


class FortianalyzerV1HelpersTest(BaseTestCase):
    def test_parse_datetime(self):
        self.assertIsNone(event_sync.parse_datetime(""))
        self.assertIsNotNone(event_sync.parse_datetime("2025-01-01T00:00:00Z"))
        # Should raise for invalid
        with self.assertRaises(Exception):
            event_sync.parse_datetime("not-a-date")

    def test_get_iso_date(self):
        dt_str = "2025-06-06T06:43:12+05:30"
        self.assertIsNotNone(event_sync.get_iso_date(dt_str))
        # Should raise for invalid
        with self.assertRaises(Exception):
            event_sync.get_iso_date("not-a-date")

    def test_get_reputation_score(self):
        self.assertEqual(
            event_sync.get_reputation_score("critical"),
            ReputationScore.POSSIBLY_MALICIOUS,
        )
        self.assertEqual(
            event_sync.get_reputation_score("high"), ReputationScore.SUSPICIOUS_RISKY
        )
        self.assertEqual(
            event_sync.get_reputation_score("medium"), ReputationScore.MAY_NOT_BE_SAFE
        )
        self.assertEqual(
            event_sync.get_reputation_score("low"), ReputationScore.PROBABLY_SAFE
        )
        self.assertEqual(
            event_sync.get_reputation_score("unknown"), ReputationScore.UNKNOWN
        )

    def test_get_severity(self):
        self.assertEqual(event_sync.get_severity("critical"), ocsf.Severity.CRITICAL)
        self.assertEqual(event_sync.get_severity("high"), ocsf.Severity.HIGH)
        self.assertEqual(event_sync.get_severity("medium"), ocsf.Severity.MEDIUM)
        self.assertEqual(event_sync.get_severity("low"), ocsf.Severity.LOW)
        self.assertEqual(event_sync.get_severity("elevated"), ocsf.Severity.OTHER)
        self.assertEqual(event_sync.get_severity("unknown"), ocsf.Severity.UNKNOWN)

    def test_get_direction(self):
        self.assertEqual(
            event_sync.get_direction("incoming"), ocsf.NetworkDirection.INBOUND
        )
        self.assertEqual(
            event_sync.get_direction("outbound"), ocsf.NetworkDirection.OUTBOUND
        )
        self.assertEqual(
            event_sync.get_direction("Unknown"), ocsf.NetworkDirection.UNKNOWN
        )
        self.assertEqual(event_sync.get_direction(""), ocsf.NetworkDirection.UNKNOWN)

    def test_get_action_desposition(self):
        # anomaly
        self.assertEqual(
            event_sync.get_action_disposition("detected", "anomaly"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.DETECTED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("dropped", "anomaly"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("reset", "anomaly"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("clear_session", "anomaly"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER),
        )

        # app-ctrl
        self.assertEqual(
            event_sync.get_action_disposition("pass", "app-ctrl"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("block", "app-ctrl"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("reject", "app-ctrl"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.QUARANTINED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("reset", "app-ctrl"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )

        # casb
        self.assertEqual(
            event_sync.get_action_disposition("monitor", "casb"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("block", "casb"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("bypass", "casb"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )

        # DLP
        self.assertEqual(
            event_sync.get_action_disposition("log-only", "dlp"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("block", "dlp"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("exempt", "dlp"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("ban", "dlp"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("ban-sender", "dlp"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("quarantine-ip", "dlp"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("quarantine-intreface", "dlp"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )

        # event
        self.assertEqual(
            event_sync.get_action_disposition("add", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("edit", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("delete", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )

        self.assertEqual(
            event_sync.get_action_disposition("config-change", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("admin-login", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )

        self.assertEqual(
            event_sync.get_action_disposition("login", "event"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("logout", "event"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("system-reboot", "event"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("admin-logout", "event"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("blocked", "file-filter"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("passthrough", "file-filter"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("blocked", "ssh"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("passthrough", "ssh"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )

        # DNS
        self.assertEqual(
            event_sync.get_action_disposition("allow", "dns"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("monitor", "dns"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("block", "dns"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )

        # Email Filter
        self.assertEqual(
            event_sync.get_action_disposition("blocked", "emailfilter"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("detected", "emailfilter"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("exempted", "emailfilter"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )

        # ICAP
        self.assertEqual(
            event_sync.get_action_disposition("forward", "icap"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("bypass", "icap"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.ERROR),
        )
        self.assertEqual(
            event_sync.get_action_disposition("detected", "icap"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("error", "icap"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.ERROR),
        )

        # IPS
        self.assertEqual(
            event_sync.get_action_disposition("dropped", "ips"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED),
        )

        self.assertEqual(
            event_sync.get_action_disposition("reset", "ips"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("reset_client", "ips"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("reset_server", "ips"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("clear_session", "ips"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("pass_session", "ips"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("drop_session", "ips"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED),
        )

        # SSL
        self.assertEqual(
            event_sync.get_action_disposition("blocked", "ssl"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("exempt", "ssl"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER),
        )

        self.assertEqual(
            event_sync.get_action_disposition("deny", "traffic"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("accept", "traffic"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("start", "traffic"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("dns", "traffic"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ERROR),
        )
        self.assertEqual(
            event_sync.get_action_disposition("ip-conn", "traffic"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ERROR),
        )
        self.assertEqual(
            event_sync.get_action_disposition("close", "traffic"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("timeout", "traffic"),
            (ocsf.ControlAction.OBSERVED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("client-rst", "traffic"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )
        self.assertEqual(
            event_sync.get_action_disposition("server-rst", "traffic"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.RESET),
        )

        self.assertEqual(
            event_sync.get_action_disposition("blocked", "virtual-patch"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("patched", "virtual-patch"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.CORRECTED),
        )

        self.assertEqual(
            event_sync.get_action_disposition("blocked", "virus"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("passthrough", "virus"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.OTHER),
        )
        self.assertEqual(
            event_sync.get_action_disposition("monitored", "virus"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.LOGGED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("analytics", "virus"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.UNKNOWN),
        )

        self.assertEqual(
            event_sync.get_action_disposition("permit", "voip"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("blocked", "voip"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("dropped", "voip"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.DROPPED),
        )

        self.assertEqual(
            event_sync.get_action_disposition("pass", "waf"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("block", "waf"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("monitor", "waf"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.DETECTED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("erase", "waf"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.CORRECTED),
        )

        self.assertEqual(
            event_sync.get_action_disposition("blocked", "webfilter"),
            (ocsf.ControlAction.DENIED, ocsf.Disposition.BLOCKED),
        )
        self.assertEqual(
            event_sync.get_action_disposition("passthrough", "webfilter"),
            (ocsf.ControlAction.ALLOWED, ocsf.Disposition.ALLOWED),
        )

        # Unknown case
        self.assertEqual(
            event_sync.get_action_disposition("nonsense", "invalid"),
            (ocsf.ControlAction.UNKNOWN, ocsf.Disposition.UNKNOWN),
        )

    def test_osint_with_attack(self):
        event = {
            "attack": "SQL Injection",
            "attackId": "ATT-001",
            "ref": "http://threat.example.com",
            "craction": "threatintel",
            "crlevel": "critical",
            "crscore": 9,
        }
        osint = event_sync.get_osint(event)
        self.assertEqual(len(osint), 1)
        self.assertEqual(osint[0].value, "SQL Injection")
        self.assertEqual(osint[0].uid, "ATT-001")
        self.assertEqual(osint[0].src_url, "http://threat.example.com")

    def test_osint_with_email(self):
        event = {
            "from": "<EMAIL>",
            "to": ["<EMAIL>"],
            "cc": ["<EMAIL>"],
            "sender": "<EMAIL>",
            "subject": "Test Subject",
            "recipient": "<EMAIL>",
            "craction": "mailintel",
            "crlevel": "critical",
            "crscore": 5,
        }
        osint = event_sync.get_osint(event)
        self.assertEqual(len(osint), 1)
        self.assertEqual(osint[0].type, ocsf.OSINTIndicatorType.EMAIL.name)
        self.assertEqual(osint[0].email.subject, "Test Subject")

    def test_osint_with_file(self):
        event = {
            "filename": "malware.exe",
            "file": "/tmp/malware.exe",
            "filesize": 2048,
            "filetype": "application/x-executable",
            "filehash": "abcd1234",
        }
        osint = event_sync.get_osint(event)
        self.assertEqual(len(osint), 1)
        self.assertEqual(osint[0].type, ocsf.OSINTIndicatorType.FILE.name)
        self.assertEqual(osint[0].file.name, "malware.exe")
        self.assertEqual(osint[0].file.hashes[0].value, "abcd1234")

    def test_osint_with_infected_file(self):
        event = {
            "infectedfilename": "virus.docx",
            "infectedfilesize": 5120,
            "infectedfiletype": "application/msword",
            "infectedfileurl": "http://malicious.example.com",
            "url": "http://malicious.example.com",
        }
        osint = event_sync.get_osint(event)
        self.assertEqual(len(osint), 1)
        self.assertEqual(osint[0].file.name, "virus.docx")
        self.assertEqual(osint[0].file.url, "http://malicious.example.com")
        self.assertEqual(osint[0].value, "http://malicious.example.com")

    def test_osint_empty_event(self):
        osint = event_sync.get_osint({})
        self.assertEqual(osint, [])
