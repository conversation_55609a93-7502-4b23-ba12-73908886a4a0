{"type": "messagesDelivered", "GUID": "c26dbea0-80d5-463b-b93c-4e8b708219ce", "QID": "r2FNwRHF004109", "ccAddresses": ["<EMAIL>"], "clusterId": "pharmtech_hosted", "completelyRewritten": "true", "fromAddress": "<EMAIL>", "headerCC": "\"<PERSON>\" <<EMAIL>>", "headerFrom": "\"<PERSON><PERSON>\" <<EMAIL>>", "headerReplyTo": null, "headerTo": "\"Clark Kent\" <<EMAIL>>; \"Diana Prince\" <<EMAIL>>", "impostorScore": 0, "malwareScore": 100, "messageID": "<EMAIL>", "messageParts": [{"contentType": "text/plain", "disposition": "inline", "filename": "text.txt", "md5": "008c5926ca861023c1d2a36653fd88e2", "oContentType": "text/plain", "sandboxStatus": "unsupported", "sha256": "85738f8f9a7f1b04b5329c590ebcb9e425925c6d0984089c43a022de4f19c281"}, {"contentType": "application/pdf", "disposition": "attached", "filename": "Invoice for Pharmtech.pdf", "md5": "5873c7d37608e0d49bcaa6f32b6c731f", "oContentType": "application/pdf", "sandboxStatus": "threat", "sha256": "2fab740f143fc1aa4c1cd0146d334c5593b1428f6d062b2c406e5efe8abe95ca"}], "messageTime": "2016-06-24T21:18:38.000Z", "modulesRun": ["pdr", "sandbox", "spam", "urldefense"], "phishScore": 46, "policyRoutes": ["default_inbound", "executives"], "quarantineFolder": "Attachment Defense", "quarantineRule": "module.sandbox.threat", "recipient": ["<EMAIL>", "<EMAIL>"], "xmailer": "<EMAIL>", "replyToAddress": null, "sender": "<EMAIL>", "senderIP": "***********", "spamScore": 4, "subject": "Please find a totally safe invoice attached.", "threatsInfoMap": [{"campaignId": "46e01b8a-c899-404d-bcd9-189bb393d1a7", "classification": "MALWARE", "threat": "bad_attachment", "threatID": "2fab740f143fc1aa4c1cd0146d334c5593b1428f6d062b2c406e5efe8abe95ca", "threatStatus": "active", "threatTime": "2016-06-24T21:18:38.000Z", "threatType": "ATTACHMENT", "threatUrl": "https://tap-dashboard-staging.lab.ppops.net/73aa0499-dfc8-75eb-1de8-a471b24a2e75/threat/email/d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "actors": [{"id": "000090ac-71c4-4060-9154-c590664b6739", "name": "dash_actor_05cbc5", "type": "ACTOR"}]}, {"threatID": "d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatStatus": "active", "classification": "malware", "detectionType": "NONE", "threatUrl": "https://tap-dashboard-staging.lab.ppops.net/73aa0499-dfc8-75eb-1de8-a471b24a2e75/threat/email/d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatTime": "2024-11-20T12:01:07.000Z", "threat": "http://rohan.org/willis", "campaignID": null, "actors": [{"id": "000090ac-71c4-4060-9154-c590664b6739", "name": "dash_actor_05cbc5", "type": "ACTOR"}], "threatType": "url"}, {"threatID": "d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatStatus": "active", "classification": "malware", "detectionType": "NONE", "threatUrl": "https://tap-dashboard-staging.lab.ppops.net/73aa0499-dfc8-75eb-1de8-a471b24a2e75/threat/email/d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatTime": "2024-11-20T12:01:07.000Z", "threat": "http://rohan.org/willis", "campaignID": null, "actors": [{"id": "000090ac-71c4-4060-9154-c590664b6739", "name": "dash_actor_05cbc5", "type": "ACTOR"}], "threatType": "message"}, {"threatID": "d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatStatus": "active", "classification": "malware", "detectionType": "NONE", "threatUrl": "https://tap-dashboard-staging.lab.ppops.net/73aa0499-dfc8-75eb-1de8-a471b24a2e75/threat/email/d57d32f7ad46db1c233eb586da7fa39bafba67ec0f8d1a4eae03b3df65e2c60e", "threatTime": "2024-11-20T12:01:07.000Z", "threat": "http://rohan.org/willis", "campaignID": null, "actors": [{"id": "000090ac-71c4-4060-9154-c590664b6739", "name": "dash_actor_05cbc5", "type": "ACTOR"}], "threatType": "<PERSON><PERSON><PERSON>"}]}