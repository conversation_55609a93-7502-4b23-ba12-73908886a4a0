[{"actor": {"id": "00uttidj01jqL21aM1d6", "type": "User", "alternateId": "<EMAIL>", "displayName": "<PERSON>", "detailEntry": null}, "client": {"userAgent": {"rawUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "os": "Mac OS X", "browser": "CHROME"}, "zone": null, "device": "Computer", "id": null, "ipAddress": "********", "geographicalContext": {"city": "New York", "state": "New York", "country": "United States", "postalCode": "10013", "geolocation": {"lat": 40.3157, "lon": -74.01}}}, "device": {"id": "guofdhyjex1feOgbN1d9", "name": "<PERSON>15,6", "os_platform": "OSX", "os_version": "14.6.0", "managed": false, "registered": true, "device_integrator": null, "disk_encryption_type": "ALL_INTERNAL_VOLUMES", "screen_lock_type": "BIOMETRIC", "jailbreak": null, "secure_hardware_present": true}, "authenticationContext": {"authenticationProvider": null, "credentialProvider": null, "credentialType": null, "issuer": null, "interface": null, "authenticationStep": 0, "rootSessionId": "idxBager62CSveUkTxvgRtonA", "externalSessionId": "idxBager62CSveUkTxvgRtonA"}, "displayMessage": "User login to Okta", "eventType": "user.session.start", "outcome": {"result": "SUCCESS", "reason": null}, "published": "2024-08-13T15:58:20.353Z", "securityContext": {"asNumber": 394089, "asOrg": "ASN 0000", "isp": "google", "domain": null, "isProxy": false}, "severity": "INFO", "debugContext": {"debugData": {"requestId": "ab609228fe84ce59cdcbfa690bcce016", "requestUri": "/idp/idx/authenticators/poll", "url": "/idp/idx/authenticators/poll"}}, "legacyEventType": "core.user_auth.login_success", "transaction": {"type": "WEB", "id": "ab609228fe84ce59cdcbfa690bgce016", "detail": null}, "uuid": "dc9fd3c0-598c-11ef-8478-2b7584bf8d5a", "version": "0", "request": {"ipChain": [{"ip": "********", "geographicalContext": {"city": "New York", "state": "New York", "country": "United States", "postalCode": "10013", "geolocation": {"lat": 40.3157, "lon": -74.01}}, "version": "V4", "source": null}]}, "target": [{"id": "pfdfdhyjf0HMbkP2e1d7", "type": "AuthenticatorEnrollment", "alternateId": "unknown", "displayName": "Okta Verify", "detailEntry": null}, {"id": "0oatxlef9sQvvqInq5d6", "type": "AppInstance", "alternateId": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "detailEntry": null}]}]