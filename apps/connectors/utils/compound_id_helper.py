from typing import Sequence, Tuple

###############################################################################
# Public API                                                                  #
###############################################################################


class CompoundIdError(ValueError):
    """Raised when a compound-ID cannot be combined or split."""


class CompoundIdHelper:
    """
    Self-describing compound ID utilities.

    On-wire format
    --------------
    "@<d_idx>@<part1><delimiter><part2>"

    * `d_idx`  – zero-based index of the delimiter in `DEFAULT_DELIMITERS`
    * `delimiter` – the chosen delimiter character
    * `part1`, `part2` – original strings supplied by the caller
    """

    DEFAULT_DELIMITERS: Tuple[str, ...] = (":", "|", "^", "~", "#", "$", "%", "&")
    _PREFIX_SENTINEL = "@"

    # --------------------------------------------------------------------- #
    # API methods                                                           #
    # --------------------------------------------------------------------- #

    @classmethod
    def combine(
        cls,
        part1: str,
        part2: str,
        *,
        delimiters: Sequence[str] | None = None,
    ) -> str:
        """
        Join two strings into a reversible compound ID.

        Parameters
        ----------
        part1, part2
            Strings that must not contain the chosen delimiter.
        delimiters
            Optional custom list of delimiters (preference order).

        Returns
        -------
        str
            Encoded compound ID.

        Raises
        ------
        CompoundIdError
            If no safe delimiter is available.
        """
        delimiter_list: Sequence[str] = delimiters or cls.DEFAULT_DELIMITERS
        delim, idx = cls._select_safe_delimiter(part1, part2, delimiter_list)

        prefix = cls._encode_prefix(idx)
        return f"{prefix}{part1}{delim}{part2}"

    @classmethod
    def split(
        cls,
        compound_id: str,
        *,
        delimiters: Sequence[str] | None = None,
    ) -> Tuple[str, str]:
        """
        Reverse `combine`, returning the original two parts.

        Raises
        ------
        CompoundIdError
            If the compound ID is malformed or the delimiter index is invalid.
        """
        delimiter_list: Sequence[str] = delimiters or cls.DEFAULT_DELIMITERS
        idx, body = cls._decode_prefix(compound_id)

        try:
            delimiter = delimiter_list[idx]
        except IndexError:  # bad delimiter index
            raise CompoundIdError(
                f"Delimiter index {idx} exceeds configured delimiter list"
            ) from None

        try:
            part1, part2 = body.split(delimiter, maxsplit=1)
        except ValueError:
            raise CompoundIdError(
                f"Compound ID body does not contain delimiter '{delimiter}'"
            ) from None

        return part1, part2

    # --------------------------------------------------------------------- #
    # Internal helpers (prefixed with _)                                    #
    # --------------------------------------------------------------------- #

    @classmethod
    def _select_safe_delimiter(
        cls,
        part1: str,
        part2: str,
        delimiters: Sequence[str],
    ) -> Tuple[str, int]:
        """Return the first delimiter not present in either part."""
        for idx, delim in enumerate(delimiters):
            if delim not in part1 and delim not in part2:
                return delim, idx
        raise CompoundIdError("Unable to find a safe delimiter for given strings")

    @classmethod
    def _encode_prefix(cls, idx: int) -> str:
        """Create the "@<idx>@" prefix."""
        return f"{cls._PREFIX_SENTINEL}{idx}{cls._PREFIX_SENTINEL}"

    @classmethod
    def _decode_prefix(cls, compound_id: str) -> Tuple[int, str]:
        """
        Extract delimiter index and return (index, remainder_of_id).

        Expected input: "@<idx>@rest_of_compound_id"
        """
        if not compound_id.startswith(cls._PREFIX_SENTINEL):
            raise CompoundIdError("Compound ID missing leading '@' prefix")

        try:
            second_at = compound_id.index(cls._PREFIX_SENTINEL, 1)
            idx_str = compound_id[1:second_at]
            remainder = compound_id[second_at + 1 :]
            return int(idx_str), remainder
        except (ValueError, IndexError):
            raise CompoundIdError("Compound ID prefix is malformed") from None


compound_id = CompoundIdHelper
