from typing import Protocol, runtime_checkable

from pydantic import BaseModel

from .compound_id_helper import compound_id


@runtime_checkable
class Serializable(Protocol):
    def serialize(self):  # pragma: no cover
        ...


def batchify(iterable, batch_size):
    items = []
    for item in iterable:
        items.append(item)
        if len(items) == batch_size:
            yield items
            items = []
    if items:
        yield items


def split_cs(cs_string):
    """
    Split a comma-separated string into a list of strings.
    """
    if cs_string is None:
        return [""]
    return [s.strip() for s in cs_string.split(",") if s]


def serialize(iterable):
    return [
        item.serialize()
        if isinstance(item, Serializable)
        else item.model_dump(mode="json")
        if isinstance(item, BaseModel)
        else item
        for item in iterable
    ]


class HashableById:
    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.id == other.id

    def __hash__(self):
        return hash((self.__class__, self.id))
