# Generated by Django 4.2.21 on 2025-05-29 16:31

from django.contrib.postgres.operations import AddIndexConcurrently
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("connectors", "0030_remove_connector_config"),
    ]

    atomic = False

    operations = [
        AddIndexConcurrently(
            model_name="activitylog",
            index=models.Index(
                fields=["connector_id"], name="connectors__connect_2ffe44_idx"
            ),
        ),
    ]
