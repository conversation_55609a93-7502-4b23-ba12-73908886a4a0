# Generated by Django 4.2.21 on 2025-06-02 09:51

from django.contrib.postgres.operations import AddIndexConcurrently
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("connectors", "0031_activitylog_connectors__connect_2ffe44_idx"),
    ]

    atomic = False
    operations = [
        migrations.AlterModelOptions(
            name="activitylog",
            options={"ordering": ["-timestamp"]},
        ),
        AddIndexConcurrently(
            model_name="activitylog",
            index=models.Index(
                fields=["connector_id", "timestamp"],
                name="connectors__connect_4c80e7_idx",
            ),
        ),
    ]
