from typing import Annotated

from fastapi import APIRouter, HTTPException, Query
from starlette.status import HTTP_404_NOT_FOUND

from apps.api.deps import SessionDep
from apps.api.v1.schemas.base import ListResponse, MetaPair, labels_to_meta
from apps.api.v1.schemas.connection import ConnectionTemplate
from apps.api.v1.schemas.vendor import (
    Module,
    Vendor,
    VendorMeta,
    VendorTemplateVersion,
    VendorTemplateVersionBase,
)
from apps.connectors.integrations import Template

router = APIRouter(
    prefix="/vendors",
    tags=["vendors"],
)


@router.get("/meta")
def get_vendor_meta(session: SessionDep) -> VendorMeta:
    available_categories = set()
    for template in Template.get_templates():
        if template.category not in available_categories and session.is_available(
            template
        ):
            available_categories.add(template.category)

    meta_categories = [
        MetaPair(id=category.id, name=category.name)
        for category in available_categories
    ]
    meta_modules = labels_to_meta(Module.labels())
    return VendorMeta(category=meta_categories, module=meta_modules)


@router.get("")
def list_vendors(session: SessionDep) -> ListResponse[Vendor]:
    available_vendors = set()
    for template in Template.get_all_templates():
        if template.vendor not in available_vendors and session.is_available(template):
            available_vendors.add(template.vendor)

    return ListResponse.prepare(list(available_vendors))


@router.get("/{vendor_id}/template_versions", response_model_exclude_unset=True)
def list_vendor_versions(
    vendor_id: str,
    session: SessionDep,
    module: Annotated[list[str], Query()] = None,
    category_id: Annotated[list[str], Query()] = None,
) -> ListResponse[VendorTemplateVersion]:
    templates = [
        template
        for template in Template.get_templates(vendor_ids=[vendor_id])
        if session.is_available(template)
    ]
    if not templates:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND)

    template_versions = []
    for template in templates:
        if category_id is not None and template.category.id not in category_id:
            continue
        for version in template.versions.values():
            if module is not None and not version.has_module(module):
                continue

            template_version_base = VendorTemplateVersionBase.model_validate(version)
            vendor_technology_version = VendorTemplateVersion(
                **template_version_base.model_dump(),
                technology_id=template.id,
                technology_name=template.name,
                vulnerability_coverage_available=template.vulnerability_coverage_available,
                endpoint_coverage_available=template.endpoint_coverage_available,
                vendor_id=template.vendor.id,
                vendor_name=template.vendor.name,
                category_id=template.category_id,
                category_name=template.category_name,
            )
            template_versions.append(vendor_technology_version)

    return ListResponse.prepare(template_versions)


@router.get("/{vendor_id}/connection_templates", response_model_exclude_unset=True)
def get_vendor_connection_templates(
    vendor_id: str,
    session: SessionDep,
) -> list[ConnectionTemplate]:
    templates = [
        template
        for template in Template.get_templates(vendor_ids=[vendor_id])
        if session.is_available(template)
    ]
    if not templates:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND)

    connection_templates = {
        version.connection_model()
        for template in templates
        for version in template.versions.values()
    }
    return list(connection_templates)


@router.get(
    "/{vendor_id}/connection_templates/{connection_template_id}",
    response_model_exclude_unset=True,
)
def get_vendor_connection_template(
    vendor_id: str,
    connection_template_id: str,
    session: SessionDep,
) -> ConnectionTemplate:
    templates = [
        template
        for template in Template.get_templates(vendor_ids=[vendor_id])
        if session.is_available(template)
    ]
    if not templates:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND)

    for template in templates:
        for version in template.versions.values():
            if version.connection_model.id == connection_template_id:
                return version.connection_model()

    raise HTTPException(status_code=HTTP_404_NOT_FOUND)
