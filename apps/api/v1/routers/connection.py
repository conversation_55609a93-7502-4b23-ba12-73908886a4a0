import logging
from uuid import UUID

from fastapi import APIRouter, Depends
from fastapi import status as httpstatus
from fastapi.exceptions import HTTPException

from apps.api.deps import SessionDep
from apps.api.v1.crud import connection as crud
from apps.api.v1.schemas.base import ListResponse
from apps.api.v1.schemas.connection import (
    Connection,
    ConnectionCreate,
    ConnectionSummary,
    ConnectionUpdate,
)
from apps.connectors.models import Connector
from apps.connectors.models.connection import Connection as ConnectionModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/connections",
    tags=["connections"],
)


def validate_connection(connection_id: UUID, user_session: SessionDep):
    """Validate that the connection exists."""
    try:
        return crud.get(connection_id, user_session.organization)
    except ConnectionModel.DoesNotExist:
        raise HTTPException(
            status_code=httpstatus.HTTP_404_NOT_FOUND,
            detail="Connection not found",
        )


@router.get("/{connection_id}", response_model=Connection)
def get_connection(
    connection: Connector = Depends(validate_connection),
) -> Connection:
    return connection


@router.get("", response_model=ListResponse[ConnectionSummary])
def list_connections(user_session: SessionDep):
    return ListResponse.prepare(crud.list(user_session.organization))


@router.post("")
def create_connection(
    connection: ConnectionCreate,
    user_session: SessionDep,
) -> Connection:
    return crud.create(connection, user_session.organization)


@router.delete("/{connection_id}")
def delete_connection(
    connection_id: UUID,
    user_session: SessionDep,
) -> Connection:
    return crud.delete(connection_id, user_session.organization)


@router.put("/{connection_id}")
def update_connection(
    connection_id: UUID,
    connection_update: ConnectionUpdate,
    user_session: SessionDep,
) -> Connection:
    return crud.update(connection_id, connection_update, user_session.organization)
