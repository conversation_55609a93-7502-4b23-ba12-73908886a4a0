from enum import StrEnum

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
)

from apps.api.v1.schemas.base import MetaEntry
from apps.api.v1.schemas.technology import ActionMetadata, JSONSchema
from apps.connectors.integrations import IntegrationActionType


class Vendor(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str


class TemplateConfig(JSONSchema):
    pass


class TemplateSettings(JSONSchema):
    pass


class VendorTemplateVersionBase(BaseModel):
    model_config = ConfigDict(from_attributes=True, populate_by_name=True)

    id: str
    name: str
    supported_actions: list[IntegrationActionType] = Field(
        validation_alias="visible_supported_actions"
    )
    connection_template_id: str | None = None
    connection_config: TemplateConfig | None = Field(
        validation_alias="connection_config_model",
        default=None,
    )
    config: TemplateConfig = Field(validation_alias="config_model")
    settings: TemplateSettings = Field(validation_alias="settings_model")
    actions: dict[IntegrationActionType, ActionMetadata] = Field(
        validation_alias="actions_metadata"
    )
    modules: list[str]

    @field_validator("config", mode="before")
    def get_config(cls, config):
        if issubclass(config, BaseModel):
            return config.model_json_schema()
        else:
            return config

    @field_validator("settings", mode="before")
    def get_settings(cls, settings):
        if issubclass(settings, BaseModel):
            return settings.model_json_schema()
        else:
            return settings

    @field_validator("actions", mode="before")
    def get_actions_metadata(cls, actions):
        return {
            action: metadata
            for action, metadata in actions.items()
            if action in IntegrationActionType._value2member_map_
        }

    @field_validator("supported_actions", mode="before")
    def get_supported_actions(cls, supported_actions):
        return [
            action
            for action in supported_actions
            if action in IntegrationActionType._value2member_map_
        ]


class VendorTemplateVersion(VendorTemplateVersionBase):
    technology_id: str
    technology_name: str
    vulnerability_coverage_available: bool
    endpoint_coverage_available: bool
    category_id: str
    category_name: str
    vendor_id: str
    vendor_name: str


class Module(StrEnum):
    mdr = "mdr:*"
    vulnerabilities = "vulnerability:*"
    assets = "assets:*"

    @classmethod
    def labels(cls):
        return {
            cls.mdr: "MDR",
            cls.vulnerabilities: "Vulnerabilities",
            cls.assets: "Assets",
        }


class VendorMeta(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    category: MetaEntry
    module: MetaEntry
