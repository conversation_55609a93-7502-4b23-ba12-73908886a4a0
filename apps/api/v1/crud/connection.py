from typing import Any

from criticalstart.auth.v2.models import Organization
from django.db.models import QuerySet

from apps.api.v1.schemas.connection import ConnectionCreate, ConnectionUpdate
from apps.connectors.models import Connection

from .base import PublicCRUDBase


class PublicCRUDConnection(
    PublicCRUDBase[Connection, ConnectionCreate, ConnectionUpdate]
):
    def list(self, org: Organization) -> QuerySet[Connection]:
        return super().list(org).filter(is_deleted=False)

    def create(
        self,
        obj_in: ConnectionCreate,
        org: Organization,
    ) -> Connection:
        obj_in.config_model.validate_database(org)
        return super().create(obj_in, org)

    def delete(
        self,
        id: any,
        org: Organization,
    ) -> Connection:
        obj = self.get(id, org)
        obj.is_deleted = True
        obj.save(update_fields={"is_deleted", "updated_at"})
        return obj

    def update(
        self,
        id: Any,
        obj_in: ConnectionUpdate,
        org: Organization,
    ) -> Connection:
        obj_in.config_model.validate_database(org)
        return super().update(id, obj_in, org)

    @staticmethod
    def auto_update_fields():
        return {"updated_at"}


connection = PublicCRUDConnection(Connection)
