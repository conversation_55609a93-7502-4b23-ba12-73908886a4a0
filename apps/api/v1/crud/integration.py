from typing import Any

from criticalstart.auth.v2.models import Organization

from apps.api.v1.schemas.integration import (
    CoverageMode,
    IntegrationCreate,
    IntegrationUpdate,
)
from apps.connectors.models import Connection, Connector

from .base import PublicCRUDBase


class PublicCRUDConnector(
    PublicCRUDBase[Connector, IntegrationCreate, IntegrationUpdate]
):
    def list(
        self,
        org: Organization,
        technology_ids: list[str] = None,
        enabled_actions: list[str] = None,
    ) -> list[Connector]:
        params = {}
        if technology_ids is not None:
            params["technology_id__in"] = technology_ids

        result = super().list(org, params).filter(is_deleted=False)

        if enabled_actions:
            result = [
                r
                for r in result
                if set(enabled_actions).intersection(r.invokable_actions)
            ]

        return result

    def create(
        self,
        obj_in: IntegrationCreate,
        org: Organization,
    ) -> Connector:
        if obj_in.connection_id:
            connection = Connection.objects.get(
                id=obj_in.connection_id, organization_id=org.id
            )
            # FIXME: migration_id=connection_config
            obj_in.config = connection.config

        obj_in.validate_config()
        obj_in.config_model.validate_database(org)

        return super().create(obj_in, org)

    def delete(
        self,
        id: any,
        org: Organization,
    ) -> Connector:
        obj = self.get(id, org)
        obj.is_deleted = True
        obj.save(update_fields={"is_deleted", "updated_at"})
        return obj

    def update(
        self,
        id: Any,
        obj_in: IntegrationUpdate,
        org: Organization,
    ) -> Connector:
        if obj_in.connection_id:
            connection = Connection.objects.get(
                id=obj_in.connection_id, organization_id=org.id
            )
            # FIXME: migration_id=connection_config
            obj_in.config = connection.config

        obj_in.validate_config()
        obj_in.config_model.validate_database(org)

        existing_integration = self.get(id, org)

        if existing_integration.config_managed_by_portal:
            obj_in.config = existing_integration.config

        return super().update(id, obj_in, org)

    @staticmethod
    def auto_update_fields():
        return {"updated_at"}

    def update_vulnerability_coverage_mode(
        self,
        id: Any,
        mode: CoverageMode,
        org: Organization,
    ) -> Connector:
        obj = self.get(id, org)
        obj.vulnerability_coverage_mode = mode
        update_fields = {"vulnerability_coverage_mode"} | self.auto_update_fields()
        obj.save(update_fields=update_fields)
        return obj

    def update_endpoint_coverage_mode(
        self,
        id: Any,
        mode: CoverageMode,
        org: Organization,
    ) -> Connector:
        obj = self.get(id, org)
        obj.endpoint_coverage_mode = mode
        update_fields = {"endpoint_coverage_mode"} | self.auto_update_fields()
        obj.save(update_fields=update_fields)
        return obj


connector = PublicCRUDConnector(Connector)
