from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.vendors.fortinet.fortianalyzer.v1.settings import (
    FortianalyzerV1Settings,
)
from apps.demo.integrations.template import DemoConnectionTemplate, DemoTemplateConfig

from .integration import DemoFortianalyzerV1Integration


class DemoFortianalyzerV1TemplateVersion(TemplateVersion):
    integration = DemoFortianalyzerV1Integration
    id = "v1"
    name = "v1"
    supported_actions = []
    settings_model = FortianalyzerV1Settings
    config_model = DemoTemplateConfig
    connection_model = DemoConnectionTemplate
