from apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security import (
    MimecastAdvancedEmailSecurityTemplate,
)
from apps.demo.integrations.template import DemoTemplate

from .v1 import DemoMimecastAdvancedEmailSecurityV1TemplateVersion


class DemoMimecastAdvancedEmailSecurityTemplate(DemoTemplate):
    _replaces_template = MimecastAdvancedEmailSecurityTemplate
    versions = {
        DemoMimecastAdvancedEmailSecurityV1TemplateVersion.id: DemoMimecastAdvancedEmailSecurityV1TemplateVersion(),
    }
