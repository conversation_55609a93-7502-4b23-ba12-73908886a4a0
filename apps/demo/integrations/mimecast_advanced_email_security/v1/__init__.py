from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1 import (
    MimecastAdvancedEmailSecurityV1Settings,
)
from apps.demo.integrations.template import DemoConnectionTemplate, DemoTemplateConfig

from .integration import DemoMimecastAdvancedEmailSecurityV1Integration


class DemoMimecastAdvancedEmailSecurityV1TemplateVersion(TemplateVersion):
    integration = DemoMimecastAdvancedEmailSecurityV1Integration
    id = "v1"
    name = "v1"
    supported_actions = []
    settings_model = MimecastAdvancedEmailSecurityV1Settings
    config_model = DemoTemplateConfig
    connection_model = DemoConnectionTemplate
